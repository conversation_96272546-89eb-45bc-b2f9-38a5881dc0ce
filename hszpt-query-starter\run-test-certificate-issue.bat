@echo off
chcp 65001 > nul
echo 正在准备测试环境...

REM 使用Maven下载依赖
call mvn dependency:copy-dependencies -DoutputDirectory=lib

REM 设置类路径
set CLASSPATH=lib\*;target\test-classes

REM 编译测试类
echo 正在编译测试类...
javac -encoding UTF-8 -cp %CLASSPATH% -d target\test-classes src\test\java\com\js\hszpt\test\CertificateIssueTest.java

REM 运行测试
echo 正在运行测试...
java -Dfile.encoding=UTF-8 -cp %CLASSPATH% com.js.hszpt.test.CertificateIssueTest

echo 测试完成。
pause 
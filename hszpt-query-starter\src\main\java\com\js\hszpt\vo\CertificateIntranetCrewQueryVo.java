package com.js.hszpt.vo;

import lombok.Data;
import java.util.Date;

@Data
public class CertificateIntranetCrewQueryVo {
    /**
     * 序号
     */
    private Integer serialNo;

    private String certificateStatus;

    /**
     * 证书数据ID
     */
    private String certificateId;

    /**
     * 证书编号
     */
    private String certificateNumber;

    /**
     * 证书状态
     */
    private String statusFlag;

    /**
     * 持证人姓名
     */
    private String holderName;

    /**
     * 身份证号
     */
    private String holderIdentityNumber;

    /**
     * 出生日期
     */
    private String birthDate;

    /**
     * 证书印刷号
     */
    private String certPrintNo;

    /**
     * 许可证号
     */
    private String permitNumber;

    /**
     * 发证机关
     */
    private String issuingAuthority;

    /**
     * 培训机构
     */
    private String trainingOrg;

    /**
     * 外派机构
     */
    private String foreignOrg;

    /**
     * 主管签发机构
     */
    private String mainSignOrg;

    /**
     * 发证日期
     */
    private Date issuingDate;

    /**
     * 生效日期
     */
    private Date validFromDate;

    /**
     * 截止日期
     */
    private Date validToDate;

    /**
     * 船舗名称
     */
    private String shipName;

    /**
     * 培训项目名称（多个项目以#分隔）
     */
    private String projectNames;

    /**
     * 证书数据ID
     */
    private String dataId;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 证书类型代码
     */
    private String certificateTypeCode;

    /**
     * 证书类型名称
     */
    private String certificateTypeName;

    /**
     * 证书定义机构代码
     */
    private String certificateDefineAuthorityCode;

    /**
     * 证书定义机构名称
     */
    private String certificateDefineAuthorityName;

    /**
     * 国籍-中文
     */
    private String countryCn;

    /**
     * 国籍-英文
     */
    private String countryEn;

    /**
     * 持证人姓名
     */
    private String certificateHolderName;

    /**
     * 出生日期
     */
    private String birth;

    /**
     * 证书签发日期
     */
    private String certificateIssuedDate;

    /**
     * 证书到期日期
     */
    private String certificateExpiringDate;

    /**
     * 证书颁发机构名称
     */
    private String certificateIssuingAuthorityName;

    /**
     * 职务资格
     */
    private String crewType;

    /**
     * 证书有效期开始日期（英文）
     */
    private String certificateEffectiveDateEn;

    /**
     * 证书有效期截止日期（英文）
     */
    private String certificateExpiringDateEn;

    /**
     * 证书颁发日期（英文）
     */
    private String certificateIssuedDateEn;

    /**
     * 职务(英文)
     */
    private String crewTypeEn;

    /**
     * 适用航线-英文
     */
    private String applivationsEn;

    /**
     * 授权机关（中文）
     */
    private String authAuthorityCn;

    /**
     * 授权机关（英文）
     */
    private String authAuthorityEn;

    /**
     * 审核机构（中文）
     */
    private String evaOrgCn;

    /**
     * 审核机构（英文）
     */
    private String evaOrgEn;

    /**
     * 培训主管姓名（中文）
     */
    private String trainManagerNameCn;

    /**
     * 培训主管姓名（英文）
     */
    private String trainManagerNameEn;

    /**
     * 法定代表人姓名（中文）
     */
    private String representativeCn;

    /**
     * 法定代表人姓名（英文）
     */
    private String representativeEn;

    /**
     * 培训项目（中文）
     */
    private String trainingNamesCn;

    /**
     * 培训项目（英文）
     */
    private String trainingNamesEn;

    /**
     * 培训项目签发日期（中文）
     */
    private String trainingIssueDatesCn;

    /**
     * 培训项目签发日期（英文）
     */
    private String trainingIssueDatesEn;

    /**
     * 培训项目有效期至（中文）
     */
    private String trainingEffectiveDatesCn;

    /**
     * 培训项目有效期至（英文）
     */
    private String trainingEffectiveDatesEn;
}

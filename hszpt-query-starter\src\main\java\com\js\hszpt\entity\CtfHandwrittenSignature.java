package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CtfHandwrittenSignature   
 * @Description:TODO(证照手写签名表)   
 * @author:   System Generation 
 */
@Data

@TableName("CTF_HANDWRITTEN_SIGNATURE")
@ApiModel(value = "证照手写签名表")
public class CtfHandwrittenSignature extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 手写签名记录主键ID
     */
    @TableId(value = "CTF_HAND_SIGN_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "手写签名记录主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
     * 关联CTF_CERTIFICATE表的CERTIFICATE_ID
     */
    @TableField("CERTIFICATE_ID")
    @ApiModelProperty(value = "关联CTF_CERTIFICATE表的CERTIFICATE_ID")
    private String certificateId;

    /**
     * 手写签名的图像数据
     */
    @TableField("SIGNATURE_IMAGE")
    @ApiModelProperty(value = "手写签名的图像数据")
    private byte[] signatureImage;

    /**
     * 图像类型，如PNG、JPEG等
     */
    @TableField("IMAGE_TYPE")
    @ApiModelProperty(value = "图像类型，如PNG、JPEG等")
    private String imageType;

    /**
     * 手写签名的时间
     */
    @TableField(value = "SIGNATURE_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "手写签名的时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signatureTime;

    /**
     * 签名设备的相关信息
     */
    @TableField("DEVICE_INFO")
    @ApiModelProperty(value = "签名设备的相关信息")
    private String deviceInfo;

    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableField("DEL_FLAG")
    @ApiModelProperty(value = "逻辑删除标记(0--正常 1--删除)")
    private String delFlag;

}
package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BizAffairInsurance {

    // 保单ID
    @TableId
    private String guaranteeSlipInfoId;

    // 申请标识
    private String applyId;

    // 船舶编码（电子政务系统定义的船舶唯一编号）
    private String shipCode;

    // 船舶识别号（永久识别中国籍船舶的唯一编码）
    private String shipId;

    // 保单编号
    private String policyNo;

    // 保单开始日期
    private String effStartDate;

    // 保单开始小时
    private String effStartHour;

    // 保单结束日期
    private String deadLine;

    // 保单结束小时
    private String deadLineHour;

    // 时区类型（北京时间或其他时间）
    private String dateType;

    // 保险机构名称
    private String insurCompName;

    // 保险机构地址
    private String insurCompAddr;

    // 保险证书名称
    private String insurCertName;

    // 备注
    private String remark;

    // 保险机构英文名称
    private String insurCompNameEn;

    // 保险机构英文地址
    private String insurCompAddrEn;

    // 保险类别
    private String insurType;

    // 联系人
    private String contactName;

    // 联系电话
    private String contactPhone;

    // 创建人标识（DICT_EG_USER）
    private String creatorId;

    // 创建人所属机构代码
    private String creatorOrgCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 创建日期
    private Date createDate;

    // 修改人标识（DICT_EG_USER）
    private String modifierId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 修改日期
    private Date modifyDate;

    // 源系统代码
    private String sourceCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 记录创建日期
    private Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 记录修改日期
    private Date recModifyDate;

    // 数据归属机构代码（DICT_EG_ORG）
    private String msaOrgCode;
}

package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.constants.Common;
import com.js.hszpt.entity.BizEgCertCheck;
import com.js.hszpt.mapper.BizEgCertCheckMapper;
import org.springframework.stereotype.Service;

@Service
public class BizEgCertCheckService extends ServiceImpl<BizEgCertCheckMapper, BizEgCertCheck> {

    public BizEgCertCheck getCertCheckByApplyId(String windowApplyId) {
        QueryWrapper<BizEgCertCheck> certCheckQueryWrapper = Wrappers.<BizEgCertCheck>query();
        certCheckQueryWrapper.lambda()
                .eq(BizEgCertCheck::getApplyId, windowApplyId)
                .in(BizEgCertCheck::getCheckFlagCode, Common.CheckResult.PASS_CERT_CHECK).last("LIMIT 1");
        return baseMapper.selectOne(certCheckQueryWrapper);
    }

}

package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "证照类型目录详情VO")
public class CertTypeDirectoryDetailVO {
    @ApiModelProperty("证照类型目录信息")
    private CertTypeDirectoryVO certTypeDirectory;

    @ApiModelProperty("审批申请信息")
    private CertApprovalApplyVO certApprovalApply;

    @ApiModelProperty("审核审批信息列表")
    private List<CertTypeApprovalVO> certTypeApprovals;
}

@Data
@ApiModel(value = "证照类型目录VO")
class CertTypeDirectoryVO {
    @ApiModelProperty("证照类型目录ID")
    private String id;

    @ApiModelProperty("证照类型代码")
    private String certificateTypeCode;

    @ApiModelProperty("证照类型名称")
    private String certificateTypeName;

    @ApiModelProperty("创建机构名称")
    private String createOrgName;

    @ApiModelProperty("关联事项名称")
    private String relatedItemName;

    @ApiModelProperty("关联事项代码")
    private String relatedItemCode;

    @ApiModelProperty("持证主体类别")
    private String certificateHolderCategory;

    @ApiModelProperty("持证主体类别名称")
    private String certificateHolderCategoryName;

    @ApiModelProperty("有效期范围")
    private String validityRange;

    @ApiModelProperty("审批状态")
    private String approvalStatus;

    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;

    @ApiModelProperty("下发状态")
    private String issueStatus;

    @ApiModelProperty("下发状态名称")
    private String issueStatusName;

    @ApiModelProperty("下发日期")
    private Date issueDate;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("删除标志")
    private String delFlag;

    @ApiModelProperty("父级ID")
    private String parentId;
}

@Data
@ApiModel(value = "证照类型目录审批申请VO")
class CertApprovalApplyVO {
    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("申请编号，按规则生成")
    private String applyNo;

    @ApiModelProperty("证照类型目录ID")
    private String certTypeDirId;

    @ApiModelProperty("工作流实例ID")
    private String executionId;

    @ApiModelProperty("申请类型：1-新增 2-废止 3-启用")
    private String applyType;

    @ApiModelProperty("申请机构代码")
    private String applyOrgCode;

    @ApiModelProperty("申请机构名称")
    private String applyOrgName;

    @ApiModelProperty("受理机构代码")
    private String acceptOrgCode;

    @ApiModelProperty("受理机构名称")
    private String acceptOrgName;

    @ApiModelProperty("审批机构代码")
    private String approvalOrgCode;

    @ApiModelProperty("审批机构名称")
    private String approvalOrgName;

    @ApiModelProperty("发布机构代码")
    private String publishOrgCode;

    @ApiModelProperty("发布机构名称")
    private String publishOrgName;

    @ApiModelProperty("申请人ID")
    private String applicantId;

    @ApiModelProperty("申请人姓名")
    private String applicantName;

    @ApiModelProperty("申请原因说明")
    private String applyReason;

    @ApiModelProperty("申请时间")
    private Date applyTime;

    @ApiModelProperty("受理时间")
    private Date acceptTime;

    @ApiModelProperty("审批时间")
    private Date approvalTime;

    @ApiModelProperty("发布时间")
    private Date publishTime;

    @ApiModelProperty("当前节点：1-申请 2-受理 3-审核 4-审批")
    private String currentNode;

    @ApiModelProperty("当前处理人ID")
    private String currentHandlerId;

    @ApiModelProperty("当前处理人姓名")
    private String currentHandlerName;

    @ApiModelProperty("状态：1-待审核 2-审核通过 3-审核不通过 4-审核退回 5-撤回 6-待审批 7-审批通过 8-审批不通过 9-审批退回")
    private String status;

    @ApiModelProperty("删除标志：0-正常，1-已删除")
    private String delFlag;
}

@Data
@ApiModel(value = "证照类型目录审核审批VO")
class CertTypeApprovalVO {
    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("证照类型目录ID")
    private String certTypeDirId;

    @ApiModelProperty("证照类型目录审批申请ID")
    private String certApprApplyId;

    @ApiModelProperty("申请人ID")
    private String applicantId;

    @ApiModelProperty("申请人姓名")
    private String applicantName;

    @ApiModelProperty("申请提交时间")
    private Date applyTime;

    @ApiModelProperty("当前审批环节：1-申请、2-审核、3-审批")
    private String currentNode;

    @ApiModelProperty("审批类型：1-提交 2-废止 3-启用")
    private String applyType;

    @ApiModelProperty("审批结果：1-通过 2-不通过 3-退回")
    private String approvalResult;

    @ApiModelProperty("审批意见")
    private String approvalOpinion;

    @ApiModelProperty("审批完成时间")
    private Date completeTime;

    @ApiModelProperty("删除标志：0-正常，1-已删除")
    private String delFlag;
}

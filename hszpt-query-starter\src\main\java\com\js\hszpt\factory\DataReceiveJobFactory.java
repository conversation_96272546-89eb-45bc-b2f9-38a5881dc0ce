package com.js.hszpt.factory;

import org.quartz.spi.TriggerFiredBundle;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.quartz.AdaptableJobFactory;

/**
 * 数据接收定时任务工厂
 */
public class DataReceiveJobFactory extends AdaptableJobFactory implements ApplicationContextAware {

    private transient AutowireCapableBeanFactory beanFactory;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.beanFactory = applicationContext.getAutowireCapableBeanFactory();
    }

    @Override
    protected Object createJobInstance(TriggerFiredBundle bundle) throws Exception {
        // 从Spring容器中获取Job的bean（需要确保Job类被Spring管理）
        Object jobInstance = super.createJobInstance(bundle);
        // 手动注入依赖
        this.beanFactory.autowireBean(jobInstance);
        return jobInstance;
    }
}

package com.js.hszpt.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.vo.Result;
import com.js.hszpt.dto.CrewCertificateQueryDto;
import com.js.hszpt.dto.IntranetCrewQueryDto;
import com.js.hszpt.enmus.CertificateCreateStatus;
import com.js.hszpt.entity.*;
import com.js.hszpt.mapper.crew.CrewCertificateMapper;
import com.js.hszpt.vo.CertificateIntranetCrewQueryVo;
import com.js.hszpt.vo.CrewCertificateQueryVo;
import com.js.hszpt.vo.TrainingProjectVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.ObjectUtil;
import java.text.SimpleDateFormat;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import javax.annotation.PostConstruct;
import java.util.*;
import java.lang.reflect.Field;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("dzzzdwdz")
public class CrewCertificateService extends ServiceImpl<CrewCertificateMapper, CrewCertificateData> {

    @Autowired
    private CertAccessLogService accessLogService;

    @Autowired
    private CertificateConfigService certificateConfigService;

    @Autowired
    private CertTypeDirectoryService certTypeDirectoryService;

    // 证照类型缓存
    private Map<String, CertTypeDirectory> certTypeCodeCache = new HashMap<>();
    private Map<String, CertTypeDirectory> certTypeDirIdCache = new HashMap<>();

    private Map<String, List<VerificationDisplayConfig>> configCache = new HashMap<>();
    private Map<String, CertificateConfig> certificateConfigCache = new HashMap<>();

    @PostConstruct
    public void init() {
        log.info("开始加载证照类型数据到内存...");
        List<CertTypeDirectory> allTypes = certTypeDirectoryService.list();

        // 构建两个缓存Map
        for (CertTypeDirectory type : allTypes) {
            if (StrUtil.isNotBlank(type.getCertificateTypeCode())) {
                certTypeCodeCache.put(type.getCertificateTypeCode(), type);
            }
            if (StrUtil.isNotBlank(type.getId())) {
                certTypeDirIdCache.put(type.getId(), type);
            }
        }

        log.info("证照类型数据加载完成,共加载 {} 条记录", allTypes.size());
    }

    /**
     * 查询船员证书信息
     *
     * @param queryDto 查询条件
     * @param page 分页参数
     * @return 分页查询结果
     */
    public Result<Map<String, Object>> queryCrewCertificates(CrewCertificateQueryDto queryDto, Page page) {
        try {
            log.info("海事一网通办查询船员证书信息，查询条件：{}", queryDto);
            long queryStartTime = System.currentTimeMillis();
            Map<String,Object> resultMap = new HashMap<>();

            //获取有效数量和无效数量
            long validCount = 0L;
            long invalidCount = 0L;
            //保存原始状态
            page.setSize(queryDto.getPageSize());
            page.setCurrent(queryDto.getPageNumber());
            // 对应证照名称反映射为数据库的证照名称
            String certificateName = queryDto.getCertificateName();
            if (StrUtil.equals("海船船员适任证书（内河水域航线签注）", certificateName)) {
                certificateName = "海船船员内河航线行驶资格证明";
            }

            if (StrUtil.equals("海上游艇操作人员适任证书", certificateName)) {
                certificateName = "游艇驾驶证（海上）";
            }

            if (StrUtil.equals("内河游艇操作人员适任证书", certificateName)) {
                certificateName = "游艇驾驶证（内河）";
            }

            if (StrUtil.equals("小型海船船员适任证书", certificateName)) {
                certificateName = "小型海船适任证书";
            }
            queryDto.setCertificateName(certificateName);
            String certificateStatus = queryDto.getCertificateStatus();
            // 海事通APP先查询所有记录 再过滤
            if(StrUtil.equals("3",queryDto.getDisplaySource())) {
                queryDto.setCertificateStatus(null);
            }
            //IPage<CrewCertificateQueryVo> result = baseMapper.selectCrewCertificates(page, queryDto);
            long dbStartTime = System.currentTimeMillis();
            IPage<CrewCertificateQueryVo> result = null;
            if(StrUtil.equals("1",certificateStatus)){
                result = baseMapper.selectCrewCertificates(page, queryDto);
                invalidCount = result.getTotal();
                validCount = 0;
            }else if (StrUtil.equals("2",certificateStatus)){
                result = baseMapper.selectCrewCertificates(page, queryDto);
                validCount = result.getTotal();
                invalidCount = 0;
            }else{
                queryDto.setCertificateStatus(null);
                result = baseMapper.selectCrewCertificates(page, queryDto);
                // 遍历Records列表，根据certificateStatus状态统计数量
                if (result != null && result.getRecords() != null && !result.getRecords().isEmpty()) {
                    for (CrewCertificateQueryVo record : result.getRecords()) {
                        if ("有效".equals(record.getCertificateStatus())) {
                            validCount++;
                        } else if ("无效".equals(record.getCertificateStatus())) {
                            invalidCount++;
                        }
                    }
                } else {
                    validCount = 0;
                    invalidCount = 0;
                }
            }
            log.info("船员列表查询-查询数据库查询耗时：{}ms", System.currentTimeMillis() - dbStartTime);

            // 保存原始 certificateStatus

            // 当 displaySource = 3 时，对相同证照名称的记录按 effectiveDate 和 expiryDate 进行去重
            if (queryDto.getDisplaySource() != null && "3".equals(queryDto.getDisplaySource())) {
                log.info("displaySource = 3，开始对相同证照名称的记录进行去重处理");
                
                // 按证照名称分组
                Map<String, List<CrewCertificateQueryVo>> groupedRecords = result.getRecords().stream()
                    .collect(Collectors.groupingBy(CrewCertificateQueryVo::getCertificateName));
                
                List<CrewCertificateQueryVo> deduplicatedRecords = new ArrayList<>();
                
                for (Map.Entry<String, List<CrewCertificateQueryVo>> entry : groupedRecords.entrySet()) {
                    String groupCertificateName = entry.getKey();
                    List<CrewCertificateQueryVo> records = entry.getValue();
                    
                    if (records.size() == 1) {
                        // 只有一个记录，直接添加
                        deduplicatedRecords.add(records.get(0));
                    } else {
                        // 多个记录，按 effectiveDate 和 expiryDate 排序，取最新的
                        CrewCertificateQueryVo latestRecord = records.stream()
                            .max((r1, r2) -> {
                                // 比较 effectiveDate
                                int effectiveDateCompare = compareDateString(r1.getEffectiveDate(), r2.getEffectiveDate());
                                if (effectiveDateCompare != 0) {
                                    return effectiveDateCompare;
                                }
                                // effectiveDate 相同时，比较 expiryDate
                                return compareDateString(r1.getExpiryDate(), r2.getExpiryDate());
                            })
                            .orElse(records.get(0));
                        
                        deduplicatedRecords.add(latestRecord);
                        log.info("证照名称：{}，原始记录数：{}，去重后保留1条", groupCertificateName, records.size());
                    }
                }

                List<CrewCertificateQueryVo> records = new ArrayList<>();
                deduplicatedRecords.forEach(record -> {
                    String expiryDate = record.getExpiryDate();
                    int compare = DateUtil.compare(DateUtil.parse(expiryDate), DateUtil.date());
                    // 有效状态
                    if (StrUtil.equals("1", certificateStatus) && compare >= 0) {
                        records.add(record);
                    }
                    // 无效状态
                    if (StrUtil.equals("2", certificateStatus) && compare < 0) {
                        records.add(record);
                    }

                    // 全部状态
                    if (StrUtil.isBlank(certificateStatus)) {
                        records.add(record);
                    }
                });

                // 更新结果集
                result.setRecords(records);
                result.setTotal(records.size());
                log.info("去重处理完成，原始记录数：{}，去重后记录数：{}", groupedRecords.values().stream().mapToInt(List::size).sum(), records.size());
            }

            int size = result.getRecords().size();

            String cacheKey = null;
            List<VerificationDisplayConfig> defaultConfig = null;
            cacheKey = "通用证照配置" + queryDto.getDisplaySource();
            if(configCache.get(cacheKey) != null){
                defaultConfig = configCache.get(cacheKey);
            }else {
                defaultConfig = baseMapper.queryVerificationDisplayConfigs(
                        "通用证照配置", queryDto.getDisplaySource());
                configCache.put(cacheKey,defaultConfig);
            }


            List<VerificationDisplayConfig> defaultConfigEn = null;
            cacheKey = "通用证照配置-中英文" + queryDto.getDisplaySource();
            if(configCache.get(cacheKey) != null){
                defaultConfigEn = configCache.get(cacheKey);
            }else {
                defaultConfigEn = baseMapper.queryVerificationDisplayConfigs(
                        "通用证照配置-中英文", queryDto.getDisplaySource());
                configCache.put(cacheKey,defaultConfigEn);
            }

            // 遍历结果，处理内河船舶船员培训合格证的培训项目，同时转换为Map
            for (CrewCertificateQueryVo record : result.getRecords()) {
                // 处理内河船舶船员培训合格证的培训项目
                if (StrUtil.equalsAny(record.getCertificateName(), "内河船舶船员培训合格证的培训项目","内河船舶船员培训合格证","海船船员培训合格证书")) {
                    // 查询培训项目
                    TrainingProjectVo trainingProjectVo = baseMapper.queryTrainingProjects(record.getDataId());
                    BeanUtil.copyProperties(trainingProjectVo, record);
                }
                // 船上厨师培训合格证明 培训字段
                if (StrUtil.equals(record.getCertificateName(), "船上厨师培训合格证明")) {
                    record.setTrainingName("2006年海事劳工公约简介#食品安全工作程序#厨房一般安全程序#食品营养及配餐#健康及食品卫生规则#配料及存货控制#个人卫生#有关膳食的跨文化及宗教意识#海洋环境保护#实用厨艺");
                    record.setTrainingNameEn("Briefing introduction to MLC.#Food Safety procedures.#General galley safety procedures.#Food nutrition and menu planning.#General Health and Hygiene Practice.#Ingredients and stock controls.#Personal Hygiene.#Multi-cultural and religious awareness.#Marine environmental protection.#Practical cookery.");
                }

                // 外网查询处理长期证照
                if (record.getCertificateExpiringDate().startsWith("9999")
                        || record.getCertificateExpiringDate().startsWith("2900")) {
                    record.setCertificateExpiringDate("长期");
                    record.setCertificateExpiringDateEn("Long-term");
                }

                // 使用反射将对象转换为Map
                Map<String, Object> recordMap = new HashMap<>();
                Class<?> clazz = record.getClass();
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    try {
                        // 排除dataMap字段，避免循环引用
                        if (!"dataMap".equals(field.getName())) {
                            recordMap.put(field.getName(), field.get(record));
                        }
                    } catch (IllegalAccessException e) {
                        log.error("转换对象到Map异常", e);
                    }
                }

                // 根据证书类型和展示来源查询展示配置
                if (queryDto.getDisplaySource() != null && record.getCertificateTypeCode() != null) {
                    String recordCertificateName = record.getCertificateName();
                    if (StrUtil.equals("海船船员适任证书（内河水域航线签注）", recordCertificateName)) {
                        recordCertificateName = "海船船员内河航线行驶资格证明";
                    }

                    if (StrUtil.equals("海上游艇操作人员适任证书", recordCertificateName)) {
                        recordCertificateName = "游艇驾驶证（海上）";
                    }

                    if (StrUtil.equals("内河游艇操作人员适任证书", recordCertificateName)) {
                        recordCertificateName = "游艇驾驶证（内河）";
                    }

                    if (StrUtil.equals("小型海船船员适任证书", recordCertificateName)) {
                        recordCertificateName = "小型海船适任证书";
                    }
                    // 查询展示配置
                    List<VerificationDisplayConfig> configs = null;
                    cacheKey = recordCertificateName + queryDto.getDisplaySource();
                    if(configCache.get(cacheKey) != null){
                        configs = configCache.get(cacheKey);
                    }else {
                        configs = baseMapper.queryVerificationDisplayConfigs(
                                recordCertificateName, queryDto.getDisplaySource());
                        configCache.put(cacheKey,configs);
                    }

                    // 查询证书取 中文 中英文
                    CertificateConfig certificateConfig = null;
                    cacheKey = record.getCertificateName();
                    if(certificateConfigCache.get(cacheKey) != null){
                        certificateConfig = certificateConfigCache.get(cacheKey);
                    }else {
                        certificateConfig = certificateConfigService.getByCertificateCertificateName(record.getCertificateName());
                        certificateConfigCache.put(cacheKey,certificateConfig);
                    }
                    String certificateCnEn = Optional.ofNullable(certificateConfig)
                            .map(CertificateConfig::getCertificateCnEn)
                            .orElse("1");
                    // 查到多条得时候 使用通用证照配置
                    if (size > 1) {
                        // 通过中英文判断取配置
                        List<VerificationDisplayConfig> configList = StrUtil.equals("2", certificateCnEn)
                                ? defaultConfigEn : defaultConfig;
                        // 获取证书名称配置
                        VerificationDisplayConfig certificateNameConfig = configs.stream()
                                .filter(config -> StrUtil.equals("certificateName", config.getChineseDataFieldName()))
                                .findFirst()
                                .orElse(new VerificationDisplayConfig());
                        // 替换通用证照配置中的证书名称
                        configList.stream()
                                .filter(config -> StrUtil.equals("certificateName", config.getChineseDataFieldName()))
                                .forEach(config -> {
                                    config.setChineseDataDefaultValue(certificateNameConfig.getChineseDataDefaultValue());
                                    if (StrUtil.equals("2", certificateCnEn)) {
                                        config.setEnglishDataDefaultValue(certificateNameConfig.getEnglishDataDefaultValue());
                                    }
                                });
                        configs = configList;
                    }


                    // 处理展示配置
                    if (configs != null && !configs.isEmpty()) {
                        List<Map<String, Object>> displayFields = new ArrayList<>();
                        configs.forEach(config -> {
                            Map<String, Object> map = getMap(recordMap, config, record.getCertificateId());
                            String labelName = (String) map.get("label");
                            if (StrUtil.equals("证照状态", labelName)) {
                                Map<String, Object> noticeContentMap = new HashMap<>();
                                noticeContentMap.put("label", "提醒内容");
                                String statusFlag = record.getStatusFlag();
                                String value = (String) map.get("value");
                                if (StrUtil.equals("有效", value)) {
                                    noticeContentMap.put("value", "正常");
                                }
                                // 过期提醒
                                if (StrUtil.equals("无效", value) && !StrUtil.equals("-2", statusFlag)) {
                                    noticeContentMap.put("value", "该证照已过期，证照状态为无效");
                                }
                                // 注销状态
                                if (StrUtil.equals("-2", statusFlag)) {
                                    map.put("value", "无效");
                                    noticeContentMap.put("value", "该证照已注销，证照状态为无效");
                                }
                                displayFields.add(noticeContentMap);
                            }
                            displayFields.add(map);
                        });
                        // 将展示字段列表添加到记录中
                        record.setDisplayFields(displayFields);
                    }
                }
            }



            log.info("船员列表查询-总耗时：{}ms，总记录数：{}", System.currentTimeMillis() - queryStartTime, result.getTotal());
            resultMap.put("records", result.getRecords());
            resultMap.put("total", result.getTotal()); // 使用总记录数
            resultMap.put("size", result.getSize());
            resultMap.put("current", result.getCurrent());
            resultMap.put("orders", "");
            resultMap.put("searchCount", true);
            resultMap.put("pages", result.getPages()); // 使用总记录数计算总页数
            resultMap.put("validCount", validCount);
            resultMap.put("invalidCount", invalidCount);
            return Result.success(resultMap);
        } catch (Exception e) {
            log.error("查询船员证书信息异常：", e);
            return Result.failed("查询船员证书信息失败：" + e.getMessage());
        }
    }


    /**
     * 内网船员证书查询
     * @param intranetCrewQueryDto 查询条件
     * @param page 分页参数
     * @return 查询结果
     */
    public IPage<CertificateIntranetCrewQueryVo> certificateIntranetCrewQueryAuth(IntranetCrewQueryDto intranetCrewQueryDto, Page page) {
        Date start = new Date();
        log.info("智慧海事内网船员证书查询，开始时间：{}", start);
        log.info("智慧海事内网船员证书查询，参数：{}", intranetCrewQueryDto);

        // 参数校验
        if (intranetCrewQueryDto == null) {
            throw new RuntimeException("查询参数不能为空");
        }

        try {
            String orgCode = intranetCrewQueryDto.getLoginOrgCode();
            // 如果是 01 局部测试的账号，直接设为 ""。
            if ("01".equals(orgCode)) {
                intranetCrewQueryDto.setLoginOrgCode("");
            }
            // 处理证书类型ID
            if (StrUtil.isNotBlank(intranetCrewQueryDto.getCertificateType())) {
                log.info("开始处理证书类型ID，原始值：{}", intranetCrewQueryDto.getCertificateType());
                // 按英文逗号分割证书类型ID
                String[] certificateTypeIds = intranetCrewQueryDto.getCertificateType().split(",");

                // 从缓存中获取证书类型配置
                List<String> certTypeDirCodes = Arrays.stream(certificateTypeIds)
                    .map(id -> certTypeDirIdCache.get(id))
                    .filter(Objects::nonNull)
                    .map(CertTypeDirectory::getCertificateTypeCode)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());

                log.info("获取到的证书类型目录代码列表：{}", certTypeDirCodes);

                // 设置到查询条件中
                intranetCrewQueryDto.setCertTypeDirCodes(certTypeDirCodes);
            }

            // 执行查询
            IPage<CertificateIntranetCrewQueryVo> resultPage = baseMapper.certificateIntranetCrewQueryAuth(page, intranetCrewQueryDto);

            // 遍历结果，处理证照类型名称和其他信息
            for (CertificateIntranetCrewQueryVo record : resultPage.getRecords()) {
                // 处理证照类型名称
                if (StrUtil.isNotBlank(record.getCertificateTypeCode())) {
                    // 从缓存中获取证照类型
                    CertTypeDirectory certType = certTypeCodeCache.get(record.getCertificateTypeCode());

                    if (certType != null) {
                        // 从缓存中获取父级类型
                        CertTypeDirectory parentType = null;
                        if (StrUtil.isNotBlank(certType.getParentId()) && !"-1".equals(certType.getParentId())) {
                            parentType = certTypeDirIdCache.get(certType.getParentId());
                        }

                        // 组装证照类型名称
                        String typeName = (parentType != null ? parentType.getCertificateTypeName() + "-" : "")
                                        + record.getCertificateTypeName();
                        record.setCertificateTypeName(typeName);
                    }
                }

                // 处理内河船舶船员培训合格证的培训项目
                if ("内河船舶船员培训合格证".equals(record.getCertificateName())) {
                    TrainingProjectVo trainingProjectVo = baseMapper.queryTrainingProjects(record.getDataId());
                    BeanUtil.copyProperties(trainingProjectVo, record);
                }

                 // 船上厨师培训合格证明 培训字段
                if (StrUtil.equals(record.getCertificateName(), "船上厨师培训合格证明")) {
                	record.setTrainingNamesCn("2006年海事劳工公约简介#食品安全工作程序#厨房一般安全程序#食品营养及配餐#健康及食品卫生规则#配料及存货控制#个人卫生#有关膳食的跨文化及宗教意识#海洋环境保护#实用厨艺");
                	record.setTrainingNamesEn("Briefing introduction to MLC.#Food Safety procedures.#General galley safety procedures.#Food nutrition and menu planning.#General Health and Hygiene Practice.#Ingredients and stock controls.#Personal Hygiene.#Multi-cultural and religious awareness.#Marine environmental protection.#Practical cookery.");
                }

                // 出生日期为空 匹配身份证 从身份证号中截取
                if (StrUtil.isBlank(record.getBirth())
                        && StrUtil.isNotBlank(record.getHolderIdentityNumber())
                        && IdcardUtil.isValidCard(record.getHolderIdentityNumber())) {
                    DateTime dateTime = IdcardUtil.getBirthDate(record.getHolderIdentityNumber());
                    String birth = DateUtil.format(dateTime, "yyyy-MM-dd");
                    record.setBirth(birth);
                }

                if (record.getCertificateExpiringDate().startsWith("9999")
                        || record.getCertificateExpiringDate().startsWith("2900")) {
                    record.setCertificateExpiringDate("长期");
                    record.setCertificateExpiringDateEn("Long-term");
                }
            }

            log.info("内网船员证书查询完成，共查询到 {} 条记录", resultPage.getTotal());
            Date end = new Date();
            log.info("内网船员证书查询耗时：{}ms", DateUtil.between(start, end, DateUnit.MS));
            return resultPage;

        } catch (Exception e) {
            log.error("内网船员证书查询异常", e);
            throw new RuntimeException("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据配置和对象映射生成一个Map，用于存储证书信息。
     *
     * @param objMap 证书对象映射
     * @param config 验证显示配置
     * @param certificateId 证书ID
     * @return 包含证书信息的Map
     */
    public Map<String, Object> getMap(Map<String, Object> objMap, VerificationDisplayConfig config, String certificateId) {
        Map<String, Object> hashMap = new HashMap<>();
        // 取中文
        if (StringUtils.isNotBlank(config.getChineseDataDefaultValue())) {// 有默认值
            hashMap.put("label", config.getChineseLabelName());
            hashMap.put("value", config.getChineseDataDefaultValue());
        } else {// 无默认值则从照面表找
            if ("statusFlag".equals(config.getChineseDataFieldName())) {// 有效值单独处理
                String status;
                Date issueDate = (Date) objMap.get("issueDate");
                Date expireDate = (Date) objMap.get("expireDate");
                Date date = new Date();
                if (date.after(issueDate) && date.before(expireDate)) {
                    status = "有效";
                } else {
                    status = "无效";
                }
                hashMap.put("label", config.getChineseLabelName());
                hashMap.put("value", status);
            } else {
                hashMap.put("label", config.getChineseLabelName());
                Object value = objMap.get(config.getChineseDataFieldName());
                hashMap.put("value", ObjectUtil.isEmpty(value) ? "-" :value);
            }
        }
        // 取英文
        if (StringUtils.isNotBlank(config.getEnglishDataDefaultValue())) {
            hashMap.put("labelEn", config.getEnglishLabelName());
            hashMap.put("valueEn", config.getEnglishDataDefaultValue());
        } else {
            Object obj = objMap.get(config.getEnglishDataFieldName());
            if (ObjectUtil.isNotEmpty(obj)) {// 如果找得到就取出来，没有就要手动转换了，例如有些字段是没有英文的，需要手动转。
                hashMap.put("labelEn", config.getEnglishLabelName());
                hashMap.put("valueEn", obj);
            } else {// 不需要转换,返回中文值
                hashMap.put("labelEn", config.getEnglishLabelName());
                hashMap.put("valueEn", null);// ?
            }
        }
        // 中文证书不展示英文字段了
        if ("statusFlag".equals(config.getChineseDataFieldName())
                && StrUtil.isNotBlank(config.getEnglishLabelName())) {
            if ("有效".equals(hashMap.get("value"))) {
                hashMap.put("valueEn", "Effective");
            } else {
                hashMap.put("valueEn", "Invalid");
            }
        }
        hashMap.put("highlight", config.getHighlight());
        return hashMap;
    }

    /**
     * 比较两个日期字符串，支持多种日期格式
     * @param date1 日期字符串1
     * @param date2 日期字符串2
     * @return 比较结果：-1表示date1<date2，0表示相等，1表示date1>date2
     */
    private int compareDateString(String date1, String date2) {
        // 处理空值情况
        if (StrUtil.isBlank(date1) && StrUtil.isBlank(date2)) {
            return 0;
        }
        if (StrUtil.isBlank(date1)) {
            return -1;
        }
        if (StrUtil.isBlank(date2)) {
            return 1;
        }
        
        try {
            // 尝试解析日期，支持多种格式
            Date parsedDate1 = parseDateString(date1);
            Date parsedDate2 = parseDateString(date2);
            
            if (parsedDate1 == null && parsedDate2 == null) {
                return 0;
            }
            if (parsedDate1 == null) {
                return -1;
            }
            if (parsedDate2 == null) {
                return 1;
            }
            
            return parsedDate1.compareTo(parsedDate2);
        } catch (Exception e) {
            log.warn("日期比较异常，date1: {}, date2: {}, 错误: {}", date1, date2, e.getMessage());
            // 如果解析失败，按字符串比较
            return date1.compareTo(date2);
        }
    }
    
    /**
     * 解析日期字符串，支持多种格式
     * @param dateStr 日期字符串
     * @return 解析后的Date对象，解析失败返回null
     */
    private Date parseDateString(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return null;
        }
        
        // 支持的日期格式
        String[] patterns = {
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyyMMdd",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss.SSS"
        };
        
        for (String pattern : patterns) {
            try {
                return DateUtil.parse(dateStr, pattern);
            } catch (Exception e) {
                // 继续尝试下一个格式
            }
        }
        
        return null;
    }


}

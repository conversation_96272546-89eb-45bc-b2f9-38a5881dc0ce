package com.js.hszpt.builder;

import com.js.hszpt.vo.CertificateVo;

import java.util.Collections;
import java.util.Map;

import static com.js.hszpt.constants.OilPollutionWarrantyConstant.FUEL_POLLUTION;
import static com.js.hszpt.constants.OilPollutionWarrantyConstant.FUEL_POLLUTION_REVERSE;

public class FuelPollutionCertBuilder implements CertificateInfoBuilder{
    @Override
    public Map<String, String> certInfo() {
        return FUEL_POLLUTION;
    }

    @Override
    public Map<String, String> certReverseInfo() {
        return FUEL_POLLUTION_REVERSE;
    }

    @Override
    public Map<String, String> buildCertificateAttribute(CertificateVo certificateVo) {
        return Collections.emptyMap();
    }
}

package com.js.hszpt.dto;

import lombok.Data;

@Data
public class CrewCertificateQueryDto {
    private String certificateNumber;          // 证书编号
    private String certificateHolderName;      // 持证人姓名
    private String certificateHolderCode;      // 身份证号
    private String certificateTypeCode;        // 证照类型代码
    private String issueOrgCode;               // 签发机构代码
    private String trainingOrgCode;            // 培训机构代码
    private String foreignOrgCode;             // 外派机构代码
    private String mainSignOrgCode;            // 主管签证机关代码
    private String certificateName;            // 证照名称
    private String birth;                      // 出生年月日-yyyymmdd
    private String certificateId;              // 新增证照ID查询字段
    private String displaySource;              // 展示来源：1-外网查询 2-app核验 3-app失效证照查询
    //证照开始时间
    private String certStartDate;
    //证照结束时间
    private String certEndDate;
    private String validFromDate;
    private String validToDate;
    private String certificateStatus;
    //App使用的入参，过滤培训相关的证书
    private String notCert;
    private Long pageSize = 10L;
    private Long pageNumber = 1L;
}

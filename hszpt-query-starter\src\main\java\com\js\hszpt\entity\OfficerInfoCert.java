package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 
 * @ClassName:  OfficerInfoCert   
 * @Description:TODO()   
 * @author:   System Generation 
 */
@Data

@TableName("officer_info_cert")
@ApiModel(value = "")
public class OfficerInfoCert extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String officerName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String department;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String provDeptName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String orgCode;


}
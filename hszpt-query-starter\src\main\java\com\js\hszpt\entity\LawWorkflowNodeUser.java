package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  LawWorkflowNodeUser   
 * @Description:TODO(工作流用户信息)   
 * @author:   System Generation 
 */
@Data

@TableName("LAW_WORKFLOW_NODE_USER")
@ApiModel(value = "工作流用户信息")
public class LawWorkflowNodeUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "LAW_WORKFLOW_NODE_USER_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
     * 工作流模板节点机构自定义配置表ID
     */
    @TableField("LAW_WORKFLOW_NODE_ORG_ID")
    @ApiModelProperty(value = "工作流模板节点机构自定义配置表ID")
    private String lawWorkflowNodeOrgId;

    /**
    * 工作流模板ID
    */
    @ApiModelProperty(value = "工作流模板ID")
    private String lawWorkflowNodeSetId;


    /**
    * 工作流模板节点ID
    */
    @ApiModelProperty(value = "工作流模板节点ID")
    private String lawWorkflowNodeInfoId;


    /**
    * 用户身份证号码
    */
    @ApiModelProperty(value = "用户身份证号码")
    private String certNo;


    /**
    * sys_user表的username
    */
    @ApiModelProperty(value = "sys_user表的username")
    private String userCode;


    /**
    * 用户姓名名称
    */
    @ApiModelProperty(value = "用户姓名名称")
    private String userName;


    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createOperId;


    /**
    * 创建部门
    */
    @ApiModelProperty(value = "创建部门")
    private String createOperDept;


    /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String modifyOperId;


    /**
    * 修改部门
    */
    @ApiModelProperty(value = "修改部门")
    private String modifyOperDept;


    /**
    * 修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;


    /**
    * 版本号
    */
    @ApiModelProperty(value = "版本号")
    private String recordVersion;


    /**
    * 海事机构
    */
    @ApiModelProperty(value = "海事机构")
    private String orgCode;


}
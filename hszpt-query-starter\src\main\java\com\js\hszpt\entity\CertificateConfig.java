package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dwdz_ctf_certificate_config") // 对应数据库表名
public class CertificateConfig {

    @TableId(value = "CERTIFICATE_TYPE_ID") // 主键，手动输入模式
    private String certificateTypeId;

    @TableField("CERTIFICATE_NAME")
    private String certificateName;

    @TableField("CERT_TYPE_DIR_ID")
    private String certTypeDirId;

    @TableField("CERTIFICATE_NAME_EN")
    private String certificateNameEn;

    @TableField("CERT_TYPE_DIR_CODE")
    private String certTypeDirCode;

    @TableField("CERTIFICATE_TYPE_CODE")
    private String certificateTypeCode;

    @TableField("CERTIFICATE_CN_EN")
    private String certificateCnEn;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("catalog_id")
    private String catalogId;

    @TableField("CREATE_OPER_ID")
    private String createOperId;

    @TableField("CREATE_DATE")
    private Date createDate;

    @TableField("MODIFY_OPER_ID")
    private String modifyOperId;

    @TableField("MODIFY_DATE")
    private Date modifyDate;

    @TableLogic // 逻辑删除注解（需在配置中定义逻辑删除值）
    @TableField("DEL_FLAG")
    private String delFlag;

    @TableField(value = "rec_create_date", fill = FieldFill.INSERT) // 自动填充创建时间
    private Date recCreateDate;

    @TableField(value = "rec_modify_date", fill = FieldFill.INSERT_UPDATE) // 自动填充更新时间
    private Date recModifyDate;

    @TableField("condition")
    private String condition; // 特殊字段名需要用@TableField映射
}

package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.entity.LawWorkflowNodeSet;
import com.js.hszpt.mapper.LawWorkflowNodeSetDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
 
/**
 * 
 * @ClassName:  LawWorkflowNodeSetService    
 * @Description:TODO(工作流模板配置表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class LawWorkflowNodeSetService extends ServiceImpl<LawWorkflowNodeSetDao,LawWorkflowNodeSet> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<LawWorkflowNodeSet> findByCondition(LawWorkflowNodeSet param, SearchVo searchVo, PageVo pageVo) {
		Page<LawWorkflowNodeSet> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<LawWorkflowNodeSet> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<LawWorkflowNodeSet>      
	 * @throws
	 */
	public List<LawWorkflowNodeSet> findByCondition(LawWorkflowNodeSet param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeSet> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<LawWorkflowNodeSet>      
	 * @throws
	 */
	private QueryWrapper<LawWorkflowNodeSet> getCondition(LawWorkflowNodeSet param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeSet> queryWrapper = new QueryWrapper<LawWorkflowNodeSet>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}
}
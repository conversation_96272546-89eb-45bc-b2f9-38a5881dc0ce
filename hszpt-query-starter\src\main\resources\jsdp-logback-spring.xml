<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration scan="false" scanPeriod="60 seconds" debug="true">

	<!-- 日志文件名的设置，读取配置文件或系统变量里的spring.application.name属性。 -->
	<springProperty scope="context" name="LOG_FILE_NAME" source="spring.application.name" defaultValue="jsdp"/>

	<!-- 日志文件路径配置，读取启动命令或系统变量里的logging.file.path属性。 -->
	<springProperty scope="context" name="LOG_HOME" source="logging.file.path" defaultValue="./logs"/>
	<!-- 日志输出控制台编码设置，读取启动命令或系统变量里的logging.console.encoding属性。 -->
	<springProperty scope="context" name="LOG_CONSOLE_ENCODING" source="logging.console.encoding"  defaultValue="UTF-8"/>
	<!-- 日志输出文件编码设置，读取启动命令或系统变量里的logging.file.encoding属性。 -->
	<springProperty scope="context" name="LOG_FILE_ENCODING" source="logging.file.encoding"  defaultValue="UTF-8"/>

	<!-- 日志文件最多保留的天数，读取配置文件或系统变量里的logging.file.max-history。 -->
	<springProperty scope="context" name="LOG_FILE_MAX_HISTORY" source="logging.file.max-history"  defaultValue="60"/>
	<!-- 日志文件总体的最大值，达到之后就不再产生日志,读取配置文件或系统变量里的logging.file.total-size-cap。 -->
	<springProperty scope="context" name="LOG_FILE_TOTAL_SIZE_CAP" source="logging.file.total-size-cap"  defaultValue="10GB"/>
	<!-- 单个日志文件最大值，达到之后就进行切割，读取配置文件或系统变量里的logging.file.max-size。 -->
	<springProperty scope="context" name="LOG_FILE_MAX_SIZE" source="logging.file.max-size" defaultValue="200MB" />

	<property name="LOG_PATTERN" value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [%thread] %logger-%L:) %m%n" />
	<!-- 不采用彩色日志，则pattern使用下面这行 -->
	<!--
	<property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [%thread] %logger-%L: %m%n" />
	-->

	<!-- 彩色日志 -->
	<conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
	<conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
	<conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

	<!--输出到控制台 -->
	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<Pattern>${LOG_PATTERN}</Pattern>
			<charset>${LOG_CONSOLE_ENCODING}</charset>
		</encoder>
	</appender>
	
	<appender name="FILE" class="ch.qos.logback.core.FileAppender">  
	    <file>${LOG_HOME}/${LOG_FILE_NAME}.log</file>  
	    <append>true</append>  
	    <encoder>
			<pattern>${LOG_PATTERN}</pattern>
			<charset>${LOG_FILE_ENCODING}</charset>
		</encoder>
  </appender>  

	<!-- dev -->
	<springProfile name="dev">
		<logger name="org.apache.http.wire" level="info" />
		<logger name="org.apache.http.headers" level="info" />
		<root level="info">
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="FILE" />  
		</root>
	</springProfile>

	<!-- prod -->
	<springProfile name="!dev">
		<!-- debug日志 -->
		<appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
			<file>${LOG_HOME}/${LOG_FILE_NAME}-debug.log</file>
			<encoder>
				<pattern>${LOG_PATTERN}</pattern>
				<charset>${LOG_FILE_ENCODING}</charset>
			</encoder>
			<!-- 同时按照时间和大小滚动，每个文件100MB，总共存储20GB.保存近60天的日志 -->
			<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
				<fileNamePattern>${LOG_HOME}/${LOG_FILE_NAME}-debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
				<maxHistory>${LOG_FILE_MAX_HISTORY}</maxHistory>
				<maxFileSize>${LOG_FILE_MAX_SIZE}</maxFileSize>
				<totalSizeCap>${LOG_FILE_TOTAL_SIZE_CAP}</totalSizeCap>
			</rollingPolicy>
			<!-- 此日志文件只记录info级别的 -->
			<filter class="ch.qos.logback.classic.filter.LevelFilter">
				<level>debug</level>
				<onMatch>ACCEPT</onMatch>
				<onMismatch>DENY</onMismatch>
			</filter>
		</appender>

		<!-- INFO日志 -->
		<appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
			<file>${LOG_HOME}/${LOG_FILE_NAME}-info.log</file>
			<encoder>
				<pattern>${LOG_PATTERN}</pattern>
				<charset>${LOG_FILE_ENCODING}</charset>
			</encoder>
			<!-- 同时按照时间和大小滚动，每个文件100MB，总共存储20GB.保存近60天的日志 -->
			<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
				<fileNamePattern>${LOG_HOME}/${LOG_FILE_NAME}-info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
				<maxHistory>${LOG_FILE_MAX_HISTORY}</maxHistory>
				<maxFileSize>${LOG_FILE_MAX_SIZE}</maxFileSize>
				<totalSizeCap>${LOG_FILE_TOTAL_SIZE_CAP}</totalSizeCap>
			</rollingPolicy>
			<!-- 此日志文件记录info和warn级别的 -->
			<filter class="ch.qos.logback.classic.filter.LevelFilter">
				<level>info</level>
				<onMatch>ACCEPT</onMatch>
				<onMismatch>NEUTRAL</onMismatch>
			</filter>
			<filter class="ch.qos.logback.classic.filter.LevelFilter">
				<level>warn</level>
				<onMatch>ACCEPT</onMatch>
				<onMismatch>DENY</onMismatch>
			</filter>
		</appender>

		<!-- ERROR日志 -->
		<appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
			<file>${LOG_HOME}/${LOG_FILE_NAME}-error.log</file>
			<encoder>
				<pattern>${LOG_PATTERN}</pattern>
				<charset>${LOG_FILE_ENCODING}</charset>
			</encoder>
			<!-- 同时按照时间和大小滚动，每个文件100MB，总共存储20GB.保存近60天的日志 -->
			<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
				<fileNamePattern>${LOG_HOME}/${LOG_FILE_NAME}-error.%d{yyyy-MM-dd}.%i.log
				</fileNamePattern>
				<maxHistory>${LOG_FILE_MAX_HISTORY}</maxHistory>
				<maxFileSize>${LOG_FILE_MAX_SIZE}</maxFileSize>
				<totalSizeCap>${LOG_FILE_TOTAL_SIZE_CAP}</totalSizeCap>
			</rollingPolicy>
			<!-- 此日志文件只记录info级别的 -->
			<filter class="ch.qos.logback.classic.filter.LevelFilter">
				<level>error</level>
				<onMatch>ACCEPT</onMatch>
				<onMismatch>DENY</onMismatch>
			</filter>
		</appender>
		<!--若要开启debug日志调试，请在下列自行补充，参考如下-->
	
		<root level="info">
			<appender-ref ref="CONSOLE" />
			<appender-ref ref="DEBUG_FILE" />
			<appender-ref ref="INFO_FILE" />
			<appender-ref ref="ERROR_FILE" />
		</root>
	</springProfile>

</configuration>

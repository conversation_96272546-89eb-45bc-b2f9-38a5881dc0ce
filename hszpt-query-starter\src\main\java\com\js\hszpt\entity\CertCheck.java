package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CertCheck   
 * @Description:TODO()   
 * @author:   System Generation 
 */
@Data

@TableName("cert_check")
@ApiModel(value = "")
public class CertCheck extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certCheckId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String windowApplyId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String checkResult;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String opinion;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateNum;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String makeStaff;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date makeDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String col1;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String col2;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String col3;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String col4;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String remark;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String modifyOperId;

    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String checkStaff;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateEnId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateNumEn;


}
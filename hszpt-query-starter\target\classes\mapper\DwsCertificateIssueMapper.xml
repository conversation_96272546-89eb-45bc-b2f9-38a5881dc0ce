<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwsCertificateIssueMapper">

    <!-- 统计证照签发情况 -->
    <select id="statisticsCertificateIssue" resultType="com.js.hszpt.dto.CertificateIssueStatisticsDTO">
        SELECT
        d.CODE as orgCode,
        d.NAME as orgName,
        SUM(i.issue_count) as issueCount,
        SUM(CASE WHEN application_source_code = '1' THEN issue_count ELSE 0 END) as windowCount,
        SUM(CASE WHEN application_source_code = '2' THEN issue_count ELSE 0 END) as onlineCount
        FROM
        dws_ctf_certificate_issue i
        LEFT JOIN
        dws_ctf_sys_dept d ON SUBSTR(i.issuer_code, 1, LENGTH(d.CODE)) = d.CODE
        WHERE 1=1
        <choose>
            <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                AND (i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
                or i.issuer_name in (select org_name from dws_ctf_other_org where msa_org_code like CONCAT(#{param.loginUserOrgCode}, '%')))
                AND d.PARENT_ID = (select dept_id from dws_ctf_sys_dept where code =#{param.loginUserOrgCode})
            </when>
            <otherwise>
                AND GOV_LEVEL = '2'
            </otherwise>
        </choose>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.orgType != null and param.orgType != ''">
            AND i.org_type = #{param.orgType}
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY d.CODE, d.NAME
    </select>

    <!-- 按年统计证照签发情况 -->
    <select id="statisticsCertificateIssueByYear" resultType="com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO">
        SELECT
        d.CODE as orgCode,
        d.NAME as orgName,
        SUBSTR(i.ds_certificate_date, 1, 4) as year,
        SUM(i.issue_count) as issueCount
        FROM
        dws_ctf_certificate_issue i
        LEFT JOIN
        dws_ctf_sys_dept d ON SUBSTR(i.issuer_code, 1, LENGTH(d.CODE)) = d.CODE
        WHERE 1=1
        <choose>
            <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                AND (i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
                or i.issuer_name in (select org_name from dws_ctf_other_org where msa_org_code like CONCAT(#{param.loginUserOrgCode}, '%')))
                AND d.PARENT_ID = (select dept_id from dws_ctf_sys_dept where code =#{param.loginUserOrgCode})
            </when>
            <otherwise>
                AND GOV_LEVEL = '2'
            </otherwise>
        </choose>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY d.CODE, d.NAME, SUBSTR(i.ds_certificate_date, 1, 4)
        ORDER BY d.CODE, SUBSTR(i.ds_certificate_date, 1, 4)
    </select>

    <!-- 详细统计证照签发情况 -->
    <select id="statisticsCertificateIssueDetail" resultType="com.js.hszpt.dto.CertificateIssueDetailStatisticsDTO">
        WITH RECURSIVE
        sys_dept AS (
        select msa_org_code as orgCode,org_name as orgName from dws_ctf_other_org
        <where>
            <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                msa_org_code like CONCAT(#{param.loginUserOrgCode}, '%')
            </if>
        </where>
        group by msa_org_code,org_name
        ),
        cte_child AS (
        SELECT etd_parent.CERT_TYPE_DIR_ID
        FROM dws_ctf_cert_type_directory etd_parent
        <where>
            <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
                etd_parent.CERT_TYPE_DIR_ID IN
                <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                    #{typeCode}
                </foreach>
            </if>
        </where>
        UNION ALL
        SELECT etd_child.CERT_TYPE_DIR_ID
        FROM dws_ctf_cert_type_directory etd_child
        INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
        ),
        certificate_types AS (
        SELECT DISTINCT etd.CERTIFICATE_TYPE_CODE
        FROM dws_ctf_cert_type_directory etd
        WHERE etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
        ),
        filtered_issues AS (
        SELECT i.* FROM dws_ctf_certificate_issue i
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            JOIN certificate_types ct ON i.certificate_type_code = ct.CERTIFICATE_TYPE_CODE
        </if>
        <where>
            <if test="param.orgType != null and param.orgType != ''">
                AND i.org_type = #{param.orgType}
            </if>
            <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
                AND i.holder_category_code IN
                <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
                AND i.matter_nature_code IN
                <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
            <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
                AND i.application_source_code = #{param.applicationSourceCode}
            </if>
            <if test="param.statusCode != null and param.statusCode != ''">
                AND i.certificate_status_code = #{param.statusCode}
            </if>
            <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
                AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN SUBSTR(replace(#{param.startTime},'-',''),1,4) AND SUBSTR(replace(#{param.endTime},'-',''),1,4)
            </if>
            <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
                AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
                CASE
                WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
                WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
                WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
                WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
                END)
                BETWEEN
                CONCAT(SUBSTR(replace(#{param.startTime},'-',''),1,4),
                CASE
                WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '01' AND '03' THEN 'Q1'
                WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '04' AND '06' THEN 'Q2'
                WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '07' AND '09' THEN 'Q3'
                WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '10' AND '12' THEN 'Q4'
                END)
                AND
                CONCAT(SUBSTR(replace(#{param.endTime},'-',''),1,4),
                CASE
                WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '01' AND '03' THEN 'Q1'
                WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '04' AND '06' THEN 'Q2'
                WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '07' AND '09' THEN 'Q3'
                WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '10' AND '12' THEN 'Q4'
                END)
            </if>
            <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
                AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,6) AND SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,6)
            </if>
            <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
                AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
            </if>
            <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
                AND i.ctf_affair_name in
                <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
            <if test="param.certificateIds != null and param.certificateIds.size() > 0">
                AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
                where certificate_type_code in
                <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                )
            </if>
        </where>
        )
        select sd.orgCode                                                               as orgCode,
        sd.orgName                                                                      as orgName,
        (#{param.startTime})                                                            as startTime,
        (#{param.endTime})                                                              as endTime,
        COALESCE(SUM(i.issue_count), 0)                                              as issueCount,
        SUM(CASE WHEN i.application_source_code = '1' THEN i.issue_count ELSE 0 END) as onlineIssueCount, -- 在线申请
        SUM(CASE WHEN i.application_source_code = '2' THEN i.issue_count ELSE 0 END) as counterIssueCount -- 窗口申请
        from sys_dept sd
            INNER JOIN filtered_issues i ON i.issuer_name = sd.orgName
        GROUP BY orgCode, orgName
        order by issueCount desc;
    </select>

    <!-- 详细统计证照签发情况 -->
    <select id="statisticsCertificateIssueOrgDetail" resultType="com.js.hszpt.dto.CertificateIssueDetailStatisticsDTO">
        WITH RECURSIVE
        sys_dept AS (
        select NAME as orgName, CODE as orgCode from dws_ctf_sys_dept
        <where>
            <choose>
                <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                    AND CODE LIKE CONCAT(#{param.loginUserOrgCode}, '%')
                    AND PARENT_ID = (select dept_id from dws_ctf_sys_dept where code =#{param.loginUserOrgCode})
                </when>
                <otherwise>
                    AND GOV_LEVEL = '2'
                </otherwise>
            </choose>
            <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
                AND (
                <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                    code LIKE CONCAT(#{issuerCode}, '%')
                </foreach>
                )
            </if>
        </where>
        ),
        cte_child AS (
        SELECT etd_parent.CERT_TYPE_DIR_ID
        FROM dws_ctf_cert_type_directory etd_parent
        <where>
            <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
                etd_parent.CERT_TYPE_DIR_ID IN
                <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                    #{typeCode}
                </foreach>
            </if>
        </where>
        UNION ALL
        SELECT etd_child.CERT_TYPE_DIR_ID
        FROM dws_ctf_cert_type_directory etd_child
        INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
        ),
        certificate_types AS (
        SELECT DISTINCT etd.CERTIFICATE_TYPE_CODE
        FROM dws_ctf_cert_type_directory etd
        WHERE etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
        )
        select sd.orgCode                                                               as orgCode,
        sd.orgName                                                                      as orgName,
        (#{param.startTime})                                                            as startTime,
        (#{param.endTime})                                                              as endTime,
        COALESCE(SUM(i.issue_count), 0)                                              as issueCount,
        SUM(CASE WHEN i.application_source_code = '1' THEN i.issue_count ELSE 0 END) as onlineIssueCount, -- 在线申请
        SUM(CASE WHEN i.application_source_code = '2' THEN i.issue_count ELSE 0 END) as counterIssueCount -- 窗口申请
        from sys_dept sd
            left join dws_ctf_certificate_issue i on i.issuer_code LIKE CONCAT(sd.orgCode, '%')
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            JOIN certificate_types ct ON i.certificate_type_code = ct.CERTIFICATE_TYPE_CODE
        </if>
        <if test="param.orgType != null and param.orgType != ''">
            AND i.org_type = #{param.orgType}
        </if>
        <if test="param.issuerCodes != null and !param.issuerCodes.isEmpty()">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN SUBSTR(replace(#{param.startTime},'-',''),1,4) AND SUBSTR(replace(#{param.endTime},'-',''),1,4)
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(replace(#{param.startTime},'-',''),1,4),
            CASE
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(replace(#{param.endTime},'-',''),1,4),
            CASE
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,6) AND SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,6)
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
                    where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY orgCode, orgName
        order by issueCount desc;
    </select>

    <!-- 获取上一个周期的签发总量 -->
    <select id="getPreviousPeriodIssueCount" resultType="java.lang.Long">
        SELECT
        SUM(i.issue_count) as issueCount
        FROM
        dws_ctf_certificate_issue i
        WHERE
        <choose>
            <when test='param.orgType == "1"'>
                i.issuer_code LIKE CONCAT(#{orgCode}, '%')
            </when>
            <otherwise>
                i.issuer_name in (select org_name from dws_ctf_other_org where msa_org_code like #{orgCode} || '%')
            </otherwise>
        </choose>
        <if test="param.orgType != null and param.orgType != ''">
            AND i.org_type = #{param.orgType}
        </if>
        <if test="param.timeType == 'year'">
            AND TO_DATE(i.ds_certificate_date, 'YYYYMMDD') BETWEEN
            (TO_DATE(#{param.startTime}, 'YYYY-MM-DD') - INTERVAL '1 YEAR')
            AND
            (TO_DATE(#{param.endTime}, 'YYYY-MM-DD') - INTERVAL '1 YEAR')
        </if>
        <if test="param.timeType == 'month'">
            AND TO_DATE(i.ds_certificate_date, 'YYYYMMDD') BETWEEN
            (TO_DATE(#{param.startTime}, 'YYYY-MM-DD') - INTERVAL '1 MONTH')
            AND
            (TO_DATE(#{param.endTime}, 'YYYY-MM-DD') - INTERVAL '1 MONTH')
        </if>
        <if test="param.timeType == 'day'">
            AND TO_DATE(i.ds_certificate_date, 'YYYYMMDD') BETWEEN
            (TO_DATE(#{param.startTime}, 'YYYY-MM-DD') - INTERVAL '1 DAY')
            AND
            (TO_DATE(#{param.endTime}, 'YYYY-MM-DD') - INTERVAL '1 DAY')
        </if>
        <if test="param.timeType == 'quarter'">
            AND TO_DATE(i.ds_certificate_date, 'YYYYMMDD') BETWEEN
            (TO_DATE(#{param.startTime}, 'YYYY-MM-DD') - INTERVAL '3 MONTH')
            AND
            (TO_DATE(#{param.endTime}, 'YYYY-MM-DD') - INTERVAL '3 MONTH')
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
    </select>

    <!-- 获取当前周期的总签发量 -->
    <select id="getTotalIssueCount" resultType="com.js.hszpt.dto.CertificateIssueDetailStatisticsDTO">
        SELECT
        COALESCE(SUM(i.issue_count),0) as issueCount,
        COALESCE(sum(CASE WHEN i.application_source_code = '1' THEN i.issue_count ELSE 0 END),0) as onlineIssueCount, --在线办理
        COALESCE(sum(CASE WHEN i.application_source_code = '2' THEN i.issue_count ELSE 0 END),0) as counterIssueCount --窗口申请
        FROM
        dws_ctf_certificate_issue i
        WHERE 1=1
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            AND i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                (i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
                     or i.issuer_name in (select org_name from dws_ctf_other_org where msa_org_code like #{orgCode} || '%'))
            </foreach>
            )
        </if>
        <if test="param.orgType != null and param.orgType != ''">
            AND i.org_type = #{param.orgType}
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN SUBSTR(replace(#{param.startTime},'-',''),1,4) AND SUBSTR(replace(#{param.endTime},'-',''),1,4)
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(replace(#{param.startTime},'-',''),1,4),
            CASE
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(replace(#{param.endTime},'-',''),1,4),
            CASE
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,6) AND SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,6)
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
    </select>

    <!-- 根据机构编码获取机构名称 -->
    <select id="getOrgNameByOrgCode" resultType="java.lang.String">
        SELECT NAME FROM dws_ctf_sys_dept WHERE CODE = #{orgCode}
    </select>

    <!-- 按证照类型统计签发情况 -->
    <select id="statisticsByCertificateType" resultType="com.js.hszpt.dto.CertificateTypeStatisticsDTO">
        WITH RECURSIVE type_hierarchy AS (
        -- 初始查询：从根节点开始，标记根节点编码
        SELECT CERT_TYPE_DIR_ID,
        CERTIFICATE_TYPE_CODE,
        CERTIFICATE_TYPE_NAME,
        PARENT_ID,
        CERTIFICATE_TYPE_CODE AS first_type_code, -- 根节点的祖先编码为自己
        CERTIFICATE_TYPE_NAME AS first_type_name
        FROM dws_ctf_cert_type_directory
        WHERE PARENT_ID = '-1' -- 根据实际根节点特征调整
        UNION ALL
        -- 递归查询：继承父节点的祖先编码
        SELECT child.CERT_TYPE_DIR_ID,
        child.CERTIFICATE_TYPE_CODE,
        child.CERTIFICATE_TYPE_NAME,
        child.PARENT_ID,
        parent.first_type_code, -- 始终继承初始根节点编码
        parent.first_type_name
        FROM dws_ctf_cert_type_directory child
        INNER JOIN
        type_hierarchy parent
        ON child.PARENT_ID = parent.CERT_TYPE_DIR_ID)
        SELECT th.CERTIFICATE_TYPE_CODE        AS certificateTypeCode,
               th.CERTIFICATE_TYPE_NAME        AS certificateTypeName,
               th.first_type_code              AS parentTypeCode,
               COALESCE(SUM(i.issue_count), 0) AS issueCount
        FROM type_hierarchy th
        LEFT JOIN dws_ctf_certificate_issue i
        ON th.CERTIFICATE_TYPE_CODE = i.certificate_type_code
        AND i.certificate_status_code = #{param.statusCode}
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            AND i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN
            SUBSTR(REPLACE(#{param.startTime}, '-', ''), 1, 6)  <!-- 生成6位年月 -->
            AND
            SUBSTR(REPLACE(#{param.endTime}, '-', ''), 1, 6)     <!-- 生成6位年月 -->
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-',
            '')
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE type_tree AS (
            SELECT CERT_TYPE_DIR_ID, CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory
            WHERE CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            SELECT child.CERT_TYPE_DIR_ID, child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory child
            JOIN type_tree parent ON child.PARENT_ID = parent.CERT_TYPE_DIR_ID
            )
            SELECT 1 FROM type_tree
            WHERE CERTIFICATE_TYPE_CODE = th.CERTIFICATE_TYPE_CODE
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        GROUP BY th.CERTIFICATE_TYPE_CODE, th.CERTIFICATE_TYPE_NAME, th.first_type_code
        ORDER BY issueCount DESC
    </select>

    <!-- 按证照类型和状态统计签发情况 -->
    <select id="statisticsByCertificateTypeAndStatus" resultType="com.js.hszpt.dto.CertificateTypeStatusStatisticsDTO">
        WITH RECURSIVE type_hierarchy AS (
        -- 初始查询：从根节点开始，标记根节点编码
        SELECT CERT_TYPE_DIR_ID,
        CERTIFICATE_TYPE_CODE,
        CERTIFICATE_TYPE_NAME,
        PARENT_ID,
        CERTIFICATE_TYPE_CODE AS first_type_code, -- 根节点的祖先编码为自己
        CERTIFICATE_TYPE_NAME AS first_type_name
        FROM dws_ctf_cert_type_directory
        WHERE PARENT_ID = '-1' -- 根据实际根节点特征调整
        UNION ALL
        -- 递归查询：继承父节点的祖先编码
        SELECT child.CERT_TYPE_DIR_ID,
        child.CERTIFICATE_TYPE_CODE,
        child.CERTIFICATE_TYPE_NAME,
        child.PARENT_ID,
        parent.first_type_code, -- 始终继承初始根节点编码
        parent.first_type_name
        FROM dws_ctf_cert_type_directory child
        INNER JOIN
        type_hierarchy parent
        ON child.PARENT_ID = parent.CERT_TYPE_DIR_ID)
        SELECT
            etd.first_type_code as firstTypeCode,
            etd.first_type_name as firstTypeName,
            etd.certificate_type_code as certificateTypeCode,
            etd.CERTIFICATE_TYPE_NAME as certificateTypeName,
            COALESCE(SUM(i.issue_count), 0) as issueCount,
            SUM(CASE WHEN i.certificate_status_code = '1' THEN i.issue_count ELSE 0 END) as validCount,
            SUM(CASE WHEN i.certificate_status_code = '2' THEN i.issue_count ELSE 0 END) as invalidCount
        FROM type_hierarchy etd
        LEFT JOIN
        dws_ctf_certificate_issue i ON i.certificate_type_code = etd.CERTIFICATE_TYPE_CODE
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            and i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN
            SUBSTR(REPLACE(#{param.startTime}, '-', ''), 1, 6)  <!-- 生成6位年月 -->
            AND
            SUBSTR(REPLACE(#{param.endTime}, '-', ''), 1, 6)     <!-- 生成6位年月 -->
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-',
            '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY firstTypeCode,firstTypeName,certificateTypeCode, certificateTypeName
        ORDER BY issueCount DESC
    </select>

    <!-- 获取上一个周期按证照类型统计的签发量 -->
    <select id="getPreviousPeriodIssueCountByType" resultType="java.lang.Long">
        SELECT
        SUM(i.issue_count) as issueCount
        FROM
        dws_ctf_certificate_issue i
        WHERE i.certificate_type_code = #{certificateTypeCode}
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            AND i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <!-- Year 类型：用完整日期解析 -->
        <if test="param.timeType == 'year'">
            AND SUBSTR(i.ds_certificate_date, 1, 4) =
            TO_CHAR((
            TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD')  - INTERVAL '12 month'),'YYYY')
        </if>

        <!-- Quarter 类型：确保截取6位年月 -->
        <if test="param.timeType == 'quarter'">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN
            TO_CHAR((
            TO_DATE(SUBSTR(REPLACE(#{param.startTime}, '-', ''), 1, 6), 'YYYYMM') - INTERVAL '3 month'),'YYYYMM')
            AND
            TO_CHAR((TO_DATE(SUBSTR(REPLACE(#{param.endTime}, '-', ''), 1, 6), 'YYYYMM') - INTERVAL '3 month'),'YYYYMM')
        </if>

        <!-- Month 类型：用完整日期解析后计算 -->
        <if test="param.timeType == 'month'">
            AND SUBSTR(i.ds_certificate_date, 1, 6) =
            TO_CHAR((
            TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') - INTERVAL '1 month'),'YYYYMM')
        </if>

        <if test="param.timeType == 'day'">
            AND i.ds_certificate_date BETWEEN
            TO_CHAR(TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') -
            (TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD') - TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') + 1),
            'YYYYMMDD')
            AND
            TO_CHAR(TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD') - 1, 'YYYYMMDD')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
    </select>

    <!-- 按申请来源统计签发情况 -->
    <select id="statisticsByApplicationSource" resultType="com.js.hszpt.dto.ApplicationSourceStatisticsDTO">
        with applicate_type as (
        SELECT b.title, b.value FROM dws_ctf_dict a, dws_ctf_dict_data b
        WHERE a.type = 'apply_type' AND a.id = b.dict_id
        )
        SELECT
        a.value as applicationSourceCode,
        a.title as applicationSourceName,
        COALESCE(SUM(i.issue_count),0) as issueCount
        FROM applicate_type a left join (select * from dws_ctf_certificate_issue i
        WHERE 1=1
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            AND i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN SUBSTR(REPLACE(#{param.startTime},'-',''), 1, 4) AND SUBSTR(REPLACE(#{param.endTime},'-',''), 1, 4)
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(REPLACE(#{param.startTime},'-',''), 1, 4),
            CASE
            WHEN SUBSTR(REPLACE(#{param.startTime},'-',''), 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(REPLACE(#{param.startTime},'-',''), 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(REPLACE(#{param.startTime},'-',''), 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(REPLACE(#{param.startTime},'-',''), 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(REPLACE(#{param.endTime},'-',''), 1, 4),
            CASE
            WHEN SUBSTR(REPLACE(#{param.endTime},'-',''), 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(REPLACE(#{param.endTime},'-',''), 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(REPLACE(#{param.endTime},'-',''), 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(REPLACE(#{param.endTime},'-',''), 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,6) AND SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,6)
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>) i on a.value = i.application_source_code
        GROUP BY applicationSourceCode, applicationSourceName
        ORDER BY issueCount DESC
    </select>

    <select id="statisticsByTime" resultType="com.js.hszpt.dto.TimeStatisticsDTO">
        WITH
        <!-- 1. 定义时间维度参数（由外部传入 timeType/startTime/endTime） -->
        params AS (
        SELECT
        #{param.timeType} AS time_granularity,  <!-- 动态参数：year/quarter/month/day -->
        TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD')::date AS start_date,
        TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD')::date AS end_date,
        CAST(COALESCE(#{param.loginUserOrgCode}, '') AS VARCHAR) AS login_org_code
        ),
        <!-- 2. 生成日期序列（按粒度动态生成） -->
        date_series AS (
        SELECT
        generate_series(
        (SELECT start_date FROM params),
        (SELECT end_date FROM params),
        CASE (SELECT time_granularity FROM params)
        WHEN 'day' THEN '1 day'::interval
        WHEN 'month' THEN '1 month'::interval
        WHEN 'quarter' THEN '3 months'::interval
        WHEN 'year' THEN '1 year'::interval
        END
        )::date AS period_date
        ),
        <!-- 3. 格式化时间点（根据粒度生成展示文本） -->
        periods AS (
        SELECT
        CASE (SELECT time_granularity FROM params)
        WHEN 'day' THEN TO_CHAR(period_date, 'YYYY年MM月DD日')
        WHEN 'month' THEN TO_CHAR(period_date, 'YYYY年MM月')
        WHEN 'quarter' THEN
        TO_CHAR(period_date, 'YYYY') || '年' || EXTRACT(QUARTER FROM period_date) || '季'
        WHEN 'year' THEN TO_CHAR(period_date, 'YYYY年')
        END AS timePoint,
        CASE (SELECT time_granularity FROM params)
        WHEN 'day' THEN period_date
        WHEN 'month' THEN DATE_TRUNC('month', period_date)
        WHEN 'quarter' THEN DATE_TRUNC('quarter', period_date)
        WHEN 'year' THEN DATE_TRUNC('year', period_date)
        END AS period_date_trunc
        FROM date_series
        GROUP BY period_date_trunc, timePoint
        )
        <!-- 4. 主查询：关联时间序列并填充缺失数据为0 -->
        SELECT
        p.timePoint,
        COALESCE(SUM(i.issue_count), 0) AS issueCount
        FROM periods p
        LEFT JOIN dws_ctf_certificate_issue i
        ON p.period_date_trunc = CASE (SELECT time_granularity FROM params)
        WHEN 'day' THEN TO_DATE(i.ds_certificate_date, 'YYYYMMDD')::date
        WHEN 'month' THEN DATE_TRUNC('month', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')::date)
        WHEN 'quarter' THEN DATE_TRUNC('quarter', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')::date)
        WHEN 'year' THEN DATE_TRUNC('year', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')::date)
        END
        AND i.issuer_code LIKE CONCAT((SELECT login_org_code FROM params), '%')
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY p.timePoint
        ORDER BY p.timePoint, issueCount DESC
    </select>

    <!-- 获取证照状态名称 -->
    <select id="getCertificateStatusName" resultType="java.lang.String">
        SELECT b.title 
        FROM dws_ctf_dict a, dws_ctf_dict_data b
        WHERE a.type = 'CERTIFICATE_STATUS'
          AND a.id = b.dict_id
          AND b.value = #{statusCode}
    </select>

    <!-- 获取证照类型名称 -->
    <select id="getCertificateTypeName" resultType="java.lang.String">
        SELECT CERTIFICATE_TYPE_NAME 
        FROM dws_ctf_cert_type_directory 
        WHERE CERTIFICATE_TYPE_CODE = #{certificateTypeCode}
    </select>

    <select id="statisticsCertificateIssueByTimeType" resultType="com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO">
        WITH 
        params AS (
            SELECT 
                CAST(#{param.timeType} AS VARCHAR) AS time_granularity,
                CAST(#{param.startTime} AS DATE) AS start_date,
                CAST(#{param.endTime} AS DATE) AS end_date,
                CAST(COALESCE(#{param.loginUserOrgCode}, '') AS VARCHAR) AS login_org_code
        ),
        date_series AS (
            SELECT CAST(dt AS DATE) AS period_date
            FROM generate_series(
                (SELECT start_date FROM params),
                (SELECT end_date FROM params),
                CASE (SELECT time_granularity FROM params)
                    WHEN 'day' THEN INTERVAL '1 day'
                    WHEN 'month' THEN INTERVAL '1 month'
                    WHEN 'quarter' THEN INTERVAL '3 months'
                    WHEN 'year' THEN INTERVAL '1 year'
                END
            ) dt
        ),
        periods AS (
            SELECT 
                CASE (SELECT CAST(time_granularity AS VARCHAR) FROM params)
                    WHEN 'day' THEN CAST(TO_CHAR(period_date, 'YYYY') AS VARCHAR) || '年' || 
                                 CAST(TO_CHAR(period_date, 'MM') AS VARCHAR) || '月' ||
                                 CAST(TO_CHAR(period_date, 'DD') AS VARCHAR) || '日'
                    WHEN 'month' THEN CAST(TO_CHAR(period_date, 'YYYY') AS VARCHAR) || '年' || 
                                   CAST(TO_CHAR(period_date, 'MM') AS VARCHAR) || '月'
                    WHEN 'quarter' THEN CAST(TO_CHAR(period_date, 'YYYY') AS VARCHAR) || '年Q' || 
                                     CAST(EXTRACT(QUARTER FROM period_date) AS VARCHAR) || '季度'
                    WHEN 'year' THEN CAST(TO_CHAR(period_date, 'YYYY') AS VARCHAR) || '年'
                END AS timePoint,
                CASE (SELECT CAST(time_granularity AS VARCHAR) FROM params)
                    WHEN 'day' THEN period_date
                    WHEN 'month' THEN CAST(DATE_TRUNC('month', period_date) AS DATE)
                    WHEN 'quarter' THEN CAST(DATE_TRUNC('quarter', period_date) AS DATE)
                    WHEN 'year' THEN CAST(DATE_TRUNC('year', period_date) AS DATE)
                END AS period_date_trunc
            FROM date_series
            GROUP BY period_date_trunc, period_date
        )
        SELECT
            CAST(d.orgCode AS VARCHAR) AS orgCode,
            CAST(d.orgName AS VARCHAR) AS orgName,
            CAST(p.timePoint AS VARCHAR) AS timePoint,
            COALESCE(SUM(CAST(i.issue_count AS NUMERIC)), 0) AS issueCount
        FROM dws_ctf_certificate_issue i
        INNER JOIN (
            SELECT 
                CAST(msa_org_code AS VARCHAR) AS orgCode, 
                CAST(org_name AS VARCHAR) AS orgName 
            FROM dws_ctf_other_org
            <where>
                <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                    msa_org_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
                </if>
            </where>
            GROUP BY msa_org_code, org_name
        ) d ON i.issuer_name = d.orgName
        INNER JOIN periods p
            ON p.period_date_trunc = CASE (SELECT CAST(time_granularity AS VARCHAR) FROM params)
                WHEN 'day' THEN CAST(TO_DATE(i.ds_certificate_date, 'YYYYMMDD') AS DATE)
                WHEN 'month' THEN CAST(DATE_TRUNC('month', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')) AS DATE)
                WHEN 'quarter' THEN CAST(DATE_TRUNC('quarter', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')) AS DATE)
                WHEN 'year' THEN CAST(DATE_TRUNC('year', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')) AS DATE)
            END
        WHERE i.certificate_status_code = #{param.statusCode}
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
                SELECT 1 
                FROM dws_ctf_cert_type_directory parent
                LEFT JOIN dws_ctf_cert_type_directory child 
                    ON CAST(child.PARENT_ID AS VARCHAR) = CAST(parent.CERT_TYPE_DIR_ID AS VARCHAR)
                LEFT JOIN dws_ctf_cert_type_directory grandchild 
                    ON CAST(grandchild.PARENT_ID AS VARCHAR) = CAST(child.CERT_TYPE_DIR_ID AS VARCHAR)
                WHERE parent.CERT_TYPE_DIR_ID IN
                <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                    #{typeCode}
                </foreach>
                AND (
                    CAST(i.certificate_type_code AS VARCHAR) = CAST(parent.CERTIFICATE_TYPE_CODE AS VARCHAR)
                    OR CAST(i.certificate_type_code AS VARCHAR) = CAST(child.CERTIFICATE_TYPE_CODE AS VARCHAR)
                    OR CAST(i.certificate_type_code AS VARCHAR) = CAST(grandchild.CERTIFICATE_TYPE_CODE AS VARCHAR)
                )
            )
        </if>
        <if test="param.orgType != null and param.orgType != ''">
            AND i.org_type = #{param.orgType}
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (
                SELECT CAST(certificate_name AS VARCHAR)
                FROM dws_ctf_certificate_config
                WHERE certificate_type_code IN
                <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            )
        </if>
        GROUP BY d.orgCode, d.orgName, p.timePoint
        ORDER BY p.timePoint, issueCount DESC
    </select>

    <select id="statisticsCertificateIssueOrgByTimeType"
            resultType="com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO">
        WITH
        <!-- 1. 定义时间维度参数（由外部传入 timeType/startTime/endTime） -->
        params AS (
        SELECT
        #{param.timeType} AS time_granularity,  <!-- 动态参数：year/quarter/month/day -->
        TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD')::date AS start_date,
        TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD')::date AS end_date,
        CAST(#{param.loginUserOrgCode} AS VARCHAR) AS login_org_code
        ),
        <!-- 2. 生成日期序列（按粒度动态生成） -->
        date_series AS (
        SELECT
        generate_series(
        (SELECT start_date FROM params),
        (SELECT end_date FROM params),
        CASE (SELECT time_granularity FROM params)
        WHEN 'day' THEN '1 day'::interval
        WHEN 'month' THEN '1 month'::interval
        WHEN 'quarter' THEN '3 months'::interval
        WHEN 'year' THEN '1 year'::interval
        END
        )::date AS period_date
        ),
        <!-- 3. 格式化时间点（根据粒度生成展示文本） -->
        periods AS (
        SELECT
        CASE (SELECT time_granularity FROM params)
        WHEN 'day' THEN TO_CHAR(period_date, 'YYYY年MM月DD日')
        WHEN 'month' THEN TO_CHAR(period_date, 'YYYY年MM月')
        WHEN 'quarter' THEN TO_CHAR(period_date, 'YYYY') || '年' || EXTRACT(QUARTER FROM period_date) || '季'
        WHEN 'year' THEN TO_CHAR(period_date, 'YYYY年')
        END AS timePoint,
        CASE (SELECT time_granularity FROM params)
        WHEN 'day' THEN period_date
        WHEN 'month' THEN DATE_TRUNC('month', period_date)
        WHEN 'quarter' THEN DATE_TRUNC('quarter', period_date)
        WHEN 'year' THEN DATE_TRUNC('year', period_date)
        END AS period_date_trunc
        FROM date_series
        GROUP BY period_date_trunc, timePoint  <!-- 去重，确保唯一时间点 -->
        ),
        <!-- 4. 获取符合条件的部门列表 -->
        depts AS (
        SELECT
        CODE AS orgCode,
        NAME AS orgName
        FROM dws_ctf_sys_dept
        WHERE 1=1
        <choose>
            <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                AND PARENT_ID = (SELECT dept_id FROM dws_ctf_sys_dept WHERE code = (SELECT login_org_code FROM params))
                AND CODE LIKE CONCAT((SELECT login_org_code FROM params), '%')
            </when>
            <otherwise>
                AND GOV_LEVEL = '2'
            </otherwise>
        </choose>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        )
        <!-- 5. 主查询：关联部门、时间点，并填充缺失数据为0 -->
        SELECT
        d.orgCode,
        d.orgName,
        p.timePoint,
        COALESCE(SUM(i.issue_count), 0) AS issueCount
        FROM periods p
        CROSS JOIN depts d  <!-- 所有部门和时间组合 -->
        LEFT JOIN dws_ctf_certificate_issue i
        ON p.period_date_trunc = CASE (SELECT time_granularity FROM params)
        WHEN 'day' THEN TO_DATE(i.ds_certificate_date, 'YYYYMMDD')::date
        WHEN 'month' THEN DATE_TRUNC('month', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')::date)
        WHEN 'quarter' THEN DATE_TRUNC('quarter', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')::date)
        WHEN 'year' THEN DATE_TRUNC('year', TO_DATE(i.ds_certificate_date, 'YYYYMMDD')::date)
        END
        AND SUBSTR(i.issuer_code, 1, LENGTH(d.orgCode)) = d.orgCode
        AND i.certificate_status_code = #{param.statusCode}
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.orgType != null and param.orgType != ''">
            AND i.org_type = #{param.orgType}
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY d.orgCode, d.orgName, p.timePoint
        ORDER BY p.timePoint, issueCount DESC
    </select>
    <select id="getPreviousPeriodIssueCountByOrgCode" resultType="com.js.hszpt.dto.CertificateIssueDetailStatisticsDTO">
        WITH RECURSIVE
        sys_dept AS (
        <choose>
            <when test='param.orgType == "1"'>
                select NAME as orgName, CODE as orgCode from dws_ctf_sys_dept
                <where>
                    <choose>
                        <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                            AND PARENT_ID = (select dept_id from dws_ctf_sys_dept where code = #{param.loginUserOrgCode})
                            AND CODE LIKE CONCAT(#{param.loginUserOrgCode}, '%')
                        </when>
                        <otherwise>
                            AND GOV_LEVEL = '2'
                        </otherwise>
                    </choose>
                    <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
                        AND (
                        <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                            code LIKE CONCAT(#{issuerCode}, '%')
                        </foreach>
                        )
                    </if>
                </where>
            </when>
            <otherwise>
                select msa_org_code as orgCode,org_name as orgName from dws_ctf_other_org
                <where>
                    <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                        msa_org_code like CONCAT(#{param.loginUserOrgCode}, '%')
                    </if>
                </where>
                group by msa_org_code,org_name
            </otherwise>
        </choose>
        ),
        cte_child AS (
        SELECT etd_parent.CERT_TYPE_DIR_ID
        FROM dws_ctf_cert_type_directory etd_parent
        <where>
            <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
                etd_parent.CERT_TYPE_DIR_ID IN
                <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                    #{typeCode}
                </foreach>
            </if>
        </where>
        UNION ALL
        SELECT etd_child.CERT_TYPE_DIR_ID
        FROM dws_ctf_cert_type_directory etd_child
        INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
        ),
        certificate_types AS (
        SELECT DISTINCT etd.CERTIFICATE_TYPE_CODE
        FROM dws_ctf_cert_type_directory etd
        WHERE etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
        )
        select
        sd.orgCode as orgCode,
        sd.orgName as orgName,
        COALESCE(SUM(i.issue_count), 0) as issueCount,
        SUM(CASE WHEN i.application_source_code = '1' THEN i.issue_count ELSE 0 END) as onlineIssueCount, -- 在线申请
        SUM(CASE WHEN i.application_source_code = '2' THEN i.issue_count ELSE 0 END) as counterIssueCount -- 窗口申请
        from sys_dept sd
        <choose>
            <when test='param.orgType == "1"'>
                LEFT JOIN dws_ctf_certificate_issue i ON i.issuer_code like CONCAT(sd.orgCode,'%')
            </when>
            <otherwise>
                INNER JOIN dws_ctf_certificate_issue i ON sd.orgName = i.issuer_name
            </otherwise>
        </choose>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            JOIN certificate_types ct ON i.certificate_type_code = ct.CERTIFICATE_TYPE_CODE
        </if>
        <if test="param.orgType != null and param.orgType != ''">
            AND i.org_type = #{param.orgType}
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN SUBSTR(replace(#{param.startTime},'-',''),1,4) AND SUBSTR(replace(#{param.endTime},'-',''),1,4)
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(replace(#{param.startTime},'-',''),1,4),
            CASE
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(replace(#{param.startTime},'-',''),5,2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(replace(#{param.endTime},'-',''),1,4),
            CASE
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(replace(#{param.endTime},'-',''),5,2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,6) AND SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,6)
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY orgCode, orgName
    </select>

    <!-- 批量获取上一周期的签发数量 -->
    <select id="getPreviousPeriodIssueCountBatch" resultType="com.js.hszpt.dto.CertificateTypeStatusStatisticsDTO">
        WITH RECURSIVE type_hierarchy AS (
        -- 初始查询：从根节点开始，标记根节点编码
        SELECT CERT_TYPE_DIR_ID,
        CERTIFICATE_TYPE_CODE,
        CERTIFICATE_TYPE_NAME,
        PARENT_ID,
        CERTIFICATE_TYPE_CODE AS first_type_code, -- 根节点的祖先编码为自己
        CERTIFICATE_TYPE_NAME AS first_type_name
        FROM dws_ctf_cert_type_directory
        WHERE PARENT_ID = '-1' -- 根据实际根节点特征调整
        UNION ALL
        -- 递归查询：继承父节点的祖先编码
        SELECT child.CERT_TYPE_DIR_ID,
        child.CERTIFICATE_TYPE_CODE,
        child.CERTIFICATE_TYPE_NAME,
        child.PARENT_ID,
        parent.first_type_code, -- 始终继承初始根节点编码
        parent.first_type_name
        FROM dws_ctf_cert_type_directory child
        INNER JOIN
        type_hierarchy parent
        ON child.PARENT_ID = parent.CERT_TYPE_DIR_ID)
        SELECT
        etd.first_type_code as firstTypeCode,
        etd.first_type_name as firstTypeName,
        etd.certificate_type_code as certificateTypeCode,
        etd.CERTIFICATE_TYPE_NAME as certificateTypeName,
        COALESCE(SUM(i.issue_count), 0) as issueCount,
        SUM(CASE WHEN i.certificate_status_code = '1' THEN i.issue_count ELSE 0 END) as validCount,
        SUM(CASE WHEN i.certificate_status_code = '2' THEN i.issue_count ELSE 0 END) as invalidCount
        FROM type_hierarchy etd
        LEFT JOIN
        dws_ctf_certificate_issue i ON i.certificate_type_code = etd.CERTIFICATE_TYPE_CODE
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            and i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性获取所有初始父节点
            SELECT etd_parent.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM dws_ctf_cert_type_directory etd
            WHERE etd.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            AND etd.CERT_TYPE_DIR_ID IN (SELECT CERT_TYPE_DIR_ID FROM cte_child)
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.timeType == 'year'">
            AND TO_DATE(i.ds_certificate_date, 'YYYYMMDD') BETWEEN
            (TO_DATE(#{param.startTime}, 'YYYY-MM-DD') - INTERVAL '1 YEAR')
            AND
            (TO_DATE(#{param.endTime}, 'YYYY-MM-DD') - INTERVAL '1 YEAR')
        </if>
        <if test="param.timeType == 'month'">
            AND TO_DATE(i.ds_certificate_date, 'YYYYMMDD') BETWEEN
            (TO_DATE(#{param.startTime}, 'YYYY-MM-DD') - INTERVAL '1 MONTH')
            AND
            (TO_DATE(#{param.endTime}, 'YYYY-MM-DD') - INTERVAL '1 MONTH')
        </if>
        <if test="param.timeType == 'day'">
            AND TO_DATE(i.ds_certificate_date, 'YYYYMMDD') BETWEEN
            (TO_DATE(#{param.startTime}, 'YYYY-MM-DD') - INTERVAL '1 DAY')
            AND
            (TO_DATE(#{param.endTime}, 'YYYY-MM-DD') - INTERVAL '1 DAY')
        </if>
        <if test="param.timeType == 'quarter'">
            AND TO_DATE(i.ds_certificate_date, 'YYYYMMDD') BETWEEN
            (TO_DATE(#{param.startTime}, 'YYYY-MM-DD') - INTERVAL '3 MONTH')
            AND
            (TO_DATE(#{param.endTime}, 'YYYY-MM-DD') - INTERVAL '3 MONTH')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (select certificate_name from dws_ctf_certificate_config
            where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY firstTypeCode,firstTypeName,certificateTypeCode, certificateTypeName
        ORDER BY issueCount DESC
    </select>

</mapper> 
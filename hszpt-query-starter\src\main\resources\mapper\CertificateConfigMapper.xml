<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CertificateConfigMapper">
    <select id="getByCertificateCertificateNumber" resultType="com.js.hszpt.entity.CertificateConfig">
        SELECT CERTIFICATE_CN_EN,condition FROM dwdz_ctf_certificate_config
        WHERE CERTIFICATE_NAME in (select CERTIFICATE_NAME from dwdz_certificate_data where certificate_number =#{certificateNumber} ) order by condition desc NULLS LAST limit 1
    </select>
</mapper>
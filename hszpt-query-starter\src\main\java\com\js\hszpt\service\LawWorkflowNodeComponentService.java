package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.entity.LawWorkflowNodeComponent;
import com.js.hszpt.mapper.LawWorkflowNodeComponentDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
 
/**
 * 
 * @ClassName:  LawWorkflowNodeComponentService    
 * @Description:TODO(工作流前端节点组件展示配置表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class LawWorkflowNodeComponentService extends ServiceImpl<LawWorkflowNodeComponentDao,LawWorkflowNodeComponent> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<LawWorkflowNodeComponent> findByCondition(LawWorkflowNodeComponent param, SearchVo searchVo, PageVo pageVo) {
		Page<LawWorkflowNodeComponent> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<LawWorkflowNodeComponent> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<LawWorkflowNodeComponent>      
	 * @throws
	 */
	public List<LawWorkflowNodeComponent> findByCondition(LawWorkflowNodeComponent param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeComponent> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<LawWorkflowNodeComponent>      
	 * @throws
	 */
	private QueryWrapper<LawWorkflowNodeComponent> getCondition(LawWorkflowNodeComponent param, SearchVo searchVo){
		QueryWrapper<LawWorkflowNodeComponent> queryWrapper = new QueryWrapper<LawWorkflowNodeComponent>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}
}
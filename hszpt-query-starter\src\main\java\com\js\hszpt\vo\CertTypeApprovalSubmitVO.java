package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("证照类型目录审批提交VO")
public class CertTypeApprovalSubmitVO {

    @ApiModelProperty("审批ID")
    private String approvalId;

    @ApiModelProperty("证照类型目录ID")
    @NotBlank(message = "证照类型目录ID不能为空")
    private String certTypeDirId;
    
    @ApiModelProperty("操作类型：save-保存, submit-提交")
    @NotBlank(message = "操作类型不能为空")
    private String operationType;
    
    @ApiModelProperty("审批结果：1-通过, 2-不通过, 3-退回")
    @NotBlank(message = "审批结果不能为空")
    private String approvalResult;
    
    @ApiModelProperty("审批意见")
    @NotBlank(message = "审批意见不能为空")
    private String opinion;
    
    // 以下字段在后端处理中设置，前端无需传递
    @ApiModelProperty(hidden = true)
    private String operatorId;  // 操作人ID
    
    @ApiModelProperty(hidden = true)
    private String operatorName;  // 操作人姓名
    
    @ApiModelProperty(hidden = true)
    private String orgCode;  // 机构编码
    
    @ApiModelProperty(hidden = true)
    private String orgName;  // 机构名称
    
    @ApiModelProperty(hidden = true)
    private String orgLevel;  // 机构级别
} 
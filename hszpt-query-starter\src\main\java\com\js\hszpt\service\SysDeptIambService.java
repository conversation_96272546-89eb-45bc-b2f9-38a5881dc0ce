package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.mapper.SysDeptIambMapper;
import com.js.hszpt.vo.SysDeptInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class SysDeptIambService extends ServiceImpl<SysDeptIambMapper, SysDeptIamb> {

    @Autowired
    private SysDeptIambMapper sysDeptIambMapper;

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<SysDeptIamb> findByCondition(SysDeptIamb param, SearchVo searchVo, PageVo pageVo) {
		Page<SysDeptIamb> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<SysDeptIamb> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}

	/**
	 *
	 * @Title: findByCondition
	 * @Description: TODO(多条件列表查询)
	 * @param param
	 * @param searchVo
	 * @return  List<SysDeptIamb>
	 * @throws
	 */
	public List<SysDeptIamb> findByCondition(SysDeptIamb param, SearchVo searchVo){
		QueryWrapper<SysDeptIamb> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");

		return this.list(queryWrapper);
	}


	/**
	 *
	 * @Title: getCondition
	 * @Description: TODO(构建自定义查询条件)
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<SysDeptIamb>
	 * @throws
	 */
	private QueryWrapper<SysDeptIamb> getCondition(SysDeptIamb param, SearchVo searchVo){
		QueryWrapper<SysDeptIamb> queryWrapper = new QueryWrapper<SysDeptIamb>();
		//请根据实际字段自行扩展

		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;

	}

	/**
	 * 根据机构编码获取统一社会信用代码
	 * @param orgCode 机构编码
	 * @return 统一社会信用代码
	 */
	public String getUsccByOrgCode(String orgCode) {
		log.info("开始查询统一社会信用代码，机构编码：{}", orgCode);

		try {
			// 使用baseMapper直接查询
			QueryWrapper<SysDeptIamb> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("code", orgCode)
					.eq("del_flag", "0")
					.last("limit 1");

			// 使用baseMapper.selectList而不是getOne
			List<SysDeptIamb> list = baseMapper.selectList(queryWrapper);

			if (list != null && !list.isEmpty()) {
				String uscc = list.get(0).getUscc();
				log.info("查询到的统一社会信用代码：{}", uscc);
				return uscc;
			}

			log.warn("未查询到机构的统一社会信用代码，机构编码：{}", orgCode);
			return null;
		} catch (Exception e) {
			log.error("查询统一社会信用代码失败，机构编码：{}", orgCode, e);
			return null;
		}
	}

    public List<SysDeptInfoResponse> deptSon(String orgCode ,String flag) {
        SysDeptIamb sysDeptIamb = this.getSysDeptIamb(orgCode);
        if(null == sysDeptIamb){
            return null;
        }
        return sysDeptIambMapper.deptSon(sysDeptIamb.getDeptId(),flag);
    }

    /**
     * 通过机构编码获取机构信息
     * @param orgCode
     * @return
     */
    public SysDeptIamb getSysDeptIamb(String orgCode){
        SysDeptIamb sysDeptIamb = null;
        QueryWrapper<SysDeptIamb> queryWrapper = Wrappers.<SysDeptIamb>query();
        queryWrapper.lambda()
                .eq(SysDeptIamb::getCode, orgCode);
        queryWrapper.last("LIMIT 1");
        List<SysDeptIamb> list = this.list(queryWrapper);
        if(CollUtil.isNotEmpty(list)){
            sysDeptIamb = list.get(0);
        }
        return sysDeptIamb;
    }

	/**
	 * 根据机构编码查询机构信息
	 * @param orgCode 机构编码
	 * @return 机构信息
	 */
	public SysDeptIamb getByOrgCode(String orgCode) {
		log.info("开始查询机构信息，机构编码：{}", orgCode);

		if (StrUtil.isBlank(orgCode)) {
			log.warn("机构编码为空");
			return null;
		}

		try {
			QueryWrapper<SysDeptIamb> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("code", orgCode)
					.eq("del_flag", "0")  // 未删除
					.last("LIMIT 1");     // 只取一条

			SysDeptIamb dept = this.getOne(queryWrapper);

			if (dept == null) {
				log.warn("未找到机构信息，机构编码：{}", orgCode);
			} else {
				log.info("查询到机构信息：{}", dept);
			}

			return dept;
		} catch (Exception e) {
			log.error("查询机构信息失败，机构编码：{}", orgCode, e);
			throw new RuntimeException("查询机构信息失败：" + e.getMessage());
		}
	}


}
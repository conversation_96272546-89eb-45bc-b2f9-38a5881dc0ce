package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DictEgPort;
import com.js.hszpt.mapper.DictEgPortMapper;
import org.springframework.stereotype.Service;

@Service
public class DictEgPortService extends ServiceImpl<DictEgPortMapper, DictEgPort> {

    public DictEgPort getPortByName(String regportName) {
        QueryWrapper<DictEgPort> emiDictPortQuery = Wrappers.<DictEgPort>query();
        emiDictPortQuery.lambda().eq(DictEgPort::getPortName, regportName)
                .eq(DictEgPort::getValidFlagCode, "1")
                .isNotNull(DictEgPort::getPortNameEn)
                .last("LIMIT 1");
        return baseMapper.selectOne(emiDictPortQuery);
    }
}

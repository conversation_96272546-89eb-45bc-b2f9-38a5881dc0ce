package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("ship_info_all")
public class ShipInfoAll {

    @TableId
    private String shipCode;
    private String shipNo;
    private String shipRegNo;
    private String origShipRegNo;
    private String shipFirstregNo;
    private String shipId;
    private String shipInspectNo;
    private String shipImo;
    private String shipMmsi;
    private String shipCallsign;
    private String shipCardNo;
    private String shipName;
    private String shipNameEn;
    private String origShipName;
    private String origShipNameEn;
    private String shipRegionFlagCode;
    private String shipRouteCode;
    private String sailAreaCode;
    private String sailAreaName;
    private String regportCode;
    private String regportName;
    private String nationalityCode;
    private String nationality;
    private String shipTypeCode;
    private String shipTypeName;
    private String shipHullMaterialCode;
    private String shipHullMaterialName;
    private String shipValue;
    private Double shipLength;
    private Double shipBreadth;
    private Double shipDepth;
    private String shipGrosston;
    private String shipNetton;
    private String shipDwt;
    private String shipEngineTypeCode;
    private String shipEngineTypeName;
    private Integer shipEngineNum;
    private Double shipEnginePower;
    private String shipPropellerTypeCode;
    private String shipPropellerTypeName;
    private Integer shipPropellerNum;
    private Integer shipSlotNum;
    private Integer shipParkNum;
    private Integer shipPassengerNum;
    private Double shipSummerDraft;
    private String shipWindLevel;
    private Double shipMinFreeboard;
    private String origRegportName;
    private String shipyard;
    private String shipyardEn;
    private String shipBuiltAddr;
    private String shipBuiltAddrEn;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date shipBuiltDate;
    private String rebuiltShipyard;
    private String rebuiltShipyardEn;
    private String shipRebuiltAddr;
    private String shipRebuiltAddrEn;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date shipRebuiltDate;
    private String icCardNo;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date origDeletionDate;
    private String statusFlagCode;
    private String shipIdSealFlagCode;
    private String mortgageFlagCode;
    private String bareboatFlagCode;
    private String alterFlagCode;
    private String handoutCardFlagCode;
    private String financialLeaseFlagCode;
    private String hibernateFlagCode;
    private String trialShipFlagCode;
    private String detainFlagCode;
    private String permanentSealRemark;
    private String orgCode;
    private String orgName;
    private String creatorId;
    private String creatorOrgCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createDate;
    private String operatorId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date operateDate;
    private String operateFlagCode;
    private String remark;
    private String sourceCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date recModifyDate;
    private String msaOrgCode;
}

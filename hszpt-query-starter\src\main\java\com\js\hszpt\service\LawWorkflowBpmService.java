package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.entity.LawWorkflowBpm;
import com.js.hszpt.mapper.LawWorkflowBpmDao;
import com.js.hszpt.vo.CertTypeDirectoryQueryVO;
import com.js.hszpt.vo.CertTypeDirectoryTodoVO;
import com.js.hszpt.vo.TodoButtonPermissionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 
 * @ClassName:  LawWorkflowBpmService    
 * @Description:TODO(待办任务表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class LawWorkflowBpmService extends ServiceImpl<LawWorkflowBpmDao,LawWorkflowBpm> {
	@Autowired
	private LawWorkflowBpmDao lawWorkflowBpmDao;

	@Autowired
	private SysUserService sysUserService;

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<LawWorkflowBpm> findByCondition(LawWorkflowBpm param, SearchVo searchVo, PageVo pageVo) {
		Page<LawWorkflowBpm> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<LawWorkflowBpm> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<LawWorkflowBpm>      
	 * @throws
	 */
	public List<LawWorkflowBpm> findByCondition(LawWorkflowBpm param, SearchVo searchVo){
		QueryWrapper<LawWorkflowBpm> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<LawWorkflowBpm>      
	 * @throws
	 */
	private QueryWrapper<LawWorkflowBpm> getCondition(LawWorkflowBpm param, SearchVo searchVo){
		QueryWrapper<LawWorkflowBpm> queryWrapper = new QueryWrapper<LawWorkflowBpm>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	public Page<CertTypeDirectoryTodoVO> queryCertTypeDirectoryTodoList(CertTypeDirectoryQueryVO queryVO) {
		log.info("开始查询证照类型目录待办列表");
		
		try {
			// 1. 设置分页参数
			Page<CertTypeDirectoryTodoVO> page = new Page<>(queryVO.getPageNumber(), queryVO.getPageSize());
			
			// 2. 获取当前用户ID
			String currentUserId = sysUserService.getCurrUser().getId();
			
			// 3. 执行查询
			List<CertTypeDirectoryTodoVO> records = lawWorkflowBpmDao.queryCertTypeDirectoryTodoList(page, queryVO, currentUserId);
			
			// 4. 处理按钮权限
			if (records != null && !records.isEmpty()) {
				records.forEach(vo -> processButtonPermissions(vo, currentUserId));
			}
			
			// 5. 设置返回结果
			page.setRecords(records);
			
			log.info("查询证照类型目录待办列表完成，总记录数：{}", page.getTotal());
			return page;
			
		} catch (Exception e) {
			log.error("查询证照类型目录待办列表失败", e);
			throw new RuntimeException("查询证照类型目录待办列表失败", e);
		}
	}

	/**
	 * 处理按钮权限
	 * @param vo 待办任务VO
	 * @param currentUserId 当前用户ID
	 */
	private void processButtonPermissions(CertTypeDirectoryTodoVO vo, String currentUserId) {
		TodoButtonPermissionVO permission = new TodoButtonPermissionVO();
		
		// 1. 处理按钮：优先使用SQL标志，如果没有则判断userCode
		permission.setCanProcess("1".equals(vo.getShowProcessButton()) || 
							(vo.getUserCode() != null && currentUserId.equals(vo.getUserCode())));
		
		// 2. 查看按钮：优先使用SQL标志，如果没有则判断createBy和applicantId
		permission.setCanView("1".equals(vo.getShowViewButton()) || 
							currentUserId.equals(vo.getCreateBy()) || 
							currentUserId.equals(vo.getApplicantId()));
		
		vo.setButtonPermission(permission);
	}
}
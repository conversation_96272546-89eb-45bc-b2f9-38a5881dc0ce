package com.js.hszpt.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.CertificateData;
import com.js.hszpt.mapper.CertificateDataMapper;
import com.js.hszpt.service.CertificateDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 电子证照信息汇聚表Service实现类
 */
@Slf4j
@Service
public class CertificateDataServiceImpl extends ServiceImpl<CertificateDataMapper, CertificateData>
        implements CertificateDataService {

    @Override
    public CertificateData getByCertificateId(String certificateId) {
        return this.getOne(Wrappers.<CertificateData>lambdaQuery()
                .eq(CertificateData::getCertificateid, certificateId)
                .orderByDesc(CertificateData::getCreatetime)
                .last("limit 1"));
    }
}
package com.js.hszpt.dto;

import com.js.hszpt.entity.Certificate;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OilDamageDto extends Certificate {

    private String validDate1;
    private String validDate2;
    private String IssueDeptDate1;
    private String IssueDeptDate2;
    private String securityType1;
    private String securityType2;
    private String securityDate1;
    private String securityDate2;
    private String guarantorName1;
    private String guarantorName2;
    private String guarantorAddress1;
    private String guarantorAddress2;
    private String shipsName1;
    private String shipsName2;
    private String grossTonnage;
    private String shipsCode;
    private String IMOshipsCode;
    private String shipsArea1;
    private String shipsArea2;
    private String shipsAddress1;
    private String shipsAddress2;
    private String IssuedGovernment1;
    private String IssuedGovernment2;
    private String IssueDeptAddress1;
    private String IssueDeptAddress2;
    private String IssuePeople1;
    private String IssuePeople2;
    private String IssueJob1;
    private String IssueJob2;
    private String certificateNumber;
}

package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DictEgOrg;
import com.js.hszpt.mapper.DictEgOrgMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Wrapper;

@Slf4j
@Service
public class DictEgOrgService extends ServiceImpl<DictEgOrgMapper, DictEgOrg> {

    public DictEgOrg getDictEgOrgByOrgCode(String orgCode){
        QueryWrapper<DictEgOrg> queryWrapper = Wrappers.<DictEgOrg>query();
        queryWrapper.lambda()
                .eq(DictEgOrg::getCode,orgCode)
                .last("LIMIT 1");
        return this.baseMapper.selectOne(queryWrapper);
    }
}

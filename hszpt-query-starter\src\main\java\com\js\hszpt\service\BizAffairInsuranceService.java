package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.BizAffairInsurance;
import com.js.hszpt.mapper.BizAffairInsuranceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BizAffairInsuranceService extends ServiceImpl<BizAffairInsuranceMapper, BizAffairInsurance> {

    public BizAffairInsurance getByWindowApplyId(String windowApplyId) {
        QueryWrapper<BizAffairInsurance> queryWrapper = Wrappers.<BizAffairInsurance>query();
        queryWrapper.lambda()
                .eq(BizAffairInsurance::getApplyId, windowApplyId)
                .isNotNull(BizAffairInsurance::getEffStartDate)
                .orderByDesc(BizAffairInsurance::getRecCreateDate)
                .last("LIMIT 1");
        return this.getOne(queryWrapper,false);
    }
}

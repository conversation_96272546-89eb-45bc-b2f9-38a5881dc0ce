package com.js.hszpt.test;

import com.js.hszpt.utils.encrypt.Sm4Tool;
import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * CertificateService测试类
 */
@Slf4j
public class CertificateServiceTest {

    /**
     * 测试生成二维码URL的方法
     */
    public static void main(String[] args) {
        // 创建测试参数
        String certificateTypeCode = "0108";
        String id = "NH202311002";
        
        try {
            // 手动实现generateQrCodeUrl方法的逻辑
            String certId = certificateTypeCode + "_" + id;
            
            // 使用SM4加密
            String encryptedId = Sm4Tool.encrypt(certId, "5d3b282609644b4f8992b31a2e92f0f3");
            
            // URL编码
            String para = URLEncoder.encode(encryptedId, StandardCharsets.UTF_8.toString());
            
            // 假设scanUrl的值
            String scanUrl = "https://mjtest.jingsaitech.com/dzzz-prod-api/certificate/cert";
            
            // 生成二维码URL
            String qrCodeUrl = scanUrl + "?id=" + para;
            
            System.out.println("生成的二维码URL: " + qrCodeUrl);
            System.out.println("加密后的ID: " + encryptedId);
            System.out.println("URL编码后的参数: " + para);
        } catch (Exception e) {
            System.err.println("生成二维码URL失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
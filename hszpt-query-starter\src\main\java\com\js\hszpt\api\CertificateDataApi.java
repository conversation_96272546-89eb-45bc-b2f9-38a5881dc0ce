package com.js.hszpt.api;

import com.js.core.common.vo.Result;
import com.js.hszpt.entity.CertificateData;
import com.js.hszpt.service.CertificateDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * 电子证照信息汇聚表API接口
 */
@Slf4j
@RestController
@RequestMapping("/certificateData")
public class CertificateDataApi {

    @Autowired
    private CertificateDataService certificateDataService;

    /**
     * 根据dataid查询证照信息
     *
     * @param certificateId 证照id
     * @return 证照信息
     */
    @GetMapping("/getFilePathByDataId/{certificateId}")
    public Result<String> getFilePathByDataId(@PathVariable String certificateId) {
        try {
            long startTime = System.currentTimeMillis();
            log.info("开始根据dataid查询证照信息，dataid：{}", certificateId);
            CertificateData certificateData = certificateDataService.getByCertificateId(certificateId);
            String filePath = Optional.ofNullable(certificateData)
                    .map(CertificateData::getFilepath)
                    .orElse("");
            log.info("根据dataid查询证照信息完成，耗时：{}ms", System.currentTimeMillis() - startTime);
            return Result.success(filePath, "查询成功");
        } catch (Exception e) {
            log.error("根据dataid查询证照信息异常：", e);
            return Result.failed(500, "系统异常：" + e.getMessage());
        }
    }
}
package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;


/**
 * 
 * @ClassName:  CertTypeDirSeq   
 * @Description:TODO(证照类型代码序号表)   
 * @author:   System Generation 
 */
@Data
@Builder
@TableName("cert_type_dir_seq")
@ApiModel(value = "证照类型代码序号表")
public class CertTypeDirSeq{

    private static final long serialVersionUID = 1L;

    /**
    * 机构名称
    */
    @ApiModelProperty(value = "机构名称")
    private String orgName;


    /**
    * 机构编码
    */
    @ApiModelProperty(value = "机构编码")
    private String orgCode;


    /**
    * 序号值
    */
    @ApiModelProperty(value = "序号值")
    private Integer seqValue;

    
    /**
    * 序号值
    */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;


}
package com.js.hszpt.enmus;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum CertificateName {

    OIL_DAMAGE("油污损害民事责任保险或其他财务保证证书","CERTIFICATE OF INSURANCE OR OTHER FINANCIALSECURITY IN RESPECT OF CIVIL LIABILITY FOR OIL POLLUTION DAMAGE"),
    FUEL_POLLUTION("燃油污染损害民事责任保险或其他财务保证证书","CERTIFICATE OF INSURANCE OR OTHER FINANCIAL SECURITY IN RESPECT OF CIVIL LIABILITY FOR BUNKER OIL POLLUTION DAMAGE"),
    NON_PERSISTENT("非持久性油类污染损害民事责任保险或其他财务保证证书","CERTIFICATE OF INSURANCE OR OTHER FINANCIAL SECURITY IN RESPECT OF CIVIL LIABILITY FOR NON-PERSISTENT OIL POLLUTION DAMAGE"),
    DEBRIS("残骸清除责任保险或其他财务保证证书","CERTIFICATE OF INSURANCE OR OTHER FINANCIAL SECURITY IN RESPECT OF LIABILITY FOR THE REMOVAL OF WRECKS"),
    ;
    private String name;
    private String nameEn;

    public static CertificateName getCertificateByName(String name){
        return Arrays.stream(CertificateName.values())
                .filter(e->e.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
}

package com.js.hszpt.api;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.dto.*;
import com.js.hszpt.dto.TimeTypeSubOrgUsageStatisticsDTO;
import com.js.hszpt.service.DwsCertificateUsageService;
import com.js.hszpt.vo.CertificateUsageStatisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 证照使用统计API接口
 */
@Slf4j
@RestController
@RequestMapping("/certificate/usage/statistics")
@Api(tags = "证照使用统计接口")
@DS("dzzzdws")
public class CertificateUsageStatisticsApi {

    @Autowired
    private DwsCertificateUsageService dwsCertificateUsageService;

    /**
     * 按下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按下级机构统计结果
     */
    @PostMapping("/usageBySubOrg")
    @ApiOperation(value = "按下级机构统计使用情况", notes = "根据条件按下级机构统计证照使用情况")
    public Result<List<SubOrgUsageStatisticsDTO>> statisticsBySubOrg(@RequestBody CertificateUsageStatisticsVO param) {
        log.info("开始按下级机构统计证照使用情况，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 调用Service进行按下级机构统计
            List<SubOrgUsageStatisticsDTO> result = dwsCertificateUsageService.statisticsBySubOrg(param);
            log.info("证照使用情况按下级机构统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照使用情况按下级机构统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按不同时间维度和下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按不同时间维度和下级机构统计结果
     */
    @PostMapping("/usageByTimeTypeAndSubOrg")
    @ApiOperation(value = "按不同时间维度和下级机构统计使用情况", notes = "根据条件按不同时间维度和下级机构统计证照使用情况")
    public Result<Map<String, List<TimeTypeSubOrgUsageStatisticsDTO>>> statisticsByTimeTypeAndSubOrg(
            @RequestBody CertificateUsageStatisticsVO param) {
        log.info("开始按年份和下级机构统计证照使用情况，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        if (!StrUtil.equalsAny(param.getTimeType(), "year", "quarter", "month", "day")) {
            return ResultUtil.error("不支持该时间维度");
        }

        try {
            // 调用Service进行按年份和下级机构统计
            Map<String, List<TimeTypeSubOrgUsageStatisticsDTO>> result = dwsCertificateUsageService
                    .statisticsByTimeTypeAndSubOrg(param);
            log.info("证照使用情况按年份和下级机构统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照使用情况按年份和下级机构统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按年份和下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按年份和下级机构统计结果
     */
    @PostMapping("/usageByYearAndSubOrg")
    @ApiOperation(value = "按年份和下级机构统计使用情况", notes = "根据条件按年份和下级机构统计证照使用情况")
    public Result<Map<String, List<TimeTypeSubOrgUsageStatisticsDTO>>> statisticsByYearAndSubOrg(
            @RequestBody CertificateUsageStatisticsVO param) {
        log.info("开始按年份和下级机构统计证照使用情况，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 设置统计类型为年
        param.setTimeType("year");

        try {
            // 调用Service进行按年份和下级机构统计
            Map<String, List<TimeTypeSubOrgUsageStatisticsDTO>> result = dwsCertificateUsageService
                    .statisticsByTimeTypeAndSubOrg(param);
            log.info("证照使用情况按年份和下级机构统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照使用情况按年份和下级机构统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按下级机构统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按下级机构统计结果带占比
     */
    @PostMapping("/usageBySubOrgWithRatio")
    @ApiOperation(value = "按下级机构统计使用情况并计算占比", notes = "根据条件按下级机构统计证照使用情况并计算占比")
    public Result<List<SubOrgUsageRatioStatisticsDTO>> statisticsBySubOrgWithRatio(
            @RequestBody CertificateUsageStatisticsVO param) {
        log.info("开始按下级机构统计证照使用情况并计算占比，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 调用Service进行按下级机构统计并计算占比
            List<SubOrgUsageRatioStatisticsDTO> result = dwsCertificateUsageService.statisticsBySubOrgWithRatio(param);
            log.info("证照使用情况按下级机构统计并计算占比完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照使用情况按下级机构统计并计算占比异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按持证主体类别统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按持证主体类别统计结果带占比
     */
    @PostMapping("/usageByHolderCategoryWithRatio")
    @ApiOperation(value = "按持证主体类别统计使用情况并计算占比", notes = "根据条件按持证主体类别统计证照使用情况并计算占比")
    public Result<List<HolderCategoryUsageStatisticsDTO>> statisticsByHolderCategoryWithRatio(
            @RequestBody CertificateUsageStatisticsVO param) {
        log.info("开始按持证主体类别统计证照使用情况并计算占比，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 调用Service进行按持证主体类别统计并计算占比
            List<HolderCategoryUsageStatisticsDTO> result = dwsCertificateUsageService
                    .statisticsByHolderCategoryWithRatio(param);
            log.info("证照使用情况按持证主体类别统计并计算占比完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照使用情况按持证主体类别统计并计算占比异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按时间统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按时间统计结果带占比
     */
    @PostMapping("/usageByTimeWithRatio")
    @ApiOperation(value = "按时间统计使用情况并计算占比", notes = "根据条件按时间统计证照使用情况并计算占比")
    public Result<List<TimeUsageStatisticsDTO>> statisticsByTimeWithRatio(
            @RequestBody CertificateUsageStatisticsVO param) {
        log.info("开始按时间统计证照使用情况并计算占比，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 调用Service进行按时间统计并计算占比
            List<TimeUsageStatisticsDTO> result = dwsCertificateUsageService.statisticsByTimeWithRatio(param);
            log.info("证照使用情况按时间统计并计算占比完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照使用情况按时间统计并计算占比异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按证照类型统计核验情况
     * @param param 查询参数
     * @return 按证照类型统计
     */
    @PostMapping("/usageByType")
    @ApiOperation(value = "按证照类型统计核验情况", notes = "根据条件按证照类型统计证照核验情况")
    public Result<List<CertificateTypeUsageStatisticsDTO>> statisticsByType(
            @RequestBody CertificateUsageStatisticsVO param) {
        log.info("开始按证照类型统计证照使用情况，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态代码不能为空");
        }

        if (param.getUsageType() == null || param.getUsageType().isEmpty()) {
            return ResultUtil.error("使用类型不能为空");
        }

        if (!param.getUsageType().equals("1") && !param.getUsageType().equals("2")) {
            return ResultUtil.error("使用类型无效，支持的类型：1-下载 2-核验");
        }

        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }

        try {
            // 调用Service进行按证照类型统计并计算占比
            List<CertificateTypeUsageStatisticsDTO> result = dwsCertificateUsageService
                    .statisticsByType(param);
            log.info("证照使用情况按证照类型统计并计算占比完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照使用情况按证照类型统计并计算占比异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
}
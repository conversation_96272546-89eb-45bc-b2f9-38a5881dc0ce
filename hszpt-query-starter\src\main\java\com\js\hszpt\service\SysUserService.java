package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.common.entity.CurrentUser;
import com.js.common.service.SecurityService;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.entity.SysUser;
import com.js.hszpt.mapper.SysUserDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
 
/**
 * 
 * @ClassName:  SysUserService    
 * @Description:TODO(用户表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class SysUserService extends ServiceImpl<SysUserDao,SysUser> {

	@Autowired
	private SecurityService securityService;

	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<SysUser> findByCondition(SysUser param, SearchVo searchVo, PageVo pageVo) {
		Page<SysUser> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<SysUser> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<SysUser>      
	 * @throws
	 */
	public List<SysUser> findByCondition(SysUser param, SearchVo searchVo){
		QueryWrapper<SysUser> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<SysUser>      
	 * @throws
	 */
	private QueryWrapper<SysUser> getCondition(SysUser param, SearchVo searchVo){
		QueryWrapper<SysUser> queryWrapper = new QueryWrapper<SysUser>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	/**
	 * 获取当前登录用户，
	 * @return
	 */
	public CurrentUser getCurrUser(){
		CurrentUser user = securityService.getCurrUser();
		return user;
	}

	/**
	 * 获取当前登录用户机构编码
	 * @return
	 */
	public String getCurrUserOrgCode(){
		CurrentUser user = securityService.getCurrUser();
		return user.getDepartmentCode();
	}


}
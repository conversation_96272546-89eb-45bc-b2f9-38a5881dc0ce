package com.js.hszpt.enmus;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum CertificateTypeCode {

    OIL_DAMAGE("油污损害民事责任保险或其他财务保证证书","0100"),
    OIL_DAMAGE_GJ("油污损害民事责任保险或其他财务保证证书（国际）","0100_GJ"),   //临时判断使用
    FUEL_POLLUTION("燃油污染损害民事责任保险或其他财务保证证书","0101"),
    FUEL_POLLUTION_GJ("燃油污染损害民事责任保险或其他财务保证证书（国际）","0101_GJ"),   //临时判断使用
    NON_PERSISTENT("非持久性油类污染损害民事责任保险或其他财务保证证书","0102"),
    DEBRIS("残骸清除责任保险或其他财务保证证书","0103"),
    ;
    private String name;
    private String typeCode;

    public static CertificateTypeCode getCertificateByName(String name){
        return Arrays.stream(CertificateTypeCode.values())
                .filter(e->e.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
}

package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 按钮权限VO类
 */
@Data
@ApiModel("按钮权限VO")
public class ButtonPermissionVO {
    @ApiModelProperty("是否可删除，待提交的办件才可以删除")
    private Boolean canDelete;
    
    @ApiModelProperty("是否可查看，每行记录都会展示查看按钮，跟审批状态和下发状态无关")
    private Boolean canView;
    
    @ApiModelProperty("是否可废止，下发状态为已下发才显示这个按钮")
    private Boolean canAbolish;
    
    @ApiModelProperty("是否可启用，下发状态为已废止状态才显示这个按钮")
    private Boolean canEnable;
    
    @ApiModelProperty("是否可重新提交，审批状态为不通过状态才显示这个按钮")
    private Boolean canResubmit;
    
    @ApiModelProperty("是否可提交审批，待提交的草稿状态记录才显示这个按钮")
    private Boolean canSubmitApproval;
    
    @ApiModelProperty("是否可撤回，审批状态为待审批才显示这个按钮")
    private Boolean canRevoke;
    
    /**
     * 是否可以编辑
     */
    private Boolean canEdit;
} 
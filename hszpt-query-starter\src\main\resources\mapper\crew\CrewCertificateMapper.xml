<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.crew.CrewCertificateMapper">
    <select id="selectCrewCertificates" resultType="com.js.hszpt.vo.CrewCertificateQueryVo">
        SELECT
            ROW_NUMBER() OVER(ORDER BY a.rec_create_date DESC) as serialNo,
            a.data_id as dataId,
            a.certificate_id as certificateId,
            a.certificate_type_name as certificateType,
            a.certificate_type_code as certificateTypeCode,
            a.certificate_define_authority_name as certificateDefineAuthorityName,
            a.certificate_define_authority_code as certificateDefineAuthorityCode,
            a.related_item_name as relatedItemName,
            a.related_item_code as relatedItemCode,
            a.certificate_holder_category as certificateHolderCategory,
            a.certificate_holder_category_name as nationality,
            a.validity_range as validityRange,
            a.certificate_identifier as certificateIdentifier,
            (case
                when a.certificate_name = '海船船员内河航线行驶资格证明' then '海船船员适任证书（内河水域航线签注）'
                when a.certificate_name = '游艇驾驶证（海上）' then '海上游艇操作人员适任证书'
                when a.certificate_name = '游艇驾驶证（内河）' then '内河游艇操作人员适任证书'
                when a.certificate_name = '小型海船适任证书' then '小型海船船员适任证书'
                else a.certificate_name
            end) as certificateName,
            a.certificate_number as certificateNumber,
            a.certificate_issuing_authority_name as issueType,
            a.certificate_issuing_authority_code as certificateIssuingAuthorityCode,
            a.certificate_issued_date as issueDate,
            a.certificate_holder_name as holderName,
            a.certificate_holder_code as holderCode,
            a.certificate_holder_type_name as certificateHolderTypeName,
            a.certificate_effective_date as effectiveDate,
            a.certificate_expiring_date as expiryDate,
            a.issue_dept_code2 as trainingOrg,
            a.issue_dept_code3 as foreignOrg,
            a.certificate_area_code as certificateAreaCode,
            (case 
                WHEN a.certificate_status = '-2' THEN '无效'
                WHEN a.certificate_expiring_date::timestamp >= current_timestamp THEN '有效' 
                ELSE '无效' 
            END) as certificateStatus,
            (case 
                WHEN a.certificate_status = '-2' THEN 'Invalid'
                WHEN a.certificate_expiring_date::timestamp >= current_timestamp THEN 'Effective' 
                ELSE 'Invalid' 
            end) as certificateStatusEn,
            a.creator_id as creatorId,
            a.create_time as createTime,
            a.operator_id as operatorId,
            a.update_time as updateTime,
            a.file_path as filePath,
            a.sync_status as syncStatus,
            a.remarks as remarks,
            a.dept_id as deptId,
            a.apply_num as applyNum,
            a.affair_type as affairType,
            a.serve_business as serveBusiness,
            a.affair_id as affairId,
            a.affair_num as affairNum,
            a.sort_name as sortName,
            a.source_code as sourceCode,
            a.rec_create_date as recCreateDate,
            a.rec_modify_date as recModifyDate,
            a.certificate_effective_date_en as certificateEffectiveDateEn,
            a.certificate_expiring_date_en as certificateExpiringDateEn,
            a.certificate_issued_date_en as certificateIssuedDateEn,
            a.training_issue_Dates_Cn as  trainingUnitName,
            <!-- 当displaySource=2(app核验)时，对职务字段进行#后缀处理 -->
            <choose>
                <when test="query.displaySource == 2">
                    (CASE
                        WHEN a.certificate_name = '内河船舶船员适任证书' THEN a.crew_type
                        WHEN a.crew_type IS NULL THEN NULL
                        WHEN a.crew_type LIKE '%#%' THEN a.crew_type
                        ELSE a.crew_type || '#'
                    END) as crewType,

                    (CASE
                        WHEN a.certificate_name = '内河船舶船员适任证书' THEN a.crew_type_en
                        WHEN a.crew_type_en IS NULL THEN NULL
                        WHEN a.crew_type_en LIKE '%#%' THEN a.crew_type_en
                        ELSE a.crew_type_en || '#'
                    END) as crewTypeEn
                </when>
                <otherwise>
                    a.crew_type as crewType,
                    a.crew_type_en as crewTypeEn
                </otherwise>
            </choose>,
            a.applivations_en as applivationsEn,
            a.auth_authority_cn as authAuthorityCn,
            a.auth_authority_en as authAuthorityEn,
            a.eva_org_cn as evaOrgCn,
            a.eva_org_en as evaOrgEn,
            a.train_manager_name_cn as trainManagerNameCn,
            a.train_manager_name_en as trainManagerNameEn,
            a.representative_cn as representativeCn,
            a.representative_en as representativeEn,
            <!-- 当displaySource=2(app核验)时，对指定字段进行#后缀处理 -->
            <choose>
                <when test="query.displaySource == 2">
                    (CASE
                        WHEN a.training_names_cn IS NULL THEN NULL
                        WHEN a.training_names_cn LIKE '%#%' THEN a.training_names_cn
                        ELSE a.training_names_cn || '#'
                    END) as trainingName,

                    (CASE
                        WHEN a.training_names_en IS NULL THEN NULL
                        WHEN a.training_names_en LIKE '%#%' THEN a.training_names_en
                        ELSE a.training_names_en || '#'
                    END) as trainingNameEn,

                    (CASE
                        WHEN a.training_issue_dates_cn IS NULL THEN NULL
                        WHEN a.training_issue_dates_cn LIKE '%#%' THEN a.training_issue_dates_cn
                        ELSE a.training_issue_dates_cn || '#'
                    END) as trainingDateOfIssue,

                    (CASE
                        WHEN a.training_issue_dates_en IS NULL THEN NULL
                        WHEN a.training_issue_dates_en LIKE '%#%' THEN a.training_issue_dates_en
                        ELSE a.training_issue_dates_en || '#'
                    END) as trainingDateOfIssueEn,

                    (CASE
                        WHEN a.training_effective_dates_cn IS NULL THEN NULL
                        WHEN a.training_effective_dates_cn LIKE '%#%' THEN a.training_effective_dates_cn
                        ELSE a.training_effective_dates_cn || '#'
                    END) as trainingEffectiveDate,

                    (CASE
                        WHEN a.training_effective_dates_en IS NULL THEN NULL
                        WHEN a.training_effective_dates_en LIKE '%#%' THEN a.training_effective_dates_en
                        ELSE a.training_effective_dates_en || '#'
                    END) as trainingEffectiveDateEn
                </when>
                <otherwise>
                    a.training_names_cn as trainingName,
                    a.training_names_en as trainingNameEn,
                    a.training_issue_dates_cn as trainingDateOfIssue,
                    a.training_issue_dates_en as trainingDateOfIssueEn,
                    a.training_effective_dates_cn as trainingEffectiveDate,
                    a.training_effective_dates_en as trainingEffectiveDateEn
                </otherwise>
            </choose>,
            a.msa_org_code as msaOrgCode,
            a.birth as birthDate,
            a.name_en as nameEn,
            a.country_cn as countryCn,
            a.country_en as countryEn,
            a.sign_dept_en as signDeptEn,
            a.applivations_cn as applivationsCn,
            b.certificate_name_en as certificateNameEn,
            c.description as certificateStatusEn,
            b.CERTIFICATE_CN_EN as certificateCnEn,
            b.condition as condition,
            a.certificate_holder_name as certificateHolderName,
            -- 统一日期格式YYYY-MM-DD
                CASE
                WHEN a.birth IS NULL THEN NULL
                ELSE
                TO_CHAR(
                CASE
                -- 标准日期格式 YYYY-MM-DD
                WHEN CAST(a.birth AS TEXT) ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' THEN
                TO_DATE(a.birth, 'YYYY-MM-DD')

                -- 没有前导零的日期格式 YYYY-M-D, YYYY-MM-D, YYYY-M-DD
                WHEN CAST(a.birth AS TEXT) ~ '^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$' THEN
                TO_DATE(a.birth, 'YYYY-MM-DD')

                -- 中文日期格式 YYYY年M月D日
                WHEN CAST(a.birth AS TEXT) ~ '^[0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日$' THEN
                TO_DATE(
                SUBSTRING(CAST(a.birth AS TEXT), 1, 4) || '-' ||
                SUBSTRING(CAST(a.birth AS TEXT) FROM '年([0-9]{1,2})月') || '-' ||
                SUBSTRING(CAST(a.birth AS TEXT) FROM '月([0-9]{1,2})日'),
                'YYYY-MM-DD'
                )

                -- 纯数字格式 YYYYMMDD
                WHEN CAST(a.birth AS TEXT) ~ '^[0-9]{8}$' THEN
                TO_DATE(a.birth, 'YYYYMMDD')

                -- 其他情况尝试提取数字并补零
                ELSE
                TO_DATE(
                REGEXP_REPLACE(CAST(a.birth AS TEXT), '[^0-9]', '', 'g'),
                'YYYYMMDD'
                )
                END,
                'YYYY-MM-DD'
                )
                END AS birth,
            a.birth_en as birthEn,
            a.certificate_issued_date as certificateIssuedDate,
            a.certificate_expiring_date as certificateExpiringDate,
            a.certificate_issuing_authority_name as certificateIssuingAuthorityName,
            a.cert_print_no as certPrintNo,
            a.training_institution_code trainingInstitutionCode,
            a.training_location trainingLocation,
            a.certificate_type_name as certificateTypeName,
            a.certificate_status as statusFlag
        FROM dwdz_certificate_data a
        LEFT JOIN dwdz_ctf_certificate_config b ON a.certificate_name = b.certificate_name
        LEFT JOIN (
            SELECT b.value, b.title, b.description
            FROM dwdz_ctf_dict a
            JOIN dwdz_ctf_dict_data b ON a.id = b.dict_id
            WHERE a.type = 'certificate_status'
        ) c ON a.certificate_status = c.value
        <where>
            a.certificate_name not in ('中华人民共和国船舶国籍证书_中英文','中华人民共和国临时船舶国籍证书_中英文',
            '中华人民共和国临时船舶国籍证书_中文','中华人民共和国船舶国籍证书_中文','油污损害民事责任保险或其他财务保证证书（国内）',
            '油污损害民事责任保险或其他财务保证证书（国际）','燃油污染损害民事责任保险或其他财务保证证书（国内）',
            '燃油污染损害民事责任保险或其他财务保证证书（国际）','非持久性油类污染损害民事责任保险或其他财务保证证书',
            '残骸清除责任保险或其他财务保证证书')
            <if test="query.certificateId != null and query.certificateId != ''">
                AND a.certificate_id = #{query.certificateId}
            </if>
            <if test="query.certificateNumber != null and query.certificateNumber != ''">
                AND a.certificate_number = #{query.certificateNumber}
            </if>
            <if test="query.birth != null and query.birth != ''">
                AND (TO_CHAR(
                CASE
                -- 标准日期格式 YYYY-MM-DD
                WHEN CAST(a.birth AS TEXT) ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' THEN
                TO_DATE(a.birth, 'YYYY-MM-DD')

                -- 没有前导零的日期格式 YYYY-M-D, YYYY-MM-D, YYYY-M-DD
                WHEN CAST(a.birth AS TEXT) ~ '^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}$' THEN
                TO_DATE(a.birth, 'YYYY-MM-DD')

                -- 中文日期格式 YYYY年M月D日
                WHEN CAST(a.birth AS TEXT) ~ '^[0-9]{4}年[0-9]{1,2}月[0-9]{1,2}日$' THEN
                TO_DATE(
                SUBSTRING(CAST(a.birth AS TEXT), 1, 4) || '-' ||
                SUBSTRING(CAST(a.birth AS TEXT) FROM '年([0-9]{1,2})月') || '-' ||
                SUBSTRING(CAST(a.birth AS TEXT) FROM '月([0-9]{1,2})日'),
                'YYYY-MM-DD'
                )

                -- 纯数字格式 YYYYMMDD
                WHEN CAST(a.birth AS TEXT) ~ '^[0-9]{8}$' THEN
                TO_DATE(a.birth, 'YYYYMMDD')

                -- 其他情况尝试提取数字并补零
                ELSE
                TO_DATE(
                REGEXP_REPLACE(CAST(a.birth AS TEXT), '[^0-9]', '', 'g'),
                'YYYYMMDD'
                )
                END,
                'YYYYMMDD'
                ) = replace(#{query.birth}, '-', '') or a.birth is null)
            </if>
            <if test="query.certificateTypeCode != null and query.certificateTypeCode != ''">
                AND a.certificate_type_code = #{query.certificateTypeCode}
            </if>
            <if test="query.certificateHolderName != null and query.certificateHolderName != ''">
                AND a.certificate_holder_name = #{query.certificateHolderName}
            </if>
            <if test="query.certificateHolderCode != null and query.certificateHolderCode != ''">
                AND a.certificate_holder_code = #{query.certificateHolderCode}
            </if>
            <!-- 证照名称列表 -->
            <if test="query.certificateName != null and query.certificateName != ''">
                AND a.certificate_name IN
                <foreach collection="query.certificateName.split(',')" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
            <!-- 签发机构代码 -->
            <if test="query.issueOrgCode != null and query.issueOrgCode != ''">
                AND (
                        <foreach collection="query.issueOrgCode.split(',')" item="code" separator="OR">
                            a.issue_dept_code3 LIKE '%' || #{code} || '%'
                        </foreach>
                    )
            </if>
            <if test="query.validFromDate != null and query.validFromDate != ''">
                AND a.certificate_effective_date &gt;= #{query.validFromDate}
            </if>
            <if test="query.validToDate != null and query.validToDate != ''">
                AND a.certificate_expiring_date &lt;= #{query.validToDate}
            </if>
            <!--证书过期-->
             <if test="query.certStartDate != null and query.certStartDate != '' and query.certEndDate != null and query.certEndDate != ''">
                 AND certificate_expiring_date
                     BETWEEN
                        TO_CHAR(TO_TIMESTAMP(#{query.certStartDate} || ' 00:00:00', 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                     AND
                        TO_CHAR(TO_TIMESTAMP(#{query.certEndDate} || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
             </if>
            <if test="query.certificateStatus != null and query.certificateStatus != ''">
                <choose>
                    <when test="query.certificateStatus == 1">
                        AND TO_TIMESTAMP(a.certificate_effective_date, 'YYYY-MM-DD HH24:MI:SS') &lt;= CURRENT_TIMESTAMP
                        AND TO_TIMESTAMP(a.certificate_expiring_date, 'YYYY-MM-DD HH24:MI:SS') &gt;= CURRENT_TIMESTAMP
                    </when>
                    <when test="query.certificateStatus == 2">
                        AND (TO_TIMESTAMP(a.certificate_effective_date, 'YYYY-MM-DD HH24:MI:SS') &gt; CURRENT_TIMESTAMP
                        OR TO_TIMESTAMP(a.certificate_expiring_date, 'YYYY-MM-DD HH24:MI:SS') &lt; CURRENT_TIMESTAMP)
                    </when>
                </choose>
            </if>
            <if test="query.notCert != null and query.notCert != '' and query.notCert == 1">
                AND a.certificate_name not in ('船员培训质量管理体系证书',
                                               '海员外派机构资质证书',
                                               '船员培训许可证',
                                               '海船船员培训许可证',
                                               '内河船员培训许可证')
            </if>

        </where>
        ORDER BY a.certificate_issued_date DESC
        -- 外网查询多条记录的时候中英文证书排前面
        <if test="query.displaySource == 1">
            ,b.CERTIFICATE_CN_EN DESC
        </if>
    </select>

    <!-- 内网船员证书查询 -->
    <select id="certificateIntranetCrewQueryAuth" resultType="com.js.hszpt.vo.CertificateIntranetCrewQueryVo">
        SELECT
            ROW_NUMBER() OVER(ORDER BY a.certificate_issued_date DESC) as serialNo,
            a.certificate_id as certificateId,
            a.data_id as dataId,
            a.certificate_number as certificateNumber,
            (CASE
            WHEN a.certificate_status = '-2' THEN '无效'
            WHEN (CASE
            WHEN a.certificate_expiring_date ~ '^\d{4}-\d{2}-\d{2}$' THEN
            TO_DATE(a.certificate_expiring_date, 'YYYY-MM-DD')
            WHEN a.certificate_expiring_date ~ '^\d{8}$' THEN
            TO_DATE(a.certificate_expiring_date, 'YYYYMMDD')
            WHEN a.certificate_expiring_date ~ '^\d{4}年\d{2}月\d{2}日$' THEN
            TO_DATE(REGEXP_REPLACE(a.certificate_expiring_date, '年|月|日', ''), 'YYYYMMDD')
            ELSE NULL
            END) &gt;= current_timestamp THEN '有效'
            ELSE '无效' END) certificateStatus,
            a.certificate_holder_name as holderName,
            a.certificate_holder_code as holderIdentityNumber,
            a.certificate_issued_date as birthDate,
            a.certificate_define_authority_code as certificateDefineAuthorityCode,
            a.certificate_define_authority_name as certificateDefineAuthorityName,
            a.certificate_type_code as certificateTypeCode,
            a.certificate_type_name as certificateTypeName,
            a.certificate_issuing_authority_name as issuingAuthority,
            a.issue_dept_code2 as trainingOrg,
            a.issue_dept_code3 as foreignOrg,
            a.certificate_define_authority_name as mainSignOrg,
            a.certificate_issued_date as issuingDate,
            a.certificate_effective_date as validFromDate,
            a.certificate_expiring_date as validToDate,
            (case
                when a.certificate_name = '海船船员内河航线行驶资格证明' then '海船船员适任证书（内河水域航线签注）'
                when a.certificate_name = '游艇驾驶证（海上）' then '海上游艇操作人员适任证书'
                when a.certificate_name = '游艇驾驶证（内河）' then '内河游艇操作人员适任证书'
                when a.certificate_name = '小型海船适任证书' then '小型海船船员适任证书'
                else a.certificate_name
            end) as certificateName,
            a.country_cn as countryCn,
            a.country_en as countryEn,
            a.certificate_holder_name as certificateHolderName,
            -- 统一日期格式YYYY-MM-DD
            CASE
                WHEN COALESCE(a.birth, '') = '' THEN NULL
                WHEN a.birth ~ '^(\d{4}-\d{2}-\d{2})$' AND
                (regexp_replace(a.birth, '[^\d]', '', 'g'))::DATE IS NOT NULL
                    THEN TO_DATE(a.birth, 'YYYY-MM-DD')::TEXT
                WHEN a.birth ~ '^(\d{8})$' AND
                    a.birth::DATE IS NOT NULL
                    THEN TO_CHAR(TO_DATE(a.birth, 'YYYYMMDD'), 'YYYY-MM-DD')
                WHEN a.birth ~ '^\d{4}年\d{1,2}月\d{1,2}日$' THEN
                    TO_CHAR(TO_DATE(REGEXP_REPLACE(a.birth, '年|月|日', '', 'g'), 'YYYYMMDD'), 'YYYY-MM-DD')
                END::TEXT AS birth,
            a.certificate_issued_date as certificateIssuedDate,
            a.certificate_expiring_date as certificateExpiringDate,
            a.certificate_issuing_authority_name as certificateIssuingAuthorityName,
            a.cert_print_no as certPrintNo,
            a.crew_type as crewType,
            a.certificate_effective_date_en as certificateEffectiveDateEn,
            a.certificate_expiring_date_en as certificateExpiringDateEn,
            a.certificate_issued_date_en as certificateIssuedDateEn,
            a.crew_type_en as crewTypeEn,
            a.applivations_en as applivationsEn,
            a.auth_authority_cn as authAuthorityCn,
            a.auth_authority_en as authAuthorityEn,
            a.eva_org_cn as evaOrgCn,
            a.eva_org_en as evaOrgEn,
            a.training_names_cn as trainingNamesCn,
            a.training_names_en as trainingNamesEn,
            a.train_manager_name_cn as trainManagerNameCn,
            a.train_manager_name_en as trainManagerNameEn,
            a.representative_cn as representativeCn,
            a.representative_en as representativeEn,
            a.training_issue_dates_cn as trainingIssueDatesCn,
            a.training_issue_dates_en as trainingIssueDatesEn,
            a.training_effective_dates_cn as trainingEffectiveDatesCn,
            a.training_effective_dates_en as trainingEffectiveDatesEn
        FROM dwdz_certificate_data a
        <if test="dto.certificateTypeCodes != null and dto.certificateTypeCodes.size() > 0">
            JOIN (
                SELECT DISTINCT etd.CERTIFICATE_TYPE_CODE
                FROM dwdz_ctf_cert_type_directory etd
                WHERE etd.CERT_TYPE_DIR_ID IN (
                    SELECT etd_child.CERT_TYPE_DIR_ID
                    FROM dwdz_ctf_cert_type_directory etd_child
                    WHERE etd_child.CERT_TYPE_DIR_ID IN
                    <foreach collection="dto.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                        #{typeCode}
                    </foreach>
                    UNION
                    SELECT etd_child.CERT_TYPE_DIR_ID
                    FROM dwdz_ctf_cert_type_directory etd_child
                    WHERE etd_child.PARENT_ID IN (
                        SELECT etd_parent.CERT_TYPE_DIR_ID
                        FROM dwdz_ctf_cert_type_directory etd_parent
                        WHERE etd_parent.CERT_TYPE_DIR_ID IN
                        <foreach collection="dto.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                            #{typeCode}
                        </foreach>
                    )
                )
            ) ct ON a.certificate_type_code = ct.CERTIFICATE_TYPE_CODE
        </if>
        <where>
            <if test='dto.sourceType == "1" '>
                AND a.certificate_name not in ('中华人民共和国船舶国籍证书_中英文','中华人民共和国临时船舶国籍证书_中英文',
                '中华人民共和国临时船舶国籍证书_中文','中华人民共和国船舶国籍证书_中文','油污损害民事责任保险或其他财务保证证书（国内）',
                '油污损害民事责任保险或其他财务保证证书（国际）','燃油污染损害民事责任保险或其他财务保证证书（国内）',
                '燃油污染损害民事责任保险或其他财务保证证书（国际）','非持久性油类污染损害民事责任保险或其他财务保证证书',
                '残骸清除责任保险或其他财务保证证书')
            </if>
            <if test="dto.shipName != null and dto.shipName != ''">
                AND a.ship_name like '%' || #{dto.shipName} || '%'
            </if>
            <if test="dto.certTypeDirCodes != null and dto.certTypeDirCodes.size() > 0">
                AND a.certificate_type_code in
                <foreach item="certTypeDirCode" collection="dto.certTypeDirCodes" open="(" separator="," close=")">
                    #{certTypeDirCode}
                </foreach>
            </if>
            <if test="dto.certificateTypeCode != null and dto.certificateTypeCode != ''">
                AND a.certificate_name in (select CERTIFICATE_NAME from dwdz_ctf_certificate_config dccc where dccc.CERTIFICATE_TYPE_CODE in
                <foreach item="typeCode" index="index" collection="dto.certificateTypeCode.split(',')" open="(" separator="," close=")">
                    #{typeCode}
                </foreach>
                )
            </if>
            <if test="dto.certificateNum != null and dto.certificateNum != ''">
                AND a.certificate_number like '%' || #{dto.certificateNum} || '%'
            </if>
           <if test="dto.birthDateStart != null and dto.birthDateStart != ''
                     and dto.birthDateEnd != null and dto.birthDateEnd != ''">
               -- 统一转换为YYYYMMDD格式进行比较
               AND CASE
               WHEN COALESCE(a.birth, '') = '' THEN NULL
               WHEN a.birth ~ '^(\d{4}-\d{2}-\d{2})$' AND
               (regexp_replace(a.birth, '[^\d]', '', 'g'))::DATE IS NOT NULL
               THEN TO_DATE(a.birth, 'YYYY-MM-DD')::DATE
               WHEN a.birth ~ '^(\d{8})$' AND
               a.birth::DATE IS NOT NULL
               THEN TO_DATE(a.birth, 'YYYYMMDD')
               WHEN a.birth ~ '^\d{4}年\d{1,2}月\d{1,2}日$' THEN
               TO_DATE(REGEXP_REPLACE(a.birth, '年|月|日', '', 'g'), 'YYYYMMDD')
               END::DATE BETWEEN
               TO_DATE(#{dto.birthDateStart}, 'YYYY-MM-DD HH24:MI:SS')
               AND
               TO_DATE(#{dto.birthDateEnd}, 'YYYY-MM-DD HH24:MI:SS')
           </if>
            <if test="dto.statCertTypeCode != null and dto.statCertTypeCode != ''">
                AND a.certificate_type_code in (
                    select CERTIFICATE_TYPE_CODE from dwdz_ctf_cert_type_directory where PARENT_ID = (
                        select CERT_TYPE_DIR_ID from dwdz_ctf_cert_type_directory where certificate_type_code = #{dto.statCertTypeCode}
                    )
                )
            </if>
            <if test="dto.birth != null and dto.birth != ''">
                AND replace(a.birth,'-','') = replace(#{dto.birth},'-','')
            </if>
            <if test="dto.holderName != null and dto.holderName != ''">
                AND a.certificate_holder_name like '%' || #{dto.holderName} || '%'
            </if>
            <if test="dto.holderIdentityNumber != null and dto.holderIdentityNumber != ''">
                AND a.certificate_holder_code like '%' || #{dto.holderIdentityNumber} || '%'
            </if>
            <if test="dto.certificateHolderCategory != null and dto.certificateHolderCategory != ''">
                AND a.certificate_holder_category = #{dto.certificateHolderCategory}
            </if>
            <if test="dto.certPrintNo != null and dto.certPrintNo != ''">
                AND a.cert_print_no like '%' || #{dto.certPrintNo} || '%'
            </if>
            <if test="dto.permitNumber != null and dto.permitNumber != ''">
                AND a.certificate_number like '%' || #{dto.permitNumber} || '%'
            </if>
            <if test="dto.issuingAuthority != null and dto.issuingAuthority != ''">
                AND a.certificate_issuing_authority_name = #{dto.issuingAuthority}
            </if>
            <if test="dto.issueDeptCode2 != null and dto.issueDeptCode2 != ''">
                AND a.issue_dept_code2 like concat(#{dto.issueDeptCode2},'%')
            </if>
            <if test="dto.issuOrgCode3 != null and dto.issuOrgCode3 != ''">
                AND a.issue_dept_code3 in
                <foreach item="issuOrgCode3" index="index" collection="dto.issuOrgCode3.split(',')" open="(" separator="," close=")">
                    #{issuOrgCode3}
                </foreach>
            </if>
            <if test="dto.loginOrgCode != null and dto.loginOrgCode != ''">
                AND (a.issue_dept_code3 like concat(#{dto.loginOrgCode},'%')
                    or a.certificate_issuing_authority_name in
                    (select org_name from dwdz_ctf_other_org where msa_org_code like #{dto.loginOrgCode} || '%'))
            </if>
            <if test="dto.trainingOrg != null and dto.trainingOrg != ''">
                AND a.certificate_issuing_authority_name = #{dto.trainingOrg}
            </if>
            <if test="dto.foreignOrg != null and dto.foreignOrg != ''">
                AND a.certificate_issuing_authority_name = #{dto.foreignOrg}
            </if>
            <if test="dto.mainSignOrg != null and dto.mainSignOrg != ''">
                AND a.certificate_issuing_authority_name = #{dto.mainSignOrg}
            </if>
            <if test="dto.relatedMatterName != null and dto.relatedMatterName != ''">
                AND a.related_item_name = #{dto.relatedMatterName}
            </if>
             <if test="dto.issueDateStart != null and dto.issueDateStart != '' and dto.issueDateEnd != null and dto.issueDateEnd != ''">
                AND a.certificate_issued_date BETWEEN TO_CHAR(TO_DATE(#{dto.issueDateStart}, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                AND TO_CHAR(TO_DATE(#{dto.issueDateEnd}, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
            </if>
            <if  test="dto.effectDateStart != null and dto.effectDateStart != '' and dto.effectDateEnd != null and dto.effectDateEnd != ''">
                AND a.certificate_effective_date BETWEEN TO_CHAR(TO_DATE(#{dto.effectDateStart}, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                AND TO_CHAR(TO_DATE(#{dto.effectDateEnd}, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
            </if>
            <if  test="dto.expireDateStart != null and dto.expireDateStart != '' and dto.expireDateEnd != null and dto.expireDateEnd != ''">
                AND a.certificate_expiring_date BETWEEN TO_CHAR(TO_DATE(#{dto.expireDateStart}, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
                AND TO_CHAR(TO_DATE(#{dto.expireDateEnd}, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD')
            </if>
            <if test="dto.statusFlag != null and dto.statusFlag != ''">
                <choose>
                    <when test='dto.statusFlag == "1"'>
                        AND TO_TIMESTAMP(a.certificate_effective_date, 'YYYY-MM-DD HH24:MI:SS') &lt;= CURRENT_TIMESTAMP
                        AND TO_TIMESTAMP(a.certificate_expiring_date, 'YYYY-MM-DD HH24:MI:SS') &gt;= CURRENT_TIMESTAMP
                    </when>
                    <when test='dto.statusFlag == "2"'>
                        AND (TO_TIMESTAMP(a.certificate_effective_date, 'YYYY-MM-DD HH24:MI:SS') &gt; CURRENT_TIMESTAMP
                        OR TO_TIMESTAMP(a.certificate_expiring_date, 'YYYY-MM-DD HH24:MI:SS') &lt; CURRENT_TIMESTAMP)
                    </when>
                </choose>
            </if>
            <if test="dto.orgType != null and dto.orgType != ''">
                <if test="dto.orgCode != null and dto.orgCode != ''">
                    <choose>
                        <when test='dto.orgType == "1"'>
                            and a.issue_dept_code3 like concat(#{dto.orgCode},'%')
                            and a.certificate_type_name not in ('船上厨师培训合格证明','船上膳食服务辅助人员培训证明','船员培训质量管理体系证书','海船船员健康证明')
                        </when>
                        <when test='dto.orgType == "2"'>
                            and a.issue_dept_code3 = #{dto.orgCode}
                            and a.certificate_type_name in ('船上厨师培训合格证明','船上膳食服务辅助人员培训证明','船员培训质量管理体系证书','海船船员健康证明')
                        </when>
                    </choose>
                </if>
                <if test="dto.orgCode == null or dto.orgCode == ''">
                    <choose>
                        <when test='dto.orgType == "1"'>
                            --数据权限控制
                            and a.certificate_type_name not in ('船上厨师培训合格证明','船上膳食服务辅助人员培训证明','船员培训质量管理体系证书','海船船员健康证明')
                        </when>
                        <when test='dto.orgType == "2"'>
                            and a.certificate_type_name in ('船上厨师培训合格证明','船上膳食服务辅助人员培训证明','船员培训质量管理体系证书','海船船员健康证明')
                        </when>
                    </choose>
                </if>
            </if>
        </where>
        ORDER BY a.certificate_issued_date DESC
    </select>

    <!-- 查询内河船舶船员培训合格证的培训项目 -->
    <select id="queryTrainingProjects" resultType="com.js.hszpt.vo.TrainingProjectVo">
        SELECT
            STRING_AGG(i.project_name, '#') || '#' as projectNames,
            STRING_AGG(i.sign_date, '#') || '#' as projectIssuedDate,
            STRING_AGG(i.end_date, '#') || '#' as projectExpiringDate
        FROM dwdz_ctf_cert_dtl_nhpxhg n
        JOIN dwdz_ctf_cert_dtl_nhpxhg_item i ON n.nhpxhg_id = i.nhpxhg_id
        WHERE n.data_id = #{dataId}
    </select>

    <!-- 查询证书展示配置 -->
    <select id="queryVerificationDisplayConfigs" resultType="com.js.hszpt.entity.VerificationDisplayConfig">
        SELECT
            CONFIG_ID as configId,
            CERTIFICATE_TYPE_CODE as certificateTypeCode,
            DATA_SOURCE_TABLE_NAME as dataSourceTableName,
            CHINESE_LABEL_NAME as chineseLabelName,
            ENGLISH_LABEL_NAME as englishLabelName,
            CHINESE_DATA_FIELD_NAME as chineseDataFieldName,
            ENGLISH_DATA_FIELD_NAME as englishDataFieldName,
            CHINESE_DATA_DEFAULT_VALUE as chineseDataDefaultValue,
            ENGLISH_DATA_DEFAULT_VALUE as englishDataDefaultValue,
            HIGHLIGHT as highlight,
            SORT_ORDER as sortOrder,
            DISPLAY_SOURCE as displaySource
        FROM dwdz_ctf_verification_display_config
        WHERE
        <choose>
            <when test='certificateTypeName == "通用证照配置"'>
                CERTIFICATE_TYPE_CODE = '99999' AND DISPLAY_SOURCE = #{displaySource}
            </when>
            <when test='certificateTypeName == "通用证照配置-中英文"'>
                CERTIFICATE_TYPE_CODE = '99998' AND DISPLAY_SOURCE = #{displaySource}
            </when>
            <otherwise>
                CERTIFICATE_TYPE_CODE = (select CERTIFICATE_TYPE_CODE from dwdz_ctf_certificate_config where certificate_name = #{certificateTypeName})
                AND DISPLAY_SOURCE = #{displaySource}
            </otherwise>
        </choose>
        ORDER BY SORT_ORDER ASC
    </select>
</mapper>

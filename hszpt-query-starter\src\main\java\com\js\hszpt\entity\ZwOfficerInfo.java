package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 官员信息表实体
 */
@Data
@TableName("OFFICER_INFO")
public class ZwOfficerInfo implements Serializable {

    @TableId(value = "OFFICER_INFO_ID")
    private String officerInfoId;

    /**
     * 姓名
     */
    @TableField("OFFICER_NAME")
    private String officerName;

    /**
     * 性别
     */
    @TableField("SEX")
    private String sex;

    /**
     * 所在单位
     */
    @TableField("DEPARTMENT")
    private String department;

    /**
     * 职位
     */
    @TableField("POSITION")
    private String position;

    /**
     * 所在机构代码
     */
    @TableField("MECHANISM")
    private String mechanism;

    /**
     * 扩展字段1
     */
    @TableField("COL1")
    private String col1;

    /**
     * 扩展字段2
     */
    @TableField("COL2")
    private String col2;

    /**
     * 扩展字段3
     */
    @TableField("COL3")
    private String col3;

    /**
     * 扩展字段4(保存所在单位英文信息)
     */
    @TableField("COL4")
    private String col4;

    /**
     * 官员姓名英文
     */
    @TableField("OFFICIAL_NAME_EN")
    private String officialNameEn;

    /**
     * 职务英文
     */
    @TableField("OFFICIAL_TITLE_EN")
    private String officialTitleEn;

    /**
     * 创建人
     */
    @TableField("CREATE_OPER_ID")
    private String createOperId;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_DATE")
    private Date createDate;

    /**
     * 修改人
     */
    @TableField("MODIFY_OPER_ID")
    private String modifyOperId;

    /**
     * 修改时间（处理列名拼写不一致）
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("MODITY_DATE")
    private Date modifyDate;

    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("DEL_FLAG")
    private String delFlag;

    /**
     * CDCDATE时间戳
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CDCDATE")
    private Date cdcDate;
}
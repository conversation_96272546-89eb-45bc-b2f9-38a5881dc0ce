package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  SysDeptIamb   
 * @Description:TODO(组织信息表)   
 * @author:   System Generation 
 */
@Data
@Builder
@TableName("sys_dept_iamb")
@ApiModel(value = "组织信息表")
public class SysDeptIamb {

    private static final long serialVersionUID = 1L;


    /**
    * DEPT_ID
    */
    @TableId(value = "DEPT_ID")
    @ApiModelProperty(value = "DEPT_ID")
    private String deptId;


    /**
    * 部门名称
    */
    @ApiModelProperty(value = "部门名称")
    private String name;


    /**
    * 排序
    */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @TableField("CREATE_TIME")
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date createTime;

    /**
    * 是否删除  -1：已删除  0：正常
    */
    @ApiModelProperty(value = "是否删除  -1：已删除  0：正常")
    private String delFlag;


    /**
    * 父级id
    */
    @ApiModelProperty(value = "父级id")
    private String parentId;


    /**
    * 层级
    */
    @ApiModelProperty(value = "层级")
    private String govLevel;


    /**
    * 编码
    */
    @ApiModelProperty(value = "编码")
    private String code;


    /**
    * 是否展示 1是 2否
    */
    @ApiModelProperty(value = "是否展示 1是 2否")
    private String isShow;


    /**
    * 大厅展示 1
    */
    @ApiModelProperty(value = "大厅展示 1")
    private String hallShow;


    /**
    * 统一社会信用代码
    */
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;


    /**
    * 办理时间
    */
    @ApiModelProperty(value = "办理时间")
    private String handleDate;


    /**
    * 办理地点
    */
    @ApiModelProperty(value = "办理地点")
    private String approveAddress;


    /**
    * 咨询方式
    */
    @ApiModelProperty(value = "咨询方式")
    private String consultType;


    /**
    * 监督投诉方式
    */
    @ApiModelProperty(value = "监督投诉方式")
    private String superviseType;


    /**
    * 是否中心1:是
    */
    @ApiModelProperty(value = "是否中心1:是")
    private String isCenter;


    /**
    * 部门办公联系电话
    */
    @ApiModelProperty(value = "部门办公联系电话")
    private String deptPhone;


    /**
    * 实施编码
    */
    @ApiModelProperty(value = "实施编码")
    private String taskCode;


    /**
    * PARENT_CODE
    */
    @ApiModelProperty(value = "PARENT_CODE")
    private String parentCode;


    /**
    * DEPT_TYPE
    */
    @ApiModelProperty(value = "DEPT_TYPE")
    private String deptType;


    /**
    * 首字母缩写
    */
    @ApiModelProperty(value = "首字母缩写")
    private String nameAbbr;


    /**
    * 机构标识代码类
    */
    @ApiModelProperty(value = "机构标识代码类")
    private String codesetId;


    /**
    * 0:虚拟组织 1:真实组织
    */
    @ApiModelProperty(value = "0:虚拟组织 1:真实组织")
    private String orgTag;


    /**
    * 组织机构简称
    */
    @ApiModelProperty(value = "组织机构简称")
    private String orgCcname;


    /**
    * 组织机构描述
    */
    @ApiModelProperty(value = "组织机构描述")
    private String orgDesc;


    /**
    * 组织机构代码
    */
    @ApiModelProperty(value = "组织机构代码")
    private String orgCertifit;


    /**
    * 机构类型
    */
    @ApiModelProperty(value = "机构类型")
    private String zsOrgType;


    /**
    * 老机构ID
    */
    @ApiModelProperty(value = "老机构ID")
    private String oldDeptId;


    /**
    * 区域编码唯一标识
    */
    @ApiModelProperty(value = "区域编码唯一标识")
    private String areaId;


    /**
    * 4A机构层级
    */
    @ApiModelProperty(value = "4A机构层级")
    private String grade;

    /**
     * 机构统一社会信用代码
     */
    @ApiModelProperty(value = "机构统一社会信用代码")
    private String uscc;


}
package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.SysDeptEn;
import com.js.hszpt.mapper.SysDeptEnMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SysDeptEnService extends ServiceImpl<SysDeptEnMapper, SysDeptEn> {

    public SysDeptEn getSysDeptEn(String code) {
        QueryWrapper<SysDeptEn> queryWrapper = Wrappers.query();
        queryWrapper.lambda()
                .eq(SysDeptEn::getOrgCode, code)
                .last("LIMIT 1");
        return baseMapper.selectOne(queryWrapper);
    }
}

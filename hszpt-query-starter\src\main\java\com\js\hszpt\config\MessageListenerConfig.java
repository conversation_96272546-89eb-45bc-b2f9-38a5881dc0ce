package com.js.hszpt.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
public class MessageListenerConfig {

    /**
     * 禁用 Redis 消息监听器容器
     */
    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory connectionFactory) {
        // 返回一个空的容器，不注册任何监听器
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        // 禁用容器
        container.setSubscriptionExecutor(null);
        container.setTaskExecutor(null);
        return container;
    }
} 
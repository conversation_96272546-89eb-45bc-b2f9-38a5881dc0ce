package com.js.hszpt.constants;

import cn.hutool.core.map.MapUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 残骸清除责任保险或其他财务保证证书 证照照面属性信息
 */

public class OilDamageInsuranceCertConstant {

    public static final Map<String,String> OIL_DAMAGE_INSURANCE_CERT_MAP;

    public static final Map<String,String> OIL_DAMAGE_INSURANCE_REVERSET_MAP;

    static {
        OIL_DAMAGE_INSURANCE_CERT_MAP = MapUtil.builder(new HashMap<String,String>())
                .put("结束时间(中文)", "vaildDate1")
                .put("X海事局(中文)", "IssuedGovernment1")
                .put("地点(中文)", "IssueDeptAddress1")
                .put("颁证日期(中文)", "IssueDeptDate1")
                .put("签证官员(中文)", "IssuePeople1")
                .put("签证官员职务(中文)", "IssueJob1")
                .put("中文船名", "shipsName1")
                .put("总吨", "grossTonnage")
                .put("船舶编号", "shipsCode")
                .put("IMO编号", "IMOshipsCode")
                .put("船籍港(英文)", "shipsArea2")
                .put("船舶所有人和地址(英文)", "shipsAddress2")
                .put("保险类别(中文)", "securityType1")
                .put("保险时间(中文)", "securityDate1")
                .put("保险机构名称(中文)", "guarantorName1")
                .put("保险机构地址(中文)", "guarantorAddress1")
                .put("证照编号", "widthCode")
                .put("结束时间(英文)", "vaildDate2")  //validDate2 调整为 vaildDate2
                .put("X海事局(英文)", "IssuedGovernment2")
                .put("地点(英文)", "IssueDeptAddress2")
                .put("颁证日期(英文)", "IssueDeptDate2")
                .put("签证官员(英文)", "IssuePeople2")
                .put("签证官员职务(英文)", "IssueJob2")
                .put("保险类别(英文)", "securityType2")
                .put("保险时间(英文)", "securityDate2")
                .put("保险机构名称(英文)", "guarantorName2")
                .put("保险机构地址(英文)", "guarantorAddress2")
                .build();
        OIL_DAMAGE_INSURANCE_REVERSET_MAP = MapUtil.reverse(OIL_DAMAGE_INSURANCE_CERT_MAP);
    }
}

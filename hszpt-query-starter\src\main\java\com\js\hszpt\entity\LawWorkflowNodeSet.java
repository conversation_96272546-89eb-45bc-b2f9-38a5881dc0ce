package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 
 * @ClassName:  LawWorkflowNodeSet   
 * @Description:TODO(工作流模板配置表)   
 * @author:   System Generation 
 */
@Data

@TableName("LAW_WORKFLOW_NODE_SET")
@ApiModel(value = "工作流模板配置表")
public class LawWorkflowNodeSet extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "LAW_WORKFLOW_NODE_SET_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
     * 模板编码
     */
    @TableField("ITEMS_CODE")
    @ApiModelProperty(value = "模板编码")
    private String itemsCode;


    /**
    * 模板名称
    */
    @ApiModelProperty(value = "模板名称")
    private String itemsName;


    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createOperId;


    /**
    * 创建部门
    */
    @ApiModelProperty(value = "创建部门")
    private String createOperDept;


    /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String modifyOperId;


    /**
    * 修改部门
    */
    @ApiModelProperty(value = "修改部门")
    private String modifyOperDept;


    /**
    * 修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;


    /**
    * 版本号
    */
    @ApiModelProperty(value = "版本号")
    private String recordVersion;


}
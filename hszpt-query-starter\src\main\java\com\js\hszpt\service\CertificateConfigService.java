package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.CertificateConfig;
import com.js.hszpt.mapper.CertificateConfigMapper;
import org.springframework.stereotype.Service;

@Service
public class CertificateConfigService extends ServiceImpl<CertificateConfigMapper, CertificateConfig> {

    public CertificateConfig getByCertificateCertificateNumber(String certificateNumber) {
        return baseMapper.getByCertificateCertificateNumber(certificateNumber);
    }

    public CertificateConfig getByCertificateCertificateName(String certificateName) {
        return this.getOne(Wrappers.<CertificateConfig>lambdaQuery()
                .select(CertificateConfig::getCertificateCnEn)
                .eq(CertificateConfig::getCertificateName, certificateName)
                .last("limit 1"));
    }
}

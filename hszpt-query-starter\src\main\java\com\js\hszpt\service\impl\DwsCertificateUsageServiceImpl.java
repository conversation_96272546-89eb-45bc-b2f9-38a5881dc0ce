package com.js.hszpt.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.dto.*;
import com.js.hszpt.dto.TimeTypeSubOrgUsageStatisticsDTO;
import com.js.hszpt.entity.DwsCertificateUsage;
import com.js.hszpt.mapper.DwsCertificateUsageMapper;
import com.js.hszpt.service.DwsCertificateUsageService;
import com.js.hszpt.vo.CertificateUsageStatisticsVO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 电子证照使用情况统计Service实现类
 */
@Service
@DS("dzzzdws")
public class DwsCertificateUsageServiceImpl extends ServiceImpl<DwsCertificateUsageMapper, DwsCertificateUsage>
        implements DwsCertificateUsageService {

    /**
     * 按下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按下级机构统计结果
     */
    @Override
    public List<SubOrgUsageStatisticsDTO> statisticsBySubOrg(CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按下级机构统计
        return baseMapper.statisticsBySubOrg(param);
    }

    /**
     * 按时间维度和下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按时间维度和下级机构统计结果
     */
    @Override
    public Map<String, List<TimeTypeSubOrgUsageStatisticsDTO>> statisticsByTimeTypeAndSubOrg(
            CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按时间维度和下级机构统计
        List<TimeTypeSubOrgUsageStatisticsDTO> timeTypeSubOrgUsageStatisticsDTOS = baseMapper
                .statisticsByTimeTypeAndSubOrg(param);
        Map<String, List<TimeTypeSubOrgUsageStatisticsDTO>> map = timeTypeSubOrgUsageStatisticsDTOS.stream()
                .collect(Collectors.groupingBy(TimeTypeSubOrgUsageStatisticsDTO::getTimePoint));
        return map;
    }

    /**
     * 按下级机构统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按下级机构统计结果带占比
     */
    @Override
    public List<SubOrgUsageRatioStatisticsDTO> statisticsBySubOrgWithRatio(CertificateUsageStatisticsVO param) {
        // 获取总使用量
        Long totalUsageCount = baseMapper.getTotalUsageCount(param);

        // 调用Mapper进行按下级机构统计
        List<SubOrgUsageRatioStatisticsDTO> detailList = baseMapper.statisticsBySubOrgWithRatio(param);

        // 创建结果列表
        List<SubOrgUsageRatioStatisticsDTO> resultList = new ArrayList<>();

        // 创建汇总数据
        SubOrgUsageRatioStatisticsDTO summaryData = new SubOrgUsageRatioStatisticsDTO();
        summaryData.setOrgCode(param.getLoginUserOrgCode());
        summaryData.setOrgName("总计");
        summaryData.setUsageCount(totalUsageCount);
        summaryData.setUsageRatio(100.0); // 汇总数据占比100%
        summaryData.setIsSummary(true);

        // 计算各下级机构的占比
        if (detailList != null && !detailList.isEmpty() && totalUsageCount != null && totalUsageCount > 0) {
            for (SubOrgUsageRatioStatisticsDTO detail : detailList) {
                // 计算占比
                double usageRatio = detail.getUsageCount() * 100.0 / totalUsageCount;
                detail.setUsageRatio(Math.round(usageRatio * 100) / 100.0);
            }
        }

        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);

        return resultList;
    }

    /**
     * 按持证主体类别统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按持证主体类别统计结果带占比
     */
    @Override
    public List<HolderCategoryUsageStatisticsDTO> statisticsByHolderCategoryWithRatio(
            CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按持证主体类别统计
        List<HolderCategoryUsageStatisticsDTO> detailList = baseMapper.statisticsByHolderCategoryWithRatio(param);

        // 创建结果列表
        List<HolderCategoryUsageStatisticsDTO> resultList = new ArrayList<>();

        // 计算总使用量
        Long totalUsageCount = 0L;
        for (HolderCategoryUsageStatisticsDTO detail : detailList) {
            totalUsageCount += detail.getUsageCount();
        }

        // 创建汇总数据
        HolderCategoryUsageStatisticsDTO summaryData = new HolderCategoryUsageStatisticsDTO();
        summaryData.setHolderCategoryCode("total");
        summaryData.setHolderCategoryName("总计");
        summaryData.setUsageCount(totalUsageCount);
        summaryData.setUsageRatio(100.0); // 汇总数据占比100%
        summaryData.setIsSummary(true);

        // 计算各持证主体类别的占比
        if (detailList != null && !detailList.isEmpty() && totalUsageCount > 0) {
            for (HolderCategoryUsageStatisticsDTO detail : detailList) {
                // 计算占比
                double usageRatio = detail.getUsageCount() * 100.0 / totalUsageCount;
                detail.setUsageRatio(Math.round(usageRatio * 100) / 100.0);
            }
        }

        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);

        return resultList;
    }

    /**
     * 按时间统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按时间统计结果带占比
     */
    @Override
    public List<TimeUsageStatisticsDTO> statisticsByTimeWithRatio(CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按时间统计
        List<TimeUsageStatisticsDTO> detailList = baseMapper.statisticsByTimeWithRatio(param);

        // 创建结果列表
        List<TimeUsageStatisticsDTO> resultList = new ArrayList<>();

        // 计算总使用量
        Long totalUsageCount = 0L;
        for (TimeUsageStatisticsDTO detail : detailList) {
            totalUsageCount += detail.getUsageCount();
        }

        // 创建汇总数据
        TimeUsageStatisticsDTO summaryData = new TimeUsageStatisticsDTO();
        summaryData.setTimePoint("总计");
        summaryData.setUsageCount(totalUsageCount);
        summaryData.setUsageRatio(100.0); // 汇总数据占比100%
        summaryData.setIsSummary(true);

        // 计算各时间点的占比
        if (detailList != null && !detailList.isEmpty() && totalUsageCount > 0) {
            for (TimeUsageStatisticsDTO detail : detailList) {
                // 计算占比
                double usageRatio = detail.getUsageCount() * 100.0 / totalUsageCount;
                detail.setUsageRatio(Math.round(usageRatio * 100) / 100.0);
            }
        }

        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);

        return resultList;
    }

    @Override
    public List<CertificateTypeUsageStatisticsDTO> statisticsByType(CertificateUsageStatisticsVO param) {
        // 调用Mapper进行按证照类型统计
        List<CertificateTypeUsageStatisticsDTO> detailList = baseMapper.statisticsByType(param);
        // 计算总使用量
        long totalUsageCount = detailList.stream()
                .mapToLong(CertificateTypeUsageStatisticsDTO::getUsageCount)
                .sum();
        CertificateTypeUsageStatisticsDTO certificateTypeUsageStatisticsDTO = new CertificateTypeUsageStatisticsDTO();
        certificateTypeUsageStatisticsDTO.setFirstLevelTypeCode("total");
        certificateTypeUsageStatisticsDTO.setFirstLevelTypeName("总计");
        certificateTypeUsageStatisticsDTO.setUsageCount(totalUsageCount);
        certificateTypeUsageStatisticsDTO.setUsageRatio(100.0);
        // 计算各证照类型的占比
        for (CertificateTypeUsageStatisticsDTO detail : detailList) {
            // 计算占比
            double usageRatio = detail.getUsageCount() * 100.0 / totalUsageCount;
            detail.setUsageRatio(Math.round(usageRatio * 100) / 100.0);
        }
        detailList.add(0, certificateTypeUsageStatisticsDTO);
        return detailList;
    }
}
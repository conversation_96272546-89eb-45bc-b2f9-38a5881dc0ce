package com.js.hszpt.test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 船员证书查询接口测试类
 */
public class TestCertificateApi {
    
    public static void main(String[] args) {
        try {
            // 接口基础URL
            String baseUrl = "http://localhost:8285/certificate/crewCertificateQuery";
            
            // 设置查询参数
            String displaySource = "2";
            String certificateNumber = "NNHZ-09010020110154HG";
            String captchaId = "001";
            String code = "001";
            String birth = "";
            
            // 构建完整URL，包含查询参数
            String urlString = baseUrl + 
                    "?displaySource=" + URLEncoder.encode(displaySource, StandardCharsets.UTF_8.toString()) +
                    "&certificateNumber=" + URLEncoder.encode(certificateNumber, StandardCharsets.UTF_8.toString()) +
                    "&captchaId=" + URLEncoder.encode(captchaId, StandardCharsets.UTF_8.toString()) +
                    "&code=" + URLEncoder.encode(code, StandardCharsets.UTF_8.toString()) +
                    "&birth=" + URLEncoder.encode(birth, StandardCharsets.UTF_8.toString());
            
            System.out.println("请求URL: " + urlString);
            
            // 创建URL对象
            URL url = new URL(urlString);
            
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法为GET
            connection.setRequestMethod("GET");
            
            // 设置请求头
            connection.setRequestProperty("Accept", "application/json");
            
            // 获取响应码
            int responseCode = connection.getResponseCode();
            System.out.println("响应码: " + responseCode);
            
            // 读取响应内容
            BufferedReader reader;
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8));
            }
            
            // 构建响应字符串
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            // 打印响应结果
            System.out.println("响应内容:");
            System.out.println(response.toString());
            
            // 关闭连接
            connection.disconnect();
            
        } catch (Exception e) {
            System.out.println("测试接口时发生异常:");
            e.printStackTrace();
        }
    }
} 
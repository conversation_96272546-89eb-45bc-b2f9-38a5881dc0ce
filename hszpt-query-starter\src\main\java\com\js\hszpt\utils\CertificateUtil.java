package com.js.hszpt.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.js.hszpt.constants.Common;
import com.js.hszpt.enmus.CertificateTypeCode;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.entity.CertificateAttribute;
import com.js.hszpt.properties.UrlConfig;
import com.js.hszpt.vo.CertificateData;
import com.js.hszpt.vo.Surface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
public class CertificateUtil {

    @Autowired
    private UrlConfig config;

    private static String[] OFFICER_INFO = {"IssuePeople1", "IssueJob1", "IssuePeople2", "IssueJob2"};

    //残骸字段整理
    private static String[] DEBRIS_OFFICER_INFO = {"IssuedGovernment1", "IssueJob1", "IssuePeople1", "IssuePeople2", "IssueJob2" , "IssuedGovernment2", "shipsName1", "shipsArea2" ,"shipsAddress2"};

    //燃油国际字段整理
    private static String[] FUEL_POLLUTION_GJ_OFFICER_INFO = {"issueDept1", "IssueJob1", "official1", "official2", "IssueJob2" , "issueDept2"};

    //油污国际字段整理
    private static String[] OIL_DAMAGE_GJ_OFFICER_INFO = {"IssuedGovernment1", "IssueJob1", "IssuePeople1", "IssuePeople2", "IssueJob2" , "IssuedGovernment2"};

    /**
     * 请求电子证照接口
     * @param certificateData 电子证照信息
     * @param url 请求地址
     */
    public String getCertificate(CertificateData certificateData, String url) {
        String certificateId = certificateData.getCertificateId();
        StringBuilder sb = new StringBuilder();
        sb.append("AccountId=").append(config.getAccountId())
                .append("※AccessToken=").append(config.getAccessToken())
                .append("※OperType=").append("add")
                .append("※UseOFD=").append("true")
                .append("※isView=").append("0")
                .append("※certificateId=").append(certificateId)
                .append("※CertificateData=")
                .append(JSON.toJSON(certificateData));
        System.out.println("请求参数--------------------------------------------------------------：" + sb.toString());
        log.info("请求地址{}:", url);
        String param = sb.toString()
                .replaceAll("&", "＆")
                .replaceAll("※", "&");
        log.info("请求地址{}:", url);
        log.info("证书接口请求参数:{}",sb.toString());
        String result = HttpRequestUtil.sendPost(url, param, false);
        Map<String, Object> data = JSONObject.parseObject(result, Map.class);
        log.info("证书接口返回值:" + data.toString());
        if (data.get("head") == null) {
            return null;
        }
        Map<String, Object> head = JSONObject.parseObject(data.get("head").toString(), Map.class);
        if(head.get("status") == null || !StrUtil.equals("0",head.get("status").toString())) {
            return null;
        }
        // save certificateId in approve
        //certificateId = (String) ((Map<String, Object>) data.get("data")).get("filePath");
        certificateId = (String) ((Map<String, Object>) data.get("data")).get("CertificateID");
        if (StrUtil.isBlank(certificateId)) {
            log.error("调用证书接口失败,CertificateID返回空");
            return null;
        }
        return certificateId;
    }

    public static CertificateData createCertificateData(Certificate certificate, List<CertificateAttribute> certificateAttributeList) {
        CertificateData certificateData = new CertificateData();
        if (certificate == null || CollUtil.isEmpty(certificateAttributeList)) {
            return certificateData;
        }

        //设置certificateId 为业务表主键id
        certificateData.setCertificateId(certificate.getId());

        List<Surface> surfaceList = new ArrayList<>();
        certificateData.setCertificateType(certificate.getCertificateName());
        certificateData.setCertificateNumber(certificate.getCertificateNum());
        certificateData.setUserId(certificate.getCreateOperId());
        certificateData.setName(certificate.getHolderName());
        // 电子证照接口新增字段信息
        certificateData.setCatalogName(certificate.getCertificateName());
        certificateData.setDeptCode(certificate.getIssuOrgCode2());
        certificateData.setCertificateHolder(certificate.getHolderName());
        certificateData.setCertificateHolderCode(certificate.getHolderIdentityNumber());
        certificateData.setApplyNum(certificate.getApplyNo());
        // 颁证机构代码
        certificateData.setIssueDept(certificate.getIssuOrgNameCn());
        certificateData.setIssueDate(Common.DateFormat.SDF.format(new Date()));
        certificateData.setCreditCode(certificate.getIssuOrgCode2());
        certificateData.setIssueDeptCode(certificate.getIssuOrgCode2());


        // 证照类型1:证 2:文书
        certificateData.setZzType("1");
        //版本
        certificateData.setVersion("V1");
        //01:通航 02:受理 03:局章
        certificateData.setQzType("17");
        //持证主体代码类型
        String applyUserType = certificate.getProType();
        if (StrUtil.equals(applyUserType, "个人")) {
            certificateData.setCertificateHolderType("身份证");
        } else if (StrUtil.equals(applyUserType, "法人")) {
            certificateData.setCertificateHolderType("统一社会信用代码");
        }
        //颁证单位所属区划代码
        certificateData.setCertificateAreaCode((String) Common.govAreaCode.get("上海"));
        certificateData.setCertificateValidateStart(Common.DateFormat.SDF.format(certificate.getEffectDate()));
        certificateData.setCertificateValidateEnd(Common.DateFormat.SDF.format(certificate.getExpireDate()));

        AtomicReference<String> officeName = new AtomicReference<>();     //签证官员(中文)
        // 签证官员(中文)
        AtomicReference<String> IssuePeople1 = new AtomicReference<>();
        // 签证官员职务(中文)
        AtomicReference<String> IssueJob1 = new AtomicReference<>();
        // 签证官员(英文)
        AtomicReference<String> IssuePeople2 = new AtomicReference<>();
        // 签证官员职务(英文)
        AtomicReference<String> IssueJob2 = new AtomicReference<>();
        // X海事局(中文)
        AtomicReference<String> IssuedGovernment1 = new AtomicReference<>();
        // 船舶名称
        AtomicReference<String> shipsName = new AtomicReference<>();
        // 船籍港
        AtomicReference<String> shipsArea = new AtomicReference<>();
        // 船舶所有人名称和地址
        AtomicReference<String> shipsAddress = new AtomicReference<>();


        // X海事局(英文)
        AtomicReference<String> IssuedGovernment2 = new AtomicReference<>();
        certificateAttributeList.forEach(certificateAttribute -> {
            if("签证官员(中文)".equals(certificateAttribute.getAttributeName())){
                officeName.set(certificateAttribute.getAttributeValue());
            }

            Surface surface = new Surface();
            surface.setName(certificateAttribute.getAttributeName());
            surface.setValue(certificateAttribute.getAttributeValue());
            surface.setValueType("string");

            //电子证照接口，燃油污染损害民事责任保险或其他财务保证证书（国际），保证期限中文传值为 issueDept 处理
            if(CertificateTypeCode.FUEL_POLLUTION.getTypeCode().equals(certificate.getCertificateTypeCode())){
                if("securityDate1".equals(certificateAttribute.getAttributeColumnName())){
                    surface.setColumnName("Duration1");
                }else{
                    surface.setColumnName(certificateAttribute.getAttributeColumnName());
                }
            }else{
                surface.setColumnName(certificateAttribute.getAttributeColumnName());
            }

            /**
             * 残骸签证官参数示例：
             * 中文职务及姓名：IssuePeople1
             * 英文姓名：IssuePeople2
             * 英文职务：IssueJobl
             * IssuePeople1：IssuedGovernment1 + IssueJob1 + IssuePeople1
             * IssuePeople2： IssuePeople2
             * IssueJobl：IssueJob2 + " of " + IssuedGovernment2
             */
            if(CertificateTypeCode.DEBRIS.getTypeCode().equals(certificate.getCertificateTypeCode())) {
               if (Arrays.asList(DEBRIS_OFFICER_INFO).contains(certificateAttribute.getAttributeColumnName())) {
                    switch (certificateAttribute.getAttributeColumnName()) {
                        case "IssuedGovernment1":
                            IssuedGovernment1.set(certificateAttribute.getAttributeValue());
                            surfaceList.add(surface);
                            break;
                        case "IssueJob1":
                            IssueJob1.set(certificateAttribute.getAttributeValue());
                            break;
                        case "IssuePeople1":
                            IssuePeople1.set(certificateAttribute.getAttributeValue());
                            break;
                       case "IssuePeople2":
                           IssuePeople2.set(certificateAttribute.getAttributeValue());
                            break;
                        case "IssueJob2":
                            IssueJob2.set(certificateAttribute.getAttributeValue());
                            break;
                        case "IssuedGovernment2":
                            IssuedGovernment2.set(certificateAttribute.getAttributeValue());
                            surfaceList.add(surface);
                            break;
                        case "shipsName1":
                            shipsName.set(certificateAttribute.getAttributeValue());
                            break;
                        case "shipsArea2":
                            shipsArea.set(certificateAttribute.getAttributeValue());
                            break;
                        case "shipsAddress2":
                            shipsAddress.set(certificateAttribute.getAttributeValue());
                            break;
                        default:
                            break;
                    }
                 }else {
                    surfaceList.add(surface);
                }
            }


            /**
             *
             *燃油国际签证官参数示例：
             * 中文职务及姓名：official1
             * 英文姓名：IssueJob1
             * 英文职务：IssueJob2
             * official1：issueDept1 + IssueJob1 + official1
             * IssueJob1：official2
             * IssueJob2：IssueJob2 + " of " + IssuedGovernment2
             *
             */
            if(CertificateTypeCode.FUEL_POLLUTION.getTypeCode().equals(certificate.getCertificateTypeCode())) {
                if(CertificateTypeCode.FUEL_POLLUTION_GJ.getName().equals(certificate.getCertificateType())) {
                    if (Arrays.asList(FUEL_POLLUTION_GJ_OFFICER_INFO).contains(certificateAttribute.getAttributeColumnName())) {
                        switch (certificateAttribute.getAttributeColumnName()) {
                            case "issueDept1":
                                IssuedGovernment1.set(certificateAttribute.getAttributeValue());
                                surfaceList.add(surface);
                                break;
                            case "IssueJob1":
                                IssueJob1.set(certificateAttribute.getAttributeValue());
                                break;
                            case "official1":
                                IssuePeople1.set(certificateAttribute.getAttributeValue());
                                break;
                            case "official2":
                                IssuePeople2.set(certificateAttribute.getAttributeValue());
                                break;
                            case "IssueJob2":
                                IssueJob2.set(certificateAttribute.getAttributeValue());
                                break;
                            case "issueDept2":
                                log.info("燃油国际签证官参数取值-issueDept2:{}", certificateAttribute.getAttributeValue());
                                IssuedGovernment2.set(certificateAttribute.getAttributeValue());
                                surfaceList.add(surface);
                                break;
                            default:
                                break;
                        }
                    }else {
                        surfaceList.add(surface);
                    }
                }else{
                    surfaceList.add(surface);
                }
            }

            /**
             * 油污国际签证官参数示例：
             * 中文职务及姓名：IssuePeople1
             * 英文姓名：IssueJob1
             * 英文职务：IssueJob2
             * IssuePeople1：IssuedGovernment1 + IssueJob1 + IssuePeople1
             * IssueJob1：IssuePeople2
             * IssueJob2：IssueJob2 + " of " + IssuedGovernment2
             */
            if(CertificateTypeCode.OIL_DAMAGE.getTypeCode().equals(certificate.getCertificateTypeCode())) {
                if (CertificateTypeCode.OIL_DAMAGE_GJ.getName().equals(certificate.getCertificateType())) {
                    if (Arrays.asList(OIL_DAMAGE_GJ_OFFICER_INFO).contains(certificateAttribute.getAttributeColumnName())) {
                        switch (certificateAttribute.getAttributeColumnName()) {
                            case "IssuedGovernment1":
                                IssuedGovernment1.set(certificateAttribute.getAttributeValue());
                                surfaceList.add(surface);
                                break;
                            case "IssueJob1":
                                IssueJob1.set(certificateAttribute.getAttributeValue());
                                break;
                            case "IssuePeople1":
                                IssuePeople1.set(certificateAttribute.getAttributeValue());
                                break;
                            case "IssuePeople2":
                                IssuePeople2.set(certificateAttribute.getAttributeValue());
                                break;
                            case "IssueJob2":
                                IssueJob2.set(certificateAttribute.getAttributeValue());
                                break;
                            case "IssuedGovernment2":
                                IssuedGovernment2.set(certificateAttribute.getAttributeValue());
                                surfaceList.add(surface);
                                break;
                            default:
                                break;
                        }
                    } else {
                        surfaceList.add(surface);
                    }
                }else{
                    surfaceList.add(surface);
                }
            }

            //非持久证书
            if(CertificateTypeCode.NON_PERSISTENT.getTypeCode().equals(certificate.getCertificateTypeCode())) {
                surfaceList.add(surface);
            }


        });
        // 燃油
        if (CertificateTypeCode.FUEL_POLLUTION_GJ.getName().equals(certificate.getCertificateType())) {
            Surface official1Surface = new Surface();
            official1Surface.setName("中文职务及姓名");
            official1Surface.setColumnName("official1");
            official1Surface.setValue(IssuedGovernment1.get() + IssueJob1.get()  + " " + IssuePeople1.get());
            official1Surface.setValueType("string");

            Surface IssueJob1Surface = new Surface();
            IssueJob1Surface.setName("英文姓名");
            IssueJob1Surface.setColumnName("IssueJob1");
            IssueJob1Surface.setValue(IssuePeople2.get());
            IssueJob1Surface.setValueType("string");

            Surface IssueJob2Surface = new Surface();
            IssueJob2Surface.setName("英文职务");
            IssueJob2Surface.setColumnName("IssueJob2");
            log.info("燃油国际签证官参数赋值-issueDept2:{}", IssuedGovernment2.get());
            String issueDept2 = IssuedGovernment2.get();
            if(StrUtil.isBlank(issueDept2)){
                issueDept2 = "Shanghai Maritime Safety Administration，P.R.C.";
                log.error("燃油国际签证官参数赋值-issueDept2为空");
            }
            IssueJob2Surface.setValue(IssueJob2.get() + " of " + issueDept2);
            IssueJob2Surface.setValueType("string");

            surfaceList.add(official1Surface);
            surfaceList.add(IssueJob1Surface);
            surfaceList.add(IssueJob2Surface);
        }
        // 油污
        if (CertificateTypeCode.OIL_DAMAGE_GJ.getName().equals(certificate.getCertificateType())) {
            Surface official1Surface = new Surface();
            official1Surface.setName("中文职务及姓名");
            official1Surface.setColumnName("IssuePeople1");
            official1Surface.setValue(IssuedGovernment1.get() + IssueJob1.get() + " " + IssuePeople1.get());
            official1Surface.setValueType("string");

            Surface IssueJob1Surface = new Surface();
            IssueJob1Surface.setName("英文姓名");
            IssueJob1Surface.setColumnName("IssueJob1");
            IssueJob1Surface.setValue(IssuePeople2.get());
            IssueJob1Surface.setValueType("string");

            Surface IssueJob2Surface = new Surface();
            IssueJob2Surface.setName("英文职务");
            IssueJob2Surface.setColumnName("IssueJob2");
            IssueJob2Surface.setValue(IssueJob2.get() + " of " + IssuedGovernment2.get());
            IssueJob2Surface.setValueType("string");

            surfaceList.add(official1Surface);
            surfaceList.add(IssueJob1Surface);
            surfaceList.add(IssueJob2Surface);
        }
        // 残骸
        if (CertificateTypeCode.DEBRIS.getTypeCode().equals(certificate.getCertificateTypeCode())) {
            Surface official1Surface = new Surface();
            official1Surface.setName("中文职务及姓名");
            official1Surface.setColumnName("IssuePeople1");
            official1Surface.setValue(IssuedGovernment1.get() + IssueJob1.get() + " " + IssuePeople1.get());
            official1Surface.setValueType("string");

            Surface IssueJob1Surface = new Surface();
            IssueJob1Surface.setName("英文姓名");
            IssueJob1Surface.setColumnName("IssuePeople2");
            IssueJob1Surface.setValue(IssuePeople2.get());
            IssueJob1Surface.setValueType("string");

            Surface IssueJob2Surface = new Surface();
            IssueJob2Surface.setName("英文职务");
            IssueJob2Surface.setColumnName("IssueJob1");
            IssueJob2Surface.setValue(IssueJob2.get() + " of " + IssuedGovernment2.get());
            IssueJob2Surface.setValueType("string");

            surfaceList.add(official1Surface);
            surfaceList.add(IssueJob1Surface);
            surfaceList.add(IssueJob2Surface);

            //船名拆分
            String shipsNameStr = shipsName.get();
            if(StrUtil.isNotBlank(shipsNameStr) && shipsNameStr.contains("\r\n")) {
                String[] shipsNameArray =  shipsNameStr.split("\r\n");
                Surface shipsName1Surface = new Surface();
                shipsName1Surface.setName("船名中文");
                shipsName1Surface.setColumnName("shipsName1");
                shipsName1Surface.setValue(shipsNameArray[0]);
                shipsName1Surface.setValueType("string");
                surfaceList.add(shipsName1Surface);

                Surface shipsName2Surface = new Surface();
                shipsName2Surface.setName("船名英文");
                shipsName2Surface.setColumnName("shipsName2");
                shipsName2Surface.setValue(shipsNameArray[1]);
                shipsName2Surface.setValueType("string");
                surfaceList.add(shipsName2Surface);

            }

            //船籍港拆分
            String shipsAreaStr = shipsArea.get();
            if(StrUtil.isNotBlank(shipsAreaStr) && shipsAreaStr.contains("\r\n")) {
                String[] shipsAreaArray =  shipsAreaStr.split("\r\n");
                Surface shipsArea1Surface = new Surface();
                shipsArea1Surface.setName("船籍港中文");
                shipsArea1Surface.setColumnName("shipsArea1");
                shipsArea1Surface.setValue(shipsAreaArray[0]);
                shipsArea1Surface.setValueType("string");
                surfaceList.add(shipsArea1Surface);

                Surface shipsArea2Surface = new Surface();
                shipsArea2Surface.setName("船籍港英文");
                shipsArea2Surface.setColumnName("shipsArea2");
                shipsArea2Surface.setValue(shipsAreaArray[1]);
                shipsArea2Surface.setValueType("string");
                surfaceList.add(shipsArea2Surface);

            }

            //船舶所有人拆分
            String shipsAddressStr = shipsAddress.get();
            if(StrUtil.isNotBlank(shipsAddressStr) && shipsAddressStr.contains("\r\n")) {
                String[] shipsAddressArray =  shipsAddressStr.split("\r\n");
                if(shipsAddressArray.length == 4) {
                    Surface shipsAddress1Surface = new Surface();
                    shipsAddress1Surface.setName("所有人名称及地址中文");
                    shipsAddress1Surface.setColumnName("shipsAddress1");
                    shipsAddress1Surface.setValue(shipsAddressArray[0] + "\r\n" + shipsAddressArray[1]);
                    shipsAddress1Surface.setValueType("string");
                    surfaceList.add(shipsAddress1Surface);

                    Surface shipsAddress2Surface = new Surface();
                    shipsAddress2Surface.setName("所有人名称及地址英文");
                    shipsAddress2Surface.setColumnName("shipsAddress2");
                    shipsAddress2Surface.setValue(shipsAddressArray[2] + "\r\n" + shipsAddressArray[3]);
                    shipsAddress2Surface.setValueType("string");
                    surfaceList.add(shipsAddress2Surface);
                }

            }


        }

        //设置燃油、油污证书类型名称，电子证照接口需要区分国际/国内
        if(CertificateTypeCode.FUEL_POLLUTION.getTypeCode().equals(certificate.getCertificateTypeCode())
                ||CertificateTypeCode.OIL_DAMAGE.getTypeCode().equals(certificate.getCertificateTypeCode())){
            /**
             * 电子证照接口 certificateType 字段取值：
             * 油污损害民事责任保险或其他财务保证证书
             * 残骸清除责任保险或其他财务保证证书
             * 燃油污染损害民事责任保险或其他财务保证证书
             * 非持久性油类污染损害民事责任保险或其他财务保证证书
             */
            certificateData.setCertificateType(certificate.getCertificateName());
            /**
             * 电子证照接口 catalogName 字段取值：
             * 油污损害民事责任保险或其他财务保证证书（国际）
             * 油污损害民事责任保险或其他财务保证证书（国内）
             * 残骸清除责任保险或其他财务保证证书
             * 燃油污染损害民事责任保险或其他财务保证证书（国际）
             * 燃油污染损害民事责任保险或其他财务保证证书（国内）
             * 非持久性油类污染损害民事责任保险或其他财务保证证书
             */
            certificateData.setCatalogName(certificate.getCertificateType());
        }
        //涉及到中英文的，都需要传qianming参数；值就是签证官的汉字名称
        if(certificateData.getCatalogName().contains("（国际）")
                || CertificateTypeCode.DEBRIS.getTypeCode().equals(certificate.getCertificateTypeCode())){
            Surface surface = new Surface();
            surface.setName("签名");
            surface.setValue(officeName.get());
//            surface.setValue("谢群威");  //电子证照测试环境联调
            surface.setValueType("string");
            surface.setColumnName("qianming");
            surfaceList.add(surface);
        }

        certificateData.setSurface(surfaceList);
        return certificateData;
    }

    public static void main(String[] args) {
        String key = "issueDept2";
        switch (key) {
            case "issueDept1":
                System.out.println("1");
                break;
            case "IssueJob1":
                System.out.println("2");
                break;
            case "official1":
                System.out.println("3");
                break;
            case "official2":
                System.out.println("4");
                break;
            case "IssueJob2":
                System.out.println("5");
                break;
            case "issueDept2":
                System.out.println("6");
                break;
            default:
                break;
        }
    }


}

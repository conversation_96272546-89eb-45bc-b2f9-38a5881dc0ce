package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.BizEgApplyInfo;
import com.js.hszpt.mapper.BizEgApplyInfoMapper;
import org.springframework.stereotype.Service;

@Service
public class BizEgApplyInfoService extends ServiceImpl<BizEgApplyInfoMapper, BizEgApplyInfo> {

    public BizEgApplyInfo getBizEgApplyInfoByApplyId(String applyId) {
        QueryWrapper<BizEgApplyInfo> queryWrapper = Wrappers.<BizEgApplyInfo>query();
        queryWrapper.lambda()
                .eq(BizEgApplyInfo::getApplyId, applyId).last("LIMIT 1");
        return this.getOne(queryWrapper,false);
    }
}

package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.BizDgrOpic;
import com.js.hszpt.mapper.BizDgrOpicMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BizDgrOpicService extends ServiceImpl<BizDgrOpicMapper, BizDgrOpic> {

    public BizDgrOpic getByWindowApplyId(String windowApplyId){
        QueryWrapper<BizDgrOpic> queryWrapper = Wrappers.<BizDgrOpic>query();
        queryWrapper.lambda()
                .eq(BizDgrOpic::getApplyId,windowApplyId).last("LIMIT 1");
        return this.getOne(queryWrapper,false);
    }

    public BizDgrOpic getByShipId(String shipId){
        QueryWrapper<BizDgrOpic> queryWrapper = Wrappers.<BizDgrOpic>query();
        queryWrapper.lambda()
                .eq(BizDgrOpic::getShipId,shipId)
                .orderByDesc(BizDgrOpic::getCreateDate)
                .last("LIMIT 1");
        return this.getOne(queryWrapper,false);
    }
}

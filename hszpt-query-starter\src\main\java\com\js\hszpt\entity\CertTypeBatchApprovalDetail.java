package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CertTypeBatchApprovalDetail   
 * @Description:TODO(批量审批明细表)   
 * @author:   System Generation 
 */
@Data

@TableName("CERT_TYPE_BATCH_APPROVAL_DETAIL")
@ApiModel(value = "批量审批明细表")
public class CertTypeBatchApprovalDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 批量审批明细记录主键ID
     */
    @TableId(value = "CERT_TYPE_BATCH_APPR_DTL_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "批量审批明细记录主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
    * 批量审批表ID
    */
    @ApiModelProperty(value = "批量审批表ID")
    private String batchApprovalId;


    /**
    * 证照类型目录审批表ID
    */
    @ApiModelProperty(value = "证照类型目录审批表ID")
    private String certTypeApprovalId;


    /**
    * 关联审批节点表ID
    */
    @ApiModelProperty(value = "关联审批节点表ID")
    private String approvalNodeId;


    /**
    * 处理结果：成功、失败
    */
    @ApiModelProperty(value = "处理结果：成功、失败")
    private String handleResult;


    /**
    * 处理失败的原因
    */
    @ApiModelProperty(value = "处理失败的原因")
    private String failReason;


    /**
    * 处理时间
    */
    @ApiModelProperty(value = "处理时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;


}
package com.js.hszpt.api;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.annotation.SystemLog;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.enums.LogType;
import com.js.hszpt.entity.CertShipOwnership;
import com.js.hszpt.service.CertShipOwnershipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


 /**
 * 
 * @ClassName: CertShipOwnershipApi  
 * @Description:TODO(船舶证书-所有权登记证书接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "船舶证书-所有权登记证书接口")
@RequestMapping("/certShipOwnership")
public class CertShipOwnershipApi extends BaseApiPlus<CertShipOwnershipService,CertShipOwnership,String>{

	@SystemLog(description = "船舶证书-所有权登记证书-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CertShipOwnership>> getPage(@ModelAttribute CertShipOwnership param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CertShipOwnership> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "船舶证书-所有权登记证书-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CertShipOwnership>> getList(@ModelAttribute CertShipOwnership param, @ModelAttribute SearchVo searchVo) {
		List<CertShipOwnership> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
}

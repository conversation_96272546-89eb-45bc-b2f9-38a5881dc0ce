package com.js.hszpt.test;

import java.sql.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 证照签发统计测试用例
 */
public class CertificateIssueTest {
    private static final String URL = "******************************************************************************************************";
    private static final String USERNAME = "system";
    private static final String PASSWORD = "system";
    
    public static void main(String[] args) {
        try {
            Class.forName("org.postgresql.Driver");
            runAllTests();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void runAllTests() throws SQLException {
        // 测试正常查询
        testNormalQuery();
        
        // 测试按证照类型筛选
        testQueryByCertificateType();
        
        // 测试按时间范围筛选
        testQueryByDateRange();
        
        // 测试按机构层级筛选
        testQueryByOrgLevel();
        
        // 测试按持证主体类别筛选
        testQueryByHolderCategory();
        
        // 测试按事项性质筛选
        testQueryByMatterNature();
        
        // 测试按申请来源筛选
        testQueryByApplicationSource();
        
        // 测试按证照状态筛选
        testQueryByStatus();
        
        // 测试空结果
        testEmptyResult();
    }
    
    // 测试正常查询
    private static void testNormalQuery() throws SQLException {
        System.out.println("\n=== 测试正常查询 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 获取一个有效的机构编码作为登录用户机构
            String loginUserOrgCode = getRandomOrgCode(conn);
            
            // 2. 执行查询
            String sql = "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "GROUP BY i.issuer_code, d.name " +
                        "ORDER BY issue_count DESC " +
                        "LIMIT 10";
                        
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {
                
                System.out.println("登录用户机构编码: " + loginUserOrgCode);
                System.out.println("机构编码\t机构名称\t签发数量");
                System.out.println("----------------------------------------");
                
                while (rs.next()) {
                    System.out.printf("%s\t%s\t%d%n",
                        rs.getString("org_code"),
                        rs.getString("org_name"),
                        rs.getLong("issue_count"));
                    
                    // 验证数据正确性
                    assert rs.getString("org_code") != null;
                    assert rs.getLong("issue_count") >= 0;
                }
            }
        }
    }
    
    // 测试按证照类型筛选
    private static void testQueryByCertificateType() throws SQLException {
        System.out.println("\n=== 测试按证照类型筛选 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 获取一个有效的证照类型编码
            String certificateTypeCode = getRandomCertificateTypeCode(conn);
            if (certificateTypeCode == null) {
                System.out.println("未找到有效的证照类型编码，跳过测试");
                return;
            }
            
            // 2. 执行查询
            String sql = "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "WHERE i.certificate_type_code = ? " +
                        "GROUP BY i.issuer_code, d.name " +
                        "ORDER BY issue_count DESC " +
                        "LIMIT 10";
                        
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, certificateTypeCode);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    System.out.println("证照类型编码: " + certificateTypeCode);
                    System.out.println("机构编码\t机构名称\t签发数量");
                    System.out.println("----------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%s\t%s\t%d%n",
                            rs.getString("org_code"),
                            rs.getString("org_name"),
                            rs.getLong("issue_count"));
                        
                        // 验证数据正确性
                        assert rs.getString("org_code") != null;
                        assert rs.getLong("issue_count") >= 0;
                    }
                }
            }
        }
    }
    
    // 测试按时间范围筛选
    private static void testQueryByDateRange() throws SQLException {
        System.out.println("\n=== 测试按时间范围筛选 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 设置时间范围(过去一年)
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusYears(1);
            
            String startDateStr = startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String endDateStr = endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            
            // 2. 执行查询
            String sql = "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "WHERE i.ds_certificate_date >= ? AND i.ds_certificate_date <= ? " +
                        "GROUP BY i.issuer_code, d.name " +
                        "ORDER BY issue_count DESC " +
                        "LIMIT 10";
                        
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, startDateStr);
                pstmt.setString(2, endDateStr);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    System.out.println("开始日期: " + startDateStr + ", 结束日期: " + endDateStr);
                    System.out.println("机构编码\t机构名称\t签发数量");
                    System.out.println("----------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%s\t%s\t%d%n",
                            rs.getString("org_code"),
                            rs.getString("org_name"),
                            rs.getLong("issue_count"));
                        
                        // 验证数据正确性
                        assert rs.getString("org_code") != null;
                        assert rs.getLong("issue_count") >= 0;
                    }
                }
            }
        }
    }
    
    // 测试按机构层级筛选
    private static void testQueryByOrgLevel() throws SQLException {
        System.out.println("\n=== 测试按机构层级筛选 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 获取一个父级机构编码
            String parentOrgCode = getParentOrgCode(conn);
            if (parentOrgCode == null) {
                System.out.println("未找到有效的父级机构编码，跳过测试");
                return;
            }
            
            // 2. 执行查询(查询该机构及其所有子机构的数据)
            String sql = "WITH RECURSIVE org_tree AS (" +
                        "  SELECT dept_id FROM dws_ctf_sys_dept WHERE dept_id = ? " +
                        "  UNION ALL " +
                        "  SELECT d.dept_id FROM dws_ctf_sys_dept d " +
                        "  JOIN org_tree ot ON d.parent_id = ot.dept_id" +
                        ") " +
                        "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "JOIN org_tree ot ON i.issuer_code = ot.dept_id " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "GROUP BY i.issuer_code, d.name " +
                        "ORDER BY issue_count DESC";
                        
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, parentOrgCode);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    System.out.println("父级机构编码: " + parentOrgCode);
                    System.out.println("机构编码\t机构名称\t签发数量");
                    System.out.println("----------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%s\t%s\t%d%n",
                            rs.getString("org_code"),
                            rs.getString("org_name"),
                            rs.getLong("issue_count"));
                        
                        // 验证数据正确性
                        assert rs.getString("org_code") != null;
                        assert rs.getLong("issue_count") >= 0;
                    }
                }
            }
        }
    }
    
    // 测试按持证主体类别筛选
    private static void testQueryByHolderCategory() throws SQLException {
        System.out.println("\n=== 测试按持证主体类别筛选 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 获取一个有效的持证主体类别编码
            String holderCategoryCode = getRandomDictValue(conn, "CERTIFICATE_HOLDER_CATEGORY");
            if (holderCategoryCode == null) {
                System.out.println("未找到有效的持证主体类别编码，跳过测试");
                return;
            }
            
            // 2. 执行查询
            String sql = "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "WHERE i.holder_category_code = ? " +
                        "GROUP BY i.issuer_code, d.name " +
                        "ORDER BY issue_count DESC " +
                        "LIMIT 10";
                        
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, holderCategoryCode);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    System.out.println("持证主体类别编码: " + holderCategoryCode);
                    System.out.println("机构编码\t机构名称\t签发数量");
                    System.out.println("----------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%s\t%s\t%d%n",
                            rs.getString("org_code"),
                            rs.getString("org_name"),
                            rs.getLong("issue_count"));
                        
                        // 验证数据正确性
                        assert rs.getString("org_code") != null;
                        assert rs.getLong("issue_count") >= 0;
                    }
                }
            }
        }
    }
    
    // 测试按事项性质筛选
    private static void testQueryByMatterNature() throws SQLException {
        System.out.println("\n=== 测试按事项性质筛选 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 获取一个有效的事项性质编码
            String matterNatureCode = getRandomDictValue(conn, "affair_type");
            if (matterNatureCode == null) {
                System.out.println("未找到有效的事项性质编码，跳过测试");
                return;
            }
            
            // 2. 执行查询
            String sql = "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "WHERE i.matter_nature_code = ? " +
                        "GROUP BY i.issuer_code, d.name " +
                        "ORDER BY issue_count DESC " +
                        "LIMIT 10";
                        
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, matterNatureCode);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    System.out.println("事项性质编码: " + matterNatureCode);
                    System.out.println("机构编码\t机构名称\t签发数量");
                    System.out.println("----------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%s\t%s\t%d%n",
                            rs.getString("org_code"),
                            rs.getString("org_name"),
                            rs.getLong("issue_count"));
                        
                        // 验证数据正确性
                        assert rs.getString("org_code") != null;
                        assert rs.getLong("issue_count") >= 0;
                    }
                }
            }
        }
    }
    
    // 测试按申请来源筛选
    private static void testQueryByApplicationSource() throws SQLException {
        System.out.println("\n=== 测试按申请来源筛选 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 获取一个有效的申请来源编码
            String applicationSourceCode = getRandomDictValue(conn, "apply_type");
            if (applicationSourceCode == null) {
                System.out.println("未找到有效的申请来源编码，跳过测试");
                return;
            }
            
            // 2. 执行查询
            String sql = "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "WHERE i.application_source_code = ? " +
                        "GROUP BY i.issuer_code, d.name " +
                        "ORDER BY issue_count DESC " +
                        "LIMIT 10";
                        
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, applicationSourceCode);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    System.out.println("申请来源编码: " + applicationSourceCode);
                    System.out.println("机构编码\t机构名称\t签发数量");
                    System.out.println("----------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%s\t%s\t%d%n",
                            rs.getString("org_code"),
                            rs.getString("org_name"),
                            rs.getLong("issue_count"));
                        
                        // 验证数据正确性
                        assert rs.getString("org_code") != null;
                        assert rs.getLong("issue_count") >= 0;
                    }
                }
            }
        }
    }
    
    // 测试按证照状态筛选
    private static void testQueryByStatus() throws SQLException {
        System.out.println("\n=== 测试按证照状态筛选 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 获取一个有效的证照状态编码
            String statusCode = getRandomDictValue(conn, "certificate_status");
            if (statusCode == null) {
                System.out.println("未找到有效的证照状态编码，跳过测试");
                return;
            }
            
            // 2. 执行查询
            String sql = "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "WHERE i.certificate_status_code = ? " +
                        "GROUP BY i.issuer_code, d.name " +
                        "ORDER BY issue_count DESC " +
                        "LIMIT 10";
                        
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, statusCode);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    System.out.println("证照状态编码: " + statusCode);
                    System.out.println("机构编码\t机构名称\t签发数量");
                    System.out.println("----------------------------------------");
                    
                    while (rs.next()) {
                        System.out.printf("%s\t%s\t%d%n",
                            rs.getString("org_code"),
                            rs.getString("org_name"),
                            rs.getLong("issue_count"));
                        
                        // 验证数据正确性
                        assert rs.getString("org_code") != null;
                        assert rs.getLong("issue_count") >= 0;
                    }
                }
            }
        }
    }
    
    // 测试空结果
    private static void testEmptyResult() throws SQLException {
        System.out.println("\n=== 测试空结果 ===");
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 使用一个不存在的证照类型编码
            String nonExistentCode = "NON_EXISTENT_CODE";
            
            String sql = "SELECT i.issuer_code as org_code, d.name as org_name, COUNT(*) as issue_count " +
                        "FROM dws_ctf_certificate_issue i " +
                        "LEFT JOIN dws_ctf_sys_dept d ON i.issuer_code = d.dept_id " +
                        "WHERE i.certificate_type_code = ? " +
                        "GROUP BY i.issuer_code, d.name";
                        
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, nonExistentCode);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    System.out.println("使用不存在的证照类型编码: " + nonExistentCode);
                    
                    boolean hasResults = false;
                    while (rs.next()) {
                        hasResults = true;
                        System.out.printf("%s\t%s\t%d%n",
                            rs.getString("org_code"),
                            rs.getString("org_name"),
                            rs.getLong("issue_count"));
                    }
                    
                    if (!hasResults) {
                        System.out.println("查询结果为空，符合预期");
                    }
                }
            }
        }
    }
    
    // 辅助方法：获取随机机构编码
    private static String getRandomOrgCode(Connection conn) throws SQLException {
        String sql = "SELECT dept_id FROM dws_ctf_sys_dept LIMIT 1";
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            if (rs.next()) {
                return rs.getString("dept_id");
            }
        }
        return null;
    }
    
    // 辅助方法：获取随机证照类型编码
    private static String getRandomCertificateTypeCode(Connection conn) throws SQLException {
        String sql = "SELECT CERTIFICATE_TYPE_CODE FROM dws_ctf_cert_type_directory LIMIT 1";
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            if (rs.next()) {
                return rs.getString("CERTIFICATE_TYPE_CODE");
            }
        }
        return null;
    }
    
    // 辅助方法：获取父级机构编码
    private static String getParentOrgCode(Connection conn) throws SQLException {
        String sql = "SELECT dept_id FROM dws_ctf_sys_dept WHERE EXISTS (SELECT 1 FROM dws_ctf_sys_dept WHERE parent_id = dept_id) LIMIT 1";
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            if (rs.next()) {
                return rs.getString("dept_id");
            }
        }
        return null;
    }
    
    // 辅助方法：获取随机字典值
    private static String getRandomDictValue(Connection conn, String dictType) throws SQLException {
        String sql = "SELECT b.value FROM dws_ctf_dict a, dws_ctf_dict_data b WHERE a.type = ? AND a.id = b.dict_id LIMIT 1";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, dictType);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("value");
                }
            }
        }
        return null;
    }
} 
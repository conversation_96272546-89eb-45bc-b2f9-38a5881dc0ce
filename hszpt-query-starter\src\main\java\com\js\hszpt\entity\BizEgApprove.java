package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class BizEgApprove {

    /**
     * 审批意见唯一标识
     */
    @TableId
    private String govApvlId;

    /**
     * 业务申请标识
     */
    private String applyId;

    /**
     * 报告类型名称
     */
    private String rptClassName;

    /**
     * 审核人姓名
     */
    private String approverName;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 审批日期
     */
    private Date apprDate;

    /**
     * 审批结果 1：许可通过；2：许可未通过
     */
    private String apprResult;

    /**
     * 审批意见
     */
    private String apprOpinion;

    /**
     * 相关依据
     */
    private String delatedBasis;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 开始日期
     */
    private Date startDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 过驳申请具体要求
     */
    private String lighterApplyReq;

    /**
     * 复议机构
     */
    private String reconsiderOrg;

    /**
     * 诉讼机构
     */
    private String litigationOrg;

    /**
     * 证书ID
     */
    private String certId1;

    /**
     * 证照编号
     */
    private String certNo1;

    /**
     * 证书地址
     */
    private String url;

    /**
     * 证书ID2
     */
    private String certId2;

    /**
     * 证书编号2
     */
    private String certNo2;

    /**
     * 审核材料结果
     */
    private String matAuditResult;

    /**
     * 证书ID(符合证明英文)
     */
    private String certIdEn;

    /**
     * 证书ID(临时符合证明英文)
     */
    private String certId2En;

    /**
     * 证书ID(年度签注中文)
     */
    private String certId3;

    /**
     * 证书ID(年度签注英文)
     */
    private String certId3En;

    /**
     * 证书ID(跟踪通知书)
     */
    private String certId4;

    /**
     * 签证官员
     */
    private String visaOffi;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人标识 DICT_EG_USER
     */
    private String creatorId;

    /**
     * 创建人所属机构代码
     */
    private String creatorOrgCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 修改人标识 DICT_EG_USER
     */
    private String modifierId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 源系统代码
     */
    private String sourceCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录创建日期
     */
    private Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录修改日期
     */
    private Date recModifyDate;

    /**
     * 数据归属机构代码 DICT_EG_ORG
     */
    private String msaOrgCode;
}

package com.js.hszpt.dto;

import lombok.Data;

import java.util.List;

/**
 * 证照类型统计结果DTO
 */
@Data
public class CertificateTypeStatisticsDTO {

    /**
     * 第一级证照类型代码
     */
    private String firstLevelTypeCode;

    /**
     * 第一级证照类型名称
     */
    private String firstLevelTypeName;

    /**
     * 证照类型代码
     */
    private String certificateTypeCode;

    /**
     * 证照类型名称
     */
    private String certificateTypeName;

    /**
     * 上级证照类型代码
     */
    private String parentTypeCode;

    /**
     * 签发数量总和
     */
    private Long issueCount;

    /**
     * 签发占比（百分比）
     */
    private Double issueRatio;

    /**
     * 子节点列表
     */
    private List<CertificateTypeStatisticsDTO> children;

    /**
     * 是否是汇总数据
     */
    private Boolean isSummary;

    /**
     * 节点级别
     */
    private Integer level;
}
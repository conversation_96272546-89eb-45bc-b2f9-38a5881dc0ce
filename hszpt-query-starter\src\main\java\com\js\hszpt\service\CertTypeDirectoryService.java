package com.js.hszpt.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.common.entity.CurrentUser;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.*;
import com.js.hszpt.mapper.CertTypeDirectoryDao;
import com.js.hszpt.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @ClassName: CertTypeDirectoryService
 * @Description:TODO(证照类型目录表接口实现)
 * @author: System Generation
 *
 */
@Slf4j
@Service
public class CertTypeDirectoryService extends ServiceImpl<CertTypeDirectoryDao, CertTypeDirectory> {


}

package com.js.hszpt.constants;


import cn.hutool.core.map.MapUtil;

import java.util.HashMap;
import java.util.Map;

public class OilPollutionWarrantyConstant {

    //燃油污染损害民事责任保险或其他财务保证证书
    public static final Map<String,String> FUEL_POLLUTION;
    public static final Map<String,String> FUEL_POLLUTION_REVERSE;
    //油污损害民事责任保险或其他财务保证证书
    public static final Map<String,String> OIL_DAMAGE;
    public static final Map<String,String> OIL_DAMAGE_REVERSE;
    //非持久性油类污染损害民事责任保险或其他财务保证证书
    public static final Map<String,String> NON_PERSISTENT;
    public static final Map<String,String> NON_PERSISTENT_REVERSE;
    static {
        FUEL_POLLUTION = MapUtil.builder(new HashMap<String,String>())
                .put("结束时间(中文)", "vaildDate1")
                .put("结束时间(英文)", "vaildDate2")
                .put("X海事局(中文)", "issueDept1")
                .put("X海事局(英文)", "issueDept2")
                .put("名称(中文)", "name0")
                .put("地址(中文)", "address0")
                .put("地点(中文)", "place1")
                .put("地点(英文)", "place2")
                .put("颁证日期(中文)", "date1")
                .put("颁证日期(英文)", "date2")
                .put("签证官员(中文)", "official1")
                .put("签证官员职务(中文)", "IssueJob1")
                .put("签证官员(英文)", "official2")
                .put("签证官员职务(英文)", "IssueJob2")
                .put("中文船名", "shipsName1")
                .put("英文船名", "shipsName2")
                .put("船舶编号/呼号", "shipNumber")
                .put("IMO编号", "IMONumber")
                .put("船籍港(中文)", "port1")
                .put("船籍港(英文)", "port2")
                .put("船舶所有人和地址(中文)", "address1")
                .put("船舶所有人和地址(英文)", "address2")
                .put("保险类别(中文)", "SecurityType1")
                .put("保险类别(英文)", "SecurityType2")
                .put("保险时间(中文)", "Duration1")   //securityDate1 调整为 Duration1
                .put("保险时间(英文)", "Duration2")
                .put("保险机构名称(英文)", "name")
                .put("保险机构地址(英文)", "address")
                .put("证照编号", "certificateNumber")
                .build();
        FUEL_POLLUTION_REVERSE = MapUtil.reverse(FUEL_POLLUTION);
        OIL_DAMAGE = MapUtil.builder(new HashMap<String,String>())
                .put("结束时间(中文)", "vaildDate1")
                .put("结束时间(英文)", "vaildDate2")  //validDate2 调整为 vaildDate2
                .put("X海事局(中文)", "IssuedGovernment1")
                .put("X海事局(英文)", "IssuedGovernment2")
                .put("地点(中文)", "IssueDeptAddress1")
                .put("地点(英文)", "IssueDeptAddress2")
                .put("颁证日期(中文)", "IssueDeptDate1")
                .put("颁证日期(英文)", "IssueDeptDate2")
                .put("签证官员(中文)", "IssuePeople1")
                .put("签证官员职务(中文)", "IssueJob1")
                .put("签证官员(英文)", "IssuePeople2")
                .put("签证官员职务(英文)", "IssueJob2")
                .put("中文船名", "shipsName1")
                .put("英文船名", "shipsName2")
                .put("船舶编号", "shipsCode")
                .put("IMO编号", "IMOshipsCode")
                .put("船籍港(中文)", "shipsArea1")
                .put("船籍港(英文)", "shipsArea2")
                .put("船舶所有人和地址(中文)", "shipsAddress1")
                .put("船舶所有人和地址(英文)", "shipsAddress2")
                .put("保险类别(中文)", "securityType1")
                .put("保险类别(英文)", "securityType2")
                .put("保险时间(中文)", "securityDate1")
                .put("保险时间(英文)", "securityDate2")
                .put("保险机构名称(中文)", "guarantorName1")
                .put("保险机构名称(英文)", "guarantorName2")
                .put("保险机构地址(中文)", "guarantorAddress1")
                .put("保险机构地址(英文)", "guarantorAddress2")
                .put("证照编号", "certificateNumber")
                .build();
        OIL_DAMAGE_REVERSE = MapUtil.reverse(OIL_DAMAGE);
        NON_PERSISTENT = MapUtil.builder(new HashMap<String,String>())
                .put("结束时间", "validDate")
                .put("X海事局", "issueDept")
                .put("地点", "IssueDeptArea")
                .put("颁证日期", "IssueDeptDate")
                .put("中文船名", "shipsName")
                .put("船舶编号", "shipsCode")
                .put("IMO编号", "IMOshipsCode")
                .put("船籍港", "shipsArea")
                .put("船舶所有人和地址", "shipsMaster")
                .put("保险类别", "ensureType")
                .put("保险时间", "ensureDate")
                .put("保险机构名称", "guarantor")
                .put("保险机构地址", "guarantorArea1")
                .put("保险机构地址2", "guarantorArea2")
                .put("证照编号", "widthCode")
                .build();
        NON_PERSISTENT_REVERSE = MapUtil.reverse(NON_PERSISTENT);
    }
}

package com.js.hszpt.dto;

import lombok.Data;

import java.util.List;

/**
 * 外网查询参数DTO
 */
@Data
public class ExternalNetworkQueryDto {
    //验证码id
    private String captchaId;
    //验证码
    private String code;
    //ID
    private String id;
    //证书编号
    private String certificateNum;
    //船舶名称 可中英文查询
    private String shipName;
    //证书名称
    private String certificateTypeCode;
    //IMO船舶识别号
    private String shipImo;
    //船舶编号或呼号
    private String shipCallSign;
    //签发机关
    private List<String> issuOrgCode3;
    private String statusFlag;
    
    // 新增查询参数
    private String certPrintNo;            // 证书印刷号
    private String holderIdentityNumber;   // 身份证号
    private String certificateType;        // 证照类型名称
}

package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.hszpt.entity.LawWorkflowBpm;
import com.js.hszpt.vo.CertTypeDirectoryQueryVO;
import com.js.hszpt.vo.CertTypeDirectoryTodoVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * @ClassName:  LawWorkflowBpm   
 * @Description:TODO(待办任务表数据处理层)   
 * @author:   System Generation 
 */
public interface LawWorkflowBpmDao extends BaseMapper<LawWorkflowBpm> {

    /**
     * 查询证照类型目录待办列表
     * @param page 分页参数
     * @param queryVO 查询条件
     * @param currentUserId 当前用户ID
     * @return 待办列表
     */
    List<CertTypeDirectoryTodoVO> queryCertTypeDirectoryTodoList(
        @Param("page") Page<CertTypeDirectoryTodoVO> page,
        @Param("query") CertTypeDirectoryQueryVO queryVO,
        @Param("currentUserId") String currentUserId
    );

    /**
     * 根据执行ID删除工作流数据
     * @param executionId 执行ID
     * @return 删除的记录数
     */
    @Delete("DELETE FROM law_workflow_bpm WHERE execution_id = #{executionId}")
    int deleteByExecutionId(@Param("executionId") String executionId);

}
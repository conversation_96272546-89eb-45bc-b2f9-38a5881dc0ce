<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.DwsCertificateUsageMapper">

    <!-- 按下级机构统计使用情况 -->
    <select id="statisticsBySubOrg" resultType="com.js.hszpt.dto.SubOrgUsageStatisticsDTO">
        SELECT
        d.CODE as orgCode,
        d.NAME as orgName,
        SUM(u.uasge_count) as usageCount
        FROM
        dws_ctf_certificate_uasge u
        JOIN
        dws_ctf_sys_dept d ON u.issuer_code LIKE CONCAT(d.CODE, '%')
        WHERE 1=1
        <choose>
            <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                AND d.PARENT_ID = (select dept_id from dws_ctf_sys_dept where code =#{param.loginUserOrgCode})
                AND u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
            </when>
            <otherwise>
                AND GOV_LEVEL = '2'
            </otherwise>
        </choose>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性加载所有初始参数对应的目录节点
            SELECT etd_parent.CERT_TYPE_DIR_ID, etd_parent.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID, etd_child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM cte_child cc
            WHERE cc.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.uasge_type = #{param.usageType}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(u.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(u.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(u.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(u.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(u.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(#{param.startTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.startTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(#{param.endTime}, 1, 4),
            CASE
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(#{param.endTime}, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND u.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND u.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_name IN (
                select certificate_name from dws_ctf_certificate_config where certificate_type_code in
                <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            )
        </if>
        GROUP BY d.CODE, d.NAME
        ORDER BY usageCount DESC
    </select>

    <!-- 根据机构编码获取机构名称 -->
    <select id="getOrgNameByOrgCode" resultType="java.lang.String">
        SELECT NAME FROM dws_ctf_sys_dept WHERE CODE = #{orgCode}
    </select>

    <!-- 获取机构的直接下级机构列表 -->
    <select id="getDirectSubOrgCodes" resultType="java.lang.String">
        SELECT code FROM dws_ctf_sys_dept WHERE PARENT_ID = (
            SELECT DEPT_ID FROM dws_ctf_sys_dept WHERE code = #{parentOrgCode}
        ) AND DEL_FLAG = '0'
    </select>

    <!-- 按年份和下级机构统计使用情况 -->
    <select id="statisticsByYearAndSubOrg" resultType="com.js.hszpt.dto.TimeTypeSubOrgUsageStatisticsDTO">
        SELECT
        d.CODE as orgCode,
        d.NAME as orgName,
        SUBSTR(u.ds_certificate_date, 1, 4) as year,
        SUM(u.uasge_count) as usageCount
        FROM
        dws_ctf_certificate_uasge u
        JOIN
        dws_ctf_sys_dept d ON u.issuer_code LIKE CONCAT(d.CODE, '%')
        WHERE 1=1
        <choose>
            <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                AND d.PARENT_ID = (select dept_id from dws_ctf_sys_dept where code =#{param.loginUserOrgCode})
                AND u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
            </when>
            <otherwise>
                AND d.GOV_LEVEL &lt;= '2'
            </otherwise>
        </choose>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性加载所有初始参数对应的目录节点
            SELECT etd_parent.CERT_TYPE_DIR_ID, etd_parent.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID, etd_child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM cte_child cc
            WHERE cc.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.uasge_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND u.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_name IN (
            select certificate_name from dws_ctf_certificate_config where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY d.CODE, d.NAME, SUBSTR(u.ds_certificate_date, 1, 4)
        ORDER BY d.CODE, year
    </select>

    <!-- 按下级机构统计使用情况并计算占比 -->
    <select id="statisticsBySubOrgWithRatio" resultType="com.js.hszpt.dto.SubOrgUsageRatioStatisticsDTO">
        <choose>
            <when test='param.orgType == "1"'>
                WITH sys_dept AS (
                select NAME, CODE from dws_ctf_sys_dept
                where 1=1
                <choose>
                    <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                        AND PARENT_ID = (select dept_id from dws_ctf_sys_dept where code =#{param.loginUserOrgCode})
                        AND code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
                    </when>
                    <otherwise>
                        AND GOV_LEVEL = '2'
                    </otherwise>
                </choose>
                <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
                    AND (
                    <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                        code LIKE CONCAT(#{issuerCode}, '%')
                    </foreach>
                    )
                </if>
                )
            </when>
            <otherwise>
                WITH sys_dept AS (select msa_org_code as CODE,org_name as NAME from dws_ctf_other_org
                <where>
                    <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                        msa_org_code like CONCAT(#{param.loginUserOrgCode}, '%')
                    </if>
                </where>
                group by msa_org_code,org_name
                )
            </otherwise>
        </choose>
        SELECT
        sd.CODE as orgCode,
        sd.NAME as orgName,
        COALESCE(SUM(u.uasge_count),0) as usageCount,
        0.0 as usageRatio,
        0 as isSummary
        FROM sys_dept sd
        <choose>
            <when test='param.orgType == "1"'>
                LEFT JOIN
            </when>
            <otherwise>
                INNER JOIN
            </otherwise>
        </choose>
            (select * from dws_ctf_certificate_uasge u
        WHERE org_type = #{param.orgType}
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性加载所有初始参数对应的目录节点
            SELECT etd_parent.CERT_TYPE_DIR_ID, etd_parent.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID, etd_child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM cte_child cc
            WHERE cc.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.uasge_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            <choose>
                <when test="param.timeType == 'year'">
                    AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN SUBSTR(replace(#{param.startTime},'-',''),1,4) AND
                    SUBSTR(replace(#{param.endTime},'-',''),1,4)
                </when>
                <when test="param.timeType == 'quarter'">
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 6) > SUBSTR(replace(#{param.startTime},'-',''),1,6)
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = SUBSTR(replace(#{param.startTime},'-',''),1,4)
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2),'9999') / 3) &gt;=
                    CEIL(TO_NUMBER(SUBSTR(replace(#{param.startTime},'-',''), 5, 2),'9999') / 3)
                    )
                    )
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 6) &lt; SUBSTR(replace(#{param.endTime},'-',''),1,6)
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = SUBSTR(replace(#{param.endTime},'-',''),1,4)
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2),'9999') / 3) &lt;=
                    CEIL(TO_NUMBER(SUBSTR(replace(#{param.endTime},'-',''), 5, 2),'9999') / 3)
                    )
                    )
                </when>
                <when test="param.timeType == 'month'">
                    AND SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN
                    SUBSTR(replace(#{param.startTime},'-',''),1,6) AND
                    SUBSTR(replace(#{param.endTime},'-',''),1,6)
                </when>
                <otherwise>
                    AND u.ds_certificate_date BETWEEN replace(#{param.startTime},'-','') AND
                    replace(#{param.endTime},'-','')
                </otherwise>
            </choose>
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND u.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_name IN (
            select certificate_name from dws_ctf_certificate_config where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        )u on
        <choose>
            <when test='param.orgType == "1"'>
                u.issuer_code like sd.CODE || '%'
            </when>
            <otherwise>
                u.issuer_name = sd.NAME
            </otherwise>
        </choose>
        GROUP BY orgCode,orgName
        ORDER BY usageCount DESC
    </select>

    <!-- 获取登录人所在机构的总使用量 -->
    <select id="getTotalUsageCount" resultType="java.lang.Long">
        SELECT
        COALESCE(SUM(u.uasge_count),0) as totalCount
        FROM
        dws_ctf_certificate_uasge u
        WHERE 1=1
        <choose>
            <when test='param.orgType == "1"'>
                and u.org_type = #{param.orgType}
                <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                    and u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
                </if>
                <if test='param.issuerCodes != null and param.issuerCodes.size() > 0'>
                    AND (
                    <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                        u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
                    </foreach>
                    )
                </if>
            </when>
            <otherwise>
                and u.org_type = #{param.orgType} and u.issuer_name in (select org_name as orgName from dws_ctf_other_org
                <where>
                    <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                        and msa_org_code like CONCAT(#{param.loginUserOrgCode}, '%')
                    </if>
                </where>
                group by msa_org_code,org_name)
            </otherwise>
        </choose>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性加载所有初始参数对应的目录节点
            SELECT etd_parent.CERT_TYPE_DIR_ID, etd_parent.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID, etd_child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM cte_child cc
            WHERE cc.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.uasge_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            <choose>
                <when test="param.timeType == 'year'">
                    AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN SUBSTR(replace(#{param.startTime},'-',''),1,4) AND SUBSTR(replace(#{param.endTime},'-',''),1,4)
                </when>
                <when test="param.timeType == 'quarter'">
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 6) > SUBSTR(replace(#{param.startTime},'-',''),1,6)
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = SUBSTR(replace(#{param.startTime},'-',''),1,4)
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2),'9999') / 3) &gt;= CEIL(TO_NUMBER(SUBSTR(replace(#{param.startTime},'-',''), 5, 2),'9999') / 3)
                    )
                    )
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 6) &lt; SUBSTR(replace(#{param.endTime},'-',''),1,6)
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = SUBSTR(replace(#{param.endTime},'-',''),1,4)
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2),'9999') / 3) &lt;= CEIL(TO_NUMBER(SUBSTR(replace(#{param.endTime},'-',''), 5, 2),'9999') / 3)
                    )
                    )
                </when>
                <when test="param.timeType == 'month'">
                    and SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN
                    SUBSTR(replace(#{param.startTime},'-',''),1,6) AND
                    SUBSTR(replace(#{param.endTime},'-',''),1,6)
                </when>
                <otherwise>
                    AND u.ds_certificate_date BETWEEN replace(#{param.startTime},'-','') AND replace(#{param.endTime},'-','')
                </otherwise>
            </choose>
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND u.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_name IN (
            select certificate_name from dws_ctf_certificate_config where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
    </select>

    <!-- 按持证主体类别统计使用情况并计算占比 -->
    <select id="statisticsByHolderCategoryWithRatio" resultType="com.js.hszpt.dto.HolderCategoryUsageStatisticsDTO">
        with dict as(SELECT b.title,b.value FROM dws_ctf_dict a, dws_ctf_dict_data b
        WHERE a.type = 'CERTIFICATE_HOLDER_CATEGORY'
        AND a.id = b.dict_id)
        SELECT
            d.value as holderCategoryCode,
            d.title as holderCategoryName,
            coalesce(SUM(u.uasge_count),0) as usageCount,
            0.0 as usageRatio,
            0 as isSummary
        FROM
        dws_ctf_certificate_uasge u right join dict d on d.value = u.holder_category_code
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            and u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性加载所有初始参数对应的目录节点
            SELECT etd_parent.CERT_TYPE_DIR_ID, etd_parent.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID, etd_child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM cte_child cc
            WHERE cc.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.uasge_type = #{param.usageType}
        </if>
        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            <choose>
                <when test="param.timeType == 'year'">
                    AND SUBSTR(u.ds_certificate_date, 1, 4) BETWEEN SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,4) AND SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,4)
                </when>
                <when test="param.timeType == 'quarter'">
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) > SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,4)
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,4)
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2),'9999') / 3) &gt;= CEIL(TO_NUMBER(SUBSTR(REPLACE(#{param.startTime}, '-', ''),5,2),'9999') / 3)
                    )
                    )
                    AND (
                    SUBSTR(u.ds_certificate_date, 1, 4) &lt;SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,4)
                    OR (
                    SUBSTR(u.ds_certificate_date, 1, 4) = SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,4)
                    AND CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2),'9999') / 3) &lt;= CEIL(TO_NUMBER(SUBSTR(REPLACE(#{param.endTime}, '-', ''),5,2),'9999') / 3)
                    )
                    )
                </when>
                <when test="param.timeType == 'month'">
                    AND SUBSTR(u.ds_certificate_date, 1, 6) BETWEEN
                    SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,6) AND
                    SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,6)
                </when>
                <otherwise>
                    AND u.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
                </otherwise>
            </choose>
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND u.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_name IN (
            select certificate_name from dws_ctf_certificate_config where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY holderCategoryCode,holderCategoryName
        ORDER BY usageCount DESC
    </select>

    <!-- 获取持证主体类别名称 -->
    <select id="getHolderCategoryName" resultType="java.lang.String">
        SELECT b.title FROM dws_ctf_dict a, dws_ctf_dict_data b
        WHERE a.type = 'CERTIFICATE_HOLDER_CATEGORY'
          AND a.id = b.dict_id
          AND b.value = #{holderCategoryCode}
    </select>

    <!-- 按时间统计使用情况并计算占比 -->
    <select id="statisticsByTimeWithRatio" resultType="com.js.hszpt.dto.TimeUsageStatisticsDTO">
        WITH
        -- 1. 动态生成完整时间序列
        date_series AS (
        SELECT
        generate_series(
        TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD'),
        TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD'),
        CASE #{param.timeType}
        WHEN 'year' THEN '1 year'::interval
        WHEN 'quarter' THEN '3 months'::interval
        WHEN 'month' THEN '1 month'::interval
        ELSE '1 day'::interval
        END
        )::date AS period_date
        ),
        -- 2. 格式化时间维度字段
        formatted_dates AS (
        SELECT
        CASE #{param.timeType}
        WHEN 'year' THEN TO_CHAR(period_date, 'YYYY年')
        WHEN 'quarter' THEN
        TO_CHAR(period_date, 'YYYY年') || CEIL(EXTRACT(MONTH FROM period_date)/3.0) || '季'
        WHEN 'month' THEN TO_CHAR(period_date, 'YYYY年MM月')
        ELSE TO_CHAR(period_date, 'YYYY年MM月DD日')
        END AS timePoint,
        CASE #{param.timeType}  -- 生成关联键
        WHEN 'year' THEN TO_CHAR(period_date, 'YYYY')
        WHEN 'quarter' THEN
        TO_CHAR(period_date, 'YYYY') || '-' || CEIL(EXTRACT(MONTH FROM period_date)/3.0)
        WHEN 'month' THEN TO_CHAR(period_date, 'YYYYMM')
        ELSE TO_CHAR(period_date, 'YYYYMMDD')
        END AS date_key
        FROM date_series
        GROUP BY timePoint, date_key  -- 按时间粒度去重
        )
        -- 3. 主查询关联时间维度
        SELECT
        d.timePoint,
        COALESCE(SUM(u.uasge_count), 0) AS usageCount,  -- 关键点：无数据时填充0
        0.0 AS usageRatio,
        0 AS isSummary
        FROM formatted_dates d
        LEFT JOIN dws_ctf_certificate_uasge u
        ON
        -- 动态时间关联逻辑
        CASE #{param.timeType}
        WHEN 'year' THEN SUBSTR(u.ds_certificate_date, 1, 4)
        WHEN 'quarter' THEN
        SUBSTR(u.ds_certificate_date, 1, 4) ||  '-'|| CEIL(TO_NUMBER(SUBSTR(u.ds_certificate_date, 5, 2),'9999')/3)
        WHEN 'month' THEN SUBSTR(u.ds_certificate_date, 1, 6)
        ELSE u.ds_certificate_date
        END = d.date_key
        -- 保留业务条件，不影响时间维度
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            AND u.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                u.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性加载所有初始参数对应的目录节点
            SELECT etd_parent.CERT_TYPE_DIR_ID, etd_parent.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID, etd_child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM cte_child cc
            WHERE cc.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.uasge_type = #{param.usageType}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND u.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_name IN (
            select certificate_name from dws_ctf_certificate_config where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY d.timePoint
        ORDER BY d.timePoint
    </select>

    <!-- 获取证照状态名称 -->
    <select id="getCertificateStatusName" resultType="java.lang.String">
        SELECT b.title 
        FROM dws_ctf_dict a, dws_ctf_dict_data b
        WHERE a.type = 'CERTIFICATE_STATUS'
          AND a.id = b.dict_id
          AND b.value = #{statusCode}
    </select>

    <!-- 获取证照类型名称 -->
    <select id="getCertificateTypeName" resultType="java.lang.String">
        SELECT CERTIFICATE_TYPE_NAME 
        FROM dws_ctf_cert_type_directory 
        WHERE CERTIFICATE_TYPE_CODE = #{certificateTypeCode}
    </select>

    <select id="statisticsByTimeTypeAndSubOrg" resultType="com.js.hszpt.dto.TimeTypeSubOrgUsageStatisticsDTO">
        WITH
        -- 1. 动态生成时间序列（支持年/季/月/日）
        date_series AS (
        SELECT
        generate_series(
        TO_DATE(REPLACE(#{param.startTime}, '-', ''), 'YYYYMMDD'),
        TO_DATE(REPLACE(#{param.endTime}, '-', ''), 'YYYYMMDD'),
        CASE #{param.timeType}
        WHEN 'day' THEN '1 day'::interval
        WHEN 'month' THEN '1 month'::interval
        WHEN 'quarter' THEN '3 months'::interval
        WHEN 'year' THEN '1 year'::interval
        END
        )::date AS period_date
        ),
        -- 2. 格式化时间维度字段
        periods AS (
        SELECT
        CASE #{param.timeType}
        WHEN 'day' THEN TO_CHAR(period_date, 'YYYY年MM月DD日')
        WHEN 'month' THEN TO_CHAR(period_date, 'YYYY年MM月')
        WHEN 'quarter' THEN TO_CHAR(period_date, 'YYYY') || '年' || EXTRACT(QUARTER FROM period_date) || '季'
        WHEN 'year' THEN TO_CHAR(period_date, 'YYYY年')
        END AS timePoint,
        CASE #{param.timeType}
        WHEN 'day' THEN period_date
        WHEN 'month' THEN DATE_TRUNC('month', period_date)
        WHEN 'quarter' THEN DATE_TRUNC('quarter', period_date)
        WHEN 'year' THEN DATE_TRUNC('year', period_date)
        END AS period_date_trunc
        FROM date_series
        GROUP BY period_date_trunc, timePoint  -- 按粒度去重
        ),
        <choose>
            <when test='param.orgType == "1"'>
                -- 3. 动态获取所有下属机构
                sub_depts AS (
                SELECT CODE, NAME
                FROM dws_ctf_sys_dept
                WHERE 1=1
                <choose>
                    <when test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                        AND PARENT_ID = (SELECT dept_id FROM dws_ctf_sys_dept WHERE code = #{param.loginUserOrgCode})
                        AND CODE LIKE CONCAT(#{param.loginUserOrgCode}, '%')
                    </when>
                    <otherwise>
                        AND GOV_LEVEL = '2'
                    </otherwise>
                </choose>
                <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
                    AND (
                    <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                        code LIKE CONCAT(#{issuerCode}, '%')
                    </foreach>
                    )
                </if>
                )
            </when>
            <otherwise>
                -- 3. 获取所有机构
                sub_depts AS (
                select msa_org_code as CODE,org_name as NAME from dws_ctf_other_org
                <where>
                    <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
                        msa_org_code like CONCAT(#{param.loginUserOrgCode}, '%')
                    </if>
                </where>
                group by msa_org_code,org_name
                )
            </otherwise>
        </choose>

        -- 4. 主查询（关联机构、时间序列，并填充缺失数据为0）
        SELECT
        d.CODE AS orgCode,
        d.NAME AS orgName,
        p.timePoint,
        COALESCE(SUM(u.uasge_count), 0) AS usageCount
        FROM sub_depts d
        CROSS JOIN periods p  -- 生成所有机构和时间组合
            <choose>
                <when test='param.orgType == "1"'>
                    LEFT JOIN dws_ctf_certificate_uasge u
                    ON u.issuer_code like d.CODE || '%'
                </when>
                <otherwise>
                    INNER JOIN dws_ctf_certificate_uasge u
                    ON u.issuer_name = d.NAME
                </otherwise>
            </choose>
            and p.period_date_trunc = CASE #{param.timeType}
        WHEN 'day' THEN TO_DATE(u.ds_certificate_date, 'YYYYMMDD')::date
        WHEN 'month' THEN DATE_TRUNC('month', TO_DATE(u.ds_certificate_date, 'YYYYMMDD')::date)
        WHEN 'quarter' THEN DATE_TRUNC('quarter', TO_DATE(u.ds_certificate_date, 'YYYYMMDD')::date)
        WHEN 'year' THEN DATE_TRUNC('year', TO_DATE(u.ds_certificate_date, 'YYYYMMDD')::date)
        END and org_type = #{param.orgType}
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性加载所有初始参数对应的目录节点
            SELECT etd_parent.CERT_TYPE_DIR_ID, etd_parent.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID, etd_child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM cte_child cc
            WHERE cc.CERTIFICATE_TYPE_CODE = u.certificate_type_code
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND u.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND u.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND u.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND u.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND u.uasge_type = #{param.usageType}
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND u.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND u.certificate_name IN (
            select certificate_name from dws_ctf_certificate_config where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY d.CODE, d.NAME, p.timePoint
        ORDER BY p.timePoint, usageCount DESC
    </select>

    <!-- 按证照类型统计使用情况并计算占比 -->
    <select id="statisticsByType" resultType="com.js.hszpt.dto.CertificateTypeUsageStatisticsDTO">
        WITH RECURSIVE type_hierarchy AS (
        SELECT
        CERT_TYPE_DIR_ID,
        CERTIFICATE_TYPE_CODE,
        CERTIFICATE_TYPE_NAME,
        PARENT_ID,
        CERTIFICATE_TYPE_CODE AS first_type_code,
        CERTIFICATE_TYPE_NAME AS first_type_name
        FROM dws_ctf_cert_type_directory
        WHERE PARENT_ID = '-1'  -- 根据实际根节点调整
        UNION ALL
        SELECT
        child.CERT_TYPE_DIR_ID,
        child.CERTIFICATE_TYPE_CODE,
        child.CERTIFICATE_TYPE_NAME,
        child.PARENT_ID,
        parent.first_type_code,
        parent.first_type_name
        FROM dws_ctf_cert_type_directory child
        INNER JOIN type_hierarchy parent
        ON child.PARENT_ID = parent.CERT_TYPE_DIR_ID
        )
        SELECT
        firstLevelTypeCode,
        firstLevelTypeName,
        COALESCE(SUM(usageCount), 0) AS usageCount  -- 确保根类型无数据时显示0
        FROM (
        SELECT
        th.first_type_code AS firstLevelTypeCode,
        th.first_type_name AS firstLevelTypeName,
        COALESCE(SUM(i.uasge_count), 0) AS usageCount  -- 子类型无数据时显示0
        FROM type_hierarchy th
        LEFT JOIN dws_ctf_certificate_uasge i  -- 左连接保留所有类型
        ON i.certificate_type_code = th.CERTIFICATE_TYPE_CODE
        -- 所有过滤条件移至 ON 子句（避免过滤掉无数据的类型）
        <if test="param.loginUserOrgCode != null and param.loginUserOrgCode != ''">
            AND i.issuer_code LIKE CONCAT(#{param.loginUserOrgCode}, '%')
        </if>
        <if test="param.issuerCodes != null and param.issuerCodes.size() > 0">
            AND (
            <foreach collection="param.issuerCodes" item="issuerCode" separator=" OR ">
                i.issuer_code LIKE CONCAT(#{issuerCode}, '%')
            </foreach>
            )
        </if>
        <if test="param.certificateTypeCodes != null and param.certificateTypeCodes.size() > 0">
            AND EXISTS (
            WITH RECURSIVE cte_child AS (
            -- 一次性加载所有初始参数对应的目录节点
            SELECT etd_parent.CERT_TYPE_DIR_ID, etd_parent.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_parent
            WHERE etd_parent.CERT_TYPE_DIR_ID IN
            <foreach collection="param.certificateTypeCodes" item="typeCode" open="(" separator="," close=")">
                #{typeCode}
            </foreach>
            UNION ALL
            -- 递归获取所有子节点
            SELECT etd_child.CERT_TYPE_DIR_ID, etd_child.CERTIFICATE_TYPE_CODE
            FROM dws_ctf_cert_type_directory etd_child
            INNER JOIN cte_child ON etd_child.PARENT_ID = cte_child.CERT_TYPE_DIR_ID
            )
            SELECT 1
            FROM cte_child cc
            WHERE cc.CERTIFICATE_TYPE_CODE = i.certificate_type_code
            )
        </if>
        <if test="param.holderCategoryCodes != null and param.holderCategoryCodes.size() > 0">
            AND i.holder_category_code IN
            <foreach collection="param.holderCategoryCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.matterNatureCodes != null and param.matterNatureCodes.size() > 0">
            AND i.matter_nature_code IN
            <foreach collection="param.matterNatureCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="param.applicationSourceCode != null and param.applicationSourceCode != ''">
            AND i.application_source_code = #{param.applicationSourceCode}
        </if>
        <if test="param.statusCode != null and param.statusCode != ''">
            AND i.certificate_status_code = #{param.statusCode}
        </if>
        <if test="param.usageType != null and param.usageType != ''">
            AND i.uasge_type = #{param.usageType}
        </if>
        <if test="param.timeType == 'year' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 4) BETWEEN SUBSTR(REPLACE(#{param.startTime},'-',''), 1, 4) AND SUBSTR(REPLACE(#{param.endTime},'-',''), 1, 4)
        </if>
        <if test="param.timeType == 'quarter' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND CONCAT(SUBSTR(i.ds_certificate_date, 1, 4),
            CASE
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(i.ds_certificate_date, 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            BETWEEN
            CONCAT(SUBSTR(REPLACE(#{param.startTime},'-',''), 1, 4),
            CASE
            WHEN SUBSTR(REPLACE(#{param.startTime},'-',''), 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(REPLACE(#{param.startTime},'-',''), 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(REPLACE(#{param.startTime},'-',''), 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(REPLACE(#{param.startTime},'-',''), 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
            AND
            CONCAT(SUBSTR(REPLACE(#{param.endTime},'-',''), 1, 4),
            CASE
            WHEN SUBSTR(REPLACE(#{param.endTime},'-',''), 5, 2) BETWEEN '01' AND '03' THEN 'Q1'
            WHEN SUBSTR(REPLACE(#{param.endTime},'-',''), 5, 2) BETWEEN '04' AND '06' THEN 'Q2'
            WHEN SUBSTR(REPLACE(#{param.endTime},'-',''), 5, 2) BETWEEN '07' AND '09' THEN 'Q3'
            WHEN SUBSTR(REPLACE(#{param.endTime},'-',''), 5, 2) BETWEEN '10' AND '12' THEN 'Q4'
            END)
        </if>
        <if test="param.timeType == 'month' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND SUBSTR(i.ds_certificate_date, 1, 6) BETWEEN SUBSTR(REPLACE(#{param.startTime}, '-', ''),1,6) AND SUBSTR(REPLACE(#{param.endTime}, '-', ''),1,6)
        </if>
        <if test="param.timeType == 'day' and param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != ''">
            AND i.ds_certificate_date BETWEEN REPLACE(#{param.startTime}, '-', '') AND REPLACE(#{param.endTime}, '-', '')
        </if>
        <if test="param.relatedMatterName != null and param.relatedMatterName != ''">
            AND i.ctf_affair_name in
            <foreach collection="param.relatedMatterName.split(',')" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        <if test="param.certificateIds != null and param.certificateIds.size() > 0">
            AND i.certificate_name IN (
            select certificate_name from dws_ctf_certificate_config where certificate_type_code in
            <foreach collection="param.certificateIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        GROUP BY th.first_type_code, th.first_type_name, th.CERTIFICATE_TYPE_CODE  -- 按层级聚合
        ) sub
        GROUP BY firstLevelTypeCode, firstLevelTypeName
        ORDER BY usageCount DESC
    </select>

</mapper> 
package com.js.hszpt.vo;

import com.js.core.common.vo.PageVo;
import lombok.Data;

import java.util.List;

/**
 * 证照使用统计请求参数VO
 */
@Data
public class CertificateUsageStatisticsVO extends PageVo {

    /**
     * 签发机构编码列表（必填）
     */
    private List<String> issuerCodes;

    /**
     * 机构类型
     */
    private String orgType;

    /**
     * 证照类型代码列表（可选）
     */
    private List<String> certificateTypeCodes;

    /**
     * 持证主体类别代码列表（可选）
     */
    private List<String> holderCategoryCodes;

    /**
     * 事项性质代码列表（可选）
     */
    private List<String> matterNatureCodes;

    /**
     * 申请来源代码（可选）
     */
    private String applicationSourceCode;

    /**
     * 证照状态代码（可选）
     */
    private String statusCode;

    /**
     */
    private String timeType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 关联事项名称（可选）
     */
    private String relatedMatterName;

    /**
     * 证照ID列表（可选）
     */
    private List<String> certificateIds;

    /**
     * 登录人所在机构编码（必填）
     */
    private String loginUserOrgCode;
    
    /**
     * 使用类型（1-下载 2-核验）
     */
    private String usageType;
} 
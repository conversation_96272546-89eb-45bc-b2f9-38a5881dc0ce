package com.js.hszpt.utils.encrypt;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

public class RSAEncryptionUtil {

    /**
     * 生成RSA密钥对
     *
     * @return 密钥对
     * @throws NoSuchAlgorithmException 密钥生成算法不支持异常
     */
    public static KeyPair generateRSAKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        return keyPairGenerator.generateKeyPair();
    }

    /**
     * 获取RSA公钥的Base64编码字符串
     *
     * @return RSA公钥Base64编码字符串
     */
    public static String getRSAPublicKeyString(PublicKey publicKey) {
        KeyFactory keyFactory;
        try {
            keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKey.getEncoded());
            return Base64.getEncoder().encodeToString(keyFactory.generatePublic(publicKeySpec).getEncoded());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据Base64编码的字符串还原为RSA公钥
     *
     * @param publicKeyString RSA公钥Base64编码字符串
     * @return RSA公钥
     */
    public static PublicKey getPublicKey(String publicKeyString) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKeyString));
            return keyFactory.generatePublic(publicKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取RSA私钥的Base64编码字符串
     *
     * @return RSA私钥Base64编码字符串
     */
    public static String getRSAPrivateKeyString(PrivateKey privateKey) {
        KeyFactory keyFactory;
        try {
            keyFactory = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(privateKey.getEncoded());
            return Base64.getEncoder().encodeToString(keyFactory.generatePrivate(privateKeySpec).getEncoded());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据Base64编码的字符串还原为RSA私钥
     *
     * @param privateKeyString RSA私钥Base64编码字符串
     * @return RSA私钥
     */
    public static PrivateKey getPrivateKey(String privateKeyString) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKeyString));
            return keyFactory.generatePrivate(privateKeySpec);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 非对称加密算法RSA加密
     *
     * @param plaintext 明文
     * @param publicKey 公钥
     * @return 加密后的密文
     * @throws Exception 加密异常
     */
    public static String encryptWithRSA(String plaintext, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        KeyPair keyPair = generateRSAKeyPair();
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 非对称加密算法RSA解密
     *
     * @param ciphertext 密文
     * @param privateKey 私钥
     * @return 解密后的明文
     * @throws Exception 解密异常
     */
    public static String decryptWithRSA(String ciphertext, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        KeyPair keyPair = generateRSAKeyPair();
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(ciphertext));
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    /**
     * RSA、SM2加解密
     * @param args
     */
    public static void main(String[] args) {
        try {
            PublicKey publicKey = RSAEncryptionUtil.getPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyY/PeJ0UjYwH/muM0+4yRtsxLRpu9Q5NR6VeG0utFciq8ve+79fIso+eXckQBlSFTknCtscvQxftjUML2UvIJEvurrfCpK3bgAGR7Ia9HxqC0pTIE+CHrurO6NrT2uU1+3WRvl9cJK/mJ3X0V4kMdj6Cr2DKVLXj9ZMXDcbqYAdmsB3JqQBP9la2Oe1WmxCTqM+tkyH4AA8S2CPV7miQ3r6MhMt/eaUMwSSXedTv4JrsGM8Rc3VlU+bWqVW6IQQMltV5m1VMvZPFVWTL9ov4H5niYfE36ZhPKHBvm5fAuWu3OSea4WlqUV/FZBxyJERPfmUFoLIe8Y+/pl7j7SZWAwIDAQAB");
            System.out.println("（传参解密公钥）RSA public：" + "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyY/PeJ0UjYwH/muM0+4yRtsxLRpu9Q5NR6VeG0utFciq8ve+79fIso+eXckQBlSFTknCtscvQxftjUML2UvIJEvurrfCpK3bgAGR7Ia9HxqC0pTIE+CHrurO6NrT2uU1+3WRvl9cJK/mJ3X0V4kMdj6Cr2DKVLXj9ZMXDcbqYAdmsB3JqQBP9la2Oe1WmxCTqM+tkyH4AA8S2CPV7miQ3r6MhMt/eaUMwSSXedTv4JrsGM8Rc3VlU+bWqVW6IQQMltV5m1VMvZPFVWTL9ov4H5niYfE36ZhPKHBvm5fAuWu3OSea4WlqUV/FZBxyJERPfmUFoLIe8Y+/pl7j7SZWAwIDAQAB");

            PrivateKey privateKey = RSAEncryptionUtil.getPrivateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDJj894nRSNjAf+a4zT7jJG2zEtGm71Dk1HpV4bS60VyKry977v18iyj55dyRAGVIVOScK2xy9DF+2NQwvZS8gkS+6ut8KkrduAAZHshr0fGoLSlMgT4Ieu6s7o2tPa5TX7dZG+X1wkr+YndfRXiQx2PoKvYMpUteP1kxcNxupgB2awHcmpAE/2VrY57VabEJOoz62TIfgADxLYI9XuaJDevoyEy395pQzBJJd51O/gmuwYzxFzdWVT5tapVbohBAyW1XmbVUy9k8VVZMv2i/gfmeJh8TfpmE8ocG+bl8C5a7c5J5rhaWpRX8VkHHIkRE9+ZQWgsh7xj7+mXuPtJlYDAgMBAAECggEAK7c7Ike/W2vRmAavho+U6/VCVyvygMfXTwC4xmbFZYIeQZAmI1LDvqaTjHe9OAJA31f0GTxeLckUxpySB1D+WZD2dYJ5fsoOX1b31Trr7cEMOqv2MQlzpCZdNfewFzu7V4yKTaXVEEfuPQTCN/ILd+Ha6jqt522DwZFjYKOR1b2iIlPana2YVtAe5MNpjR4xW/qonYNmumFgd9J2xq2NCzv/JzJmXi1G8Jj+QyZeh9eoamRqFVtUwMrTBnDG8iaq46IDQ3s8fLOuoEq1B+upWYR1fV7hZ4jAhx5zL4Wt8+2ngbwYlHrFO+6zIkZwsPjACmLe5smth0occxoXH5SPQQKBgQD732nVbxlEzgQ6cLQEq4VhjalJyl7teh8kKGTZWSEXndUSWg6hK0LTfzzW9ALICgDtv1XactN08gonPq347Dd1FpRhFTUD22oSAhdKh057GPWN451XqrYCLSKLJrp7SOV8YToZGbXWjBtb1SR19Lpyy4xFPkd86hgtboWs8yoUswKBgQDM3ViyPpTYACaUVhiJEvTxu9aqPOSJP5YwcF5eNwr2sy/2hn2o4H2UUgH+6IGw25GrCE26qacfv1f83+gZVR3oeGvojaU89i/6723UF00GyMwv3QUMZlLvzFDEwjizmSmz2TIVvkaVV8Pe7sqwhDvJaDHzryqeQ/0RrIvWhMeBcQKBgA5HDNXHdXQ8BWtWpi25l1b/U8BWE2l8ybUgAQ99CO4wyTpAFqI2NJSOmuFIdbvnLURM204FwkzXx5GP8QayRTaC8ChMrnOohWu96JoV73H06T6Az5N/ns+ixVmD+YAxkqipTkTL+03NJWPgQi/ZjpQhS7NFI4JGNtakdDlok1MXAoGAY+lHipaOzNE1/34FtUQzhXlQdfIAmxcRzknfHB/IDwzQxXBN9ICXfjCInKMULQbIghsonXKD+S2+YatpbIFgdrHUHrSF87c1KzJ948QBYt+nBWjLP4Lfy+dfNLZsJNr7xaSVkpkbn1YQRrc6zt+OsOlKA0hYhpst3uLByDs1eWECgYEAnwgjcV337Lf0bBLNeY3pzInCN82USKMfd7qR8KVcAWTatkC9ovW4FJ/cWRwVQYPJyxm2ZNVO65TaQElKcW9xlUi+esx9mp3go59R9CfnDitMnha/NG7Cb7m4LhBvRvNUtbDCJjj0Uc+wYRn7UtyUjYSxorgt8YGpAEPkwANIa3E=");
            System.out.println("（传参解密私钥）RSA private：" + "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDJj894nRSNjAf+a4zT7jJG2zEtGm71Dk1HpV4bS60VyKry977v18iyj55dyRAGVIVOScK2xy9DF+2NQwvZS8gkS+6ut8KkrduAAZHshr0fGoLSlMgT4Ieu6s7o2tPa5TX7dZG+X1wkr+YndfRXiQx2PoKvYMpUteP1kxcNxupgB2awHcmpAE/2VrY57VabEJOoz62TIfgADxLYI9XuaJDevoyEy395pQzBJJd51O/gmuwYzxFzdWVT5tapVbohBAyW1XmbVUy9k8VVZMv2i/gfmeJh8TfpmE8ocG+bl8C5a7c5J5rhaWpRX8VkHHIkRE9+ZQWgsh7xj7+mXuPtJlYDAgMBAAECggEAK7c7Ike/W2vRmAavho+U6/VCVyvygMfXTwC4xmbFZYIeQZAmI1LDvqaTjHe9OAJA31f0GTxeLckUxpySB1D+WZD2dYJ5fsoOX1b31Trr7cEMOqv2MQlzpCZdNfewFzu7V4yKTaXVEEfuPQTCN/ILd+Ha6jqt522DwZFjYKOR1b2iIlPana2YVtAe5MNpjR4xW/qonYNmumFgd9J2xq2NCzv/JzJmXi1G8Jj+QyZeh9eoamRqFVtUwMrTBnDG8iaq46IDQ3s8fLOuoEq1B+upWYR1fV7hZ4jAhx5zL4Wt8+2ngbwYlHrFO+6zIkZwsPjACmLe5smth0occxoXH5SPQQKBgQD732nVbxlEzgQ6cLQEq4VhjalJyl7teh8kKGTZWSEXndUSWg6hK0LTfzzW9ALICgDtv1XactN08gonPq347Dd1FpRhFTUD22oSAhdKh057GPWN451XqrYCLSKLJrp7SOV8YToZGbXWjBtb1SR19Lpyy4xFPkd86hgtboWs8yoUswKBgQDM3ViyPpTYACaUVhiJEvTxu9aqPOSJP5YwcF5eNwr2sy/2hn2o4H2UUgH+6IGw25GrCE26qacfv1f83+gZVR3oeGvojaU89i/6723UF00GyMwv3QUMZlLvzFDEwjizmSmz2TIVvkaVV8Pe7sqwhDvJaDHzryqeQ/0RrIvWhMeBcQKBgA5HDNXHdXQ8BWtWpi25l1b/U8BWE2l8ybUgAQ99CO4wyTpAFqI2NJSOmuFIdbvnLURM204FwkzXx5GP8QayRTaC8ChMrnOohWu96JoV73H06T6Az5N/ns+ixVmD+YAxkqipTkTL+03NJWPgQi/ZjpQhS7NFI4JGNtakdDlok1MXAoGAY+lHipaOzNE1/34FtUQzhXlQdfIAmxcRzknfHB/IDwzQxXBN9ICXfjCInKMULQbIghsonXKD+S2+YatpbIFgdrHUHrSF87c1KzJ948QBYt+nBWjLP4Lfy+dfNLZsJNr7xaSVkpkbn1YQRrc6zt+OsOlKA0hYhpst3uLByDs1eWECgYEAnwgjcV337Lf0bBLNeY3pzInCN82USKMfd7qR8KVcAWTatkC9ovW4FJ/cWRwVQYPJyxm2ZNVO65TaQElKcW9xlUi+esx9mp3go59R9CfnDitMnha/NG7Cb7m4LhBvRvNUtbDCJjj0Uc+wYRn7UtyUjYSxorgt8YGpAEPkwANIa3E=");

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //传参内容格式：ZFZ_当前日期_执法证号(可选)
            String plaintextRSA = "0100_1879402722806026242";
            //RSA加密
            String encryptedRSA = RSAEncryptionUtil.encryptWithRSA(plaintextRSA, publicKey);
            System.out.println("（传参）RSA 加密: " + encryptedRSA);
            //RSA解密
            String decryptedRSA = RSAEncryptionUtil.decryptWithRSA(encryptedRSA, privateKey);
            System.out.println("（传参）RSA 解密: " + decryptedRSA);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
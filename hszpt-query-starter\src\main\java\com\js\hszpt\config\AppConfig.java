package com.js.hszpt.config;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableAutoConfiguration(exclude = {
    QuartzAutoConfiguration.class,
    // 如果需要完全禁用数据库，可以取消下面的注释
    // DataSourceAutoConfiguration.class
})
public class AppConfig {
    // 空配置类
}
package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 电子政务-公共字典表
 */
@Data
@TableName("dict_eg_public")
public class DictEgPublic {

    /**
     * 字典标识
     */
    @TableId(value = "dict_id")
    private Long dictId;

    /**
     * 字典代码
     */
    @TableId("dict_code")
    private String dictCode;

    /**
     * 字典值
     */
    @TableField("dict_value")
    private String dictValue;

    /**
     * 字典类型
     */
    @TableId("dict_type")
    private String dictType;

    /**
     * 字典描述
     */
    @TableField("dict_desc")
    private String dictDesc;

    /**
     * 序号
     */
    @TableField("seq_no")
    private Long seqNo;

    /**
     * 删除标志代码 0：正常；1：删除
     */
    @TableField("delete_flag_code")
    @TableLogic(value = "0",delval = "1")
    private String deleteFlagCode;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 创建日期
     */
    @TableField(value = "create_date")
    private Date createDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 更新日期
     */
    @TableField(value = "update_date")
    private Date updateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录创建日期
     */
    @TableField("rec_create_date")
    private Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录修改日期
     */
    @TableField("rec_modify_date")
    private Date recModifyDate;

}

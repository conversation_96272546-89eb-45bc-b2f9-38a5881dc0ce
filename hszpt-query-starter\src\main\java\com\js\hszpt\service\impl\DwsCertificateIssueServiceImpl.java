package com.js.hszpt.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.dto.*;
import com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO;
import com.js.hszpt.entity.DwsCertificateIssue;
import com.js.hszpt.mapper.DwsCertificateIssueMapper;
import com.js.hszpt.service.DwsCertificateIssueService;
import com.js.hszpt.vo.CertificateIssueStatisticsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * 电子证照签发情况统计Service实现类
 */
@Service
@DS("dzzzdws")
@Slf4j
public class DwsCertificateIssueServiceImpl extends ServiceImpl<DwsCertificateIssueMapper, DwsCertificateIssue>
        implements DwsCertificateIssueService {

    /**
     * 统计证照签发情况
     * 
     * @param param 查询参数
     * @return 统计结果
     */
    @Override
    public List<CertificateIssueStatisticsDTO> statisticsCertificateIssue(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行统计
        List<CertificateIssueStatisticsDTO> certificateIssueStatisticsDTOS = baseMapper
                .statisticsCertificateIssue(param);
        CertificateIssueStatisticsDTO certificateIssueStatisticsDTO = new CertificateIssueStatisticsDTO();
        certificateIssueStatisticsDTO.setSummary(true);
        certificateIssueStatisticsDTO.setOrgCode(param.getLoginUserOrgCode());
        certificateIssueStatisticsDTO.setOrgName("签发总量");
        if (CollUtil.isEmpty(certificateIssueStatisticsDTOS)) {
            certificateIssueStatisticsDTO.setIssueCount(0L);
            certificateIssueStatisticsDTO.setIssueRatio(0.0);
            certificateIssueStatisticsDTO.setOnlineCount(0L);
            certificateIssueStatisticsDTO.setWindowCount(0L);
            certificateIssueStatisticsDTO.setChainRatio(0.0);
            certificateIssueStatisticsDTOS.add(certificateIssueStatisticsDTO);
            return certificateIssueStatisticsDTOS;
        }
        Map<String, Long> sumMap = certificateIssueStatisticsDTOS.stream()
                .collect(Collectors.toMap(c -> "key",
                        c -> {
                            Map<String, Long> map = new HashMap<>();
                            map.put("issueCount", c.getIssueCount());
                            map.put("windowCount", c.getWindowCount());
                            map.put("onlineCount", c.getOnlineCount());
                            return map;
                        }, (m1, m2) -> {
                            m1.forEach((key, value) -> m1.merge(key, m2.get(key), Long::sum));
                            return m1;
                        }))
                .get("key");

        certificateIssueStatisticsDTOS.forEach(c -> {
            double issueRatio = c.getIssueCount() * 100.0 / sumMap.get("issueCount");
            c.setIssueRatio(Math.round(issueRatio * 100) / 100.0);
            // 获取上一个周期的签发量
            Long previousIssueCount = baseMapper.getPreviousPeriodIssueCountByType(param,
                    param.getStatusCode());
            // 计算环比率
            if (previousIssueCount != null && previousIssueCount > 0) {
                c.setChainRatio(c.getIssueCount() * 100.0 / previousIssueCount - 100);
            } else {
                c.setChainRatio(0.0);
            }
        });
        certificateIssueStatisticsDTO.setIssueCount(sumMap.get("issueCount"));
        certificateIssueStatisticsDTO.setWindowCount(sumMap.get("windowCount"));
        certificateIssueStatisticsDTO.setOnlineCount(sumMap.get("onlineCount"));
        certificateIssueStatisticsDTO.setIssueRatio(100.0);
        certificateIssueStatisticsDTO.setChainRatio(0.0);
        certificateIssueStatisticsDTOS.add(0, certificateIssueStatisticsDTO);
        return certificateIssueStatisticsDTOS;
    }

    /**
     * 按时间维度统计证照签发情况
     * 
     * @param param 查询参数
     * @return 按时间维度统计结果
     */
    @Override
    public Map<String, List<CertificateIssueTimeTypeStatisticsDTO>> statisticsCertificateIssueByTimeType(
            CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按时间维度统计
        List<CertificateIssueTimeTypeStatisticsDTO> certificateIssueTimeTypeStatisticsDTOS;
        log.info("查询机构维度的证照签发情况:{}", param.getOrgType());
        if (StrUtil.equals(param.getOrgType(), "1")) {
            certificateIssueTimeTypeStatisticsDTOS = baseMapper
                    .statisticsCertificateIssueOrgByTimeType(param);
        } else if (StrUtil.equals(param.getOrgType(), "2")) {
            certificateIssueTimeTypeStatisticsDTOS = baseMapper
                    .statisticsCertificateIssueByTimeType(param);
        } else {
            return Collections.emptyMap();
        }
        Map<String, List<CertificateIssueTimeTypeStatisticsDTO>> map = certificateIssueTimeTypeStatisticsDTOS.stream()
                .collect(Collectors.groupingBy(CertificateIssueTimeTypeStatisticsDTO::getTimePoint));
        return map;
    }

    /**
     * 详细统计证照签发情况
     * 
     * @param param 查询参数
     * @return 详细统计结果
     */
    @Override
    public List<CertificateIssueDetailStatisticsDTO> statisticsCertificateIssueDetail(
            CertificateIssueStatisticsVO param) {
        log.info("查询机构维度的证照签发情况:{}", param.getOrgType());
        // 获取基础统计数据
        log.info("【详细统计证照签发情况】获取基础统计数据");
        List<CertificateIssueDetailStatisticsDTO> detailList;
        if (StrUtil.equals(param.getOrgType(), "1")) {
            detailList = baseMapper.statisticsCertificateIssueOrgDetail(param);
        } else if (StrUtil.equals(param.getOrgType(), "2")) {
            detailList = baseMapper.statisticsCertificateIssueDetail(param);
        } else {
            return Collections.emptyList();
        }

        Long totalIssueCount = detailList.stream().mapToLong(CertificateIssueDetailStatisticsDTO::getIssueCount).sum();
        Long counterIssueCount = detailList.stream()
                .mapToLong(CertificateIssueDetailStatisticsDTO::getCounterIssueCount).sum();
        Long onlineIssueCount = detailList.stream().mapToLong(CertificateIssueDetailStatisticsDTO::getOnlineIssueCount)
                .sum();
        List<String> orgCodeList = detailList.stream().map(CertificateIssueDetailStatisticsDTO::getOrgCode)
                .collect(Collectors.toList());

        // 获取总签发量
        // CertificateIssueDetailStatisticsDTO total =
        // baseMapper.getTotalIssueCount(param);
        // Long totalIssueCount = total.getIssueCount();

        // 创建新的结果列表，用于添加汇总数据
        List<CertificateIssueDetailStatisticsDTO> resultList = new ArrayList<>();

        // 创建汇总数据对象
        CertificateIssueDetailStatisticsDTO summaryData = new CertificateIssueDetailStatisticsDTO();
        summaryData.setOrgCode(param.getLoginUserOrgCode());
        summaryData.setOrgName("总计");
        summaryData.setIssueCount(totalIssueCount);
        summaryData.setCounterIssueCount(counterIssueCount);
        summaryData.setOnlineIssueCount(onlineIssueCount);
        summaryData.setIssueRatio(100.0); // 汇总数据占比为100%
        summaryData.setStartTime(param.getStartTime());
        summaryData.setEndTime(param.getEndTime());
        summaryData.setSummary(true);

        // 获取上一个周期的机构数据
        String startTime = param.getStartTime();
        String endTime = param.getEndTime();
        String timeType = param.getTimeType();
        if (StrUtil.equals(timeType, "year")) {
            startTime  = DateUtil.format(DateUtil.beginOfYear(DateUtil.offsetMonth(DateUtil.parse(startTime), -12)), "yyyy-MM-dd");
            endTime  = DateUtil.format(DateUtil.endOfYear(DateUtil.parse(startTime)), "yyyy-MM-dd");
        }
        if (StrUtil.equals(timeType, "month")) {
            startTime  = DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(DateUtil.parse(startTime), -1)), "yyyy-MM-dd");
            endTime  = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(startTime)), "yyyy-MM-dd");
        }
        if (StrUtil.equals(timeType, "day")) {
            endTime  = DateUtil.format(DateUtil.parse(startTime), "yyyy-MM-dd");
            startTime  = DateUtil.format(DateUtil.offsetDay(DateUtil.parse(startTime), -1), "yyyy-MM-dd");
        }
        if (StrUtil.equals(timeType, "quarter")) {
            startTime  = DateUtil.format(DateUtil.beginOfQuarter(DateUtil.offsetMonth(DateUtil.parse(startTime), -3)), "yyyy-MM-dd");
            endTime  = DateUtil.format(DateUtil.endOfQuarter(DateUtil.parse(startTime)), "yyyy-MM-dd");
        }
        param.setStartTime(startTime);
        param.setEndTime(endTime);
        List<CertificateIssueDetailStatisticsDTO> previousPeriodIssueCountList = baseMapper
                .getPreviousPeriodIssueCountByOrgCode(param, orgCodeList);
        // 计算占比和环比率，同时累加汇总数据
        if (detailList != null && !detailList.isEmpty()) {
            log.info("【详细统计证照签发情况】计算占比和环比率，同时累加汇总数据");
            Map<String, Long> map;
            if (StrUtil.equals(param.getOrgType(), "1")) {
                map = previousPeriodIssueCountList.stream()
                        .collect(Collectors.toMap(CertificateIssueDetailStatisticsDTO::getOrgCode,
                                CertificateIssueDetailStatisticsDTO::getIssueCount));
            }else {
                map = previousPeriodIssueCountList.stream()
                        .collect(Collectors.toMap(CertificateIssueDetailStatisticsDTO::getOrgName,
                                CertificateIssueDetailStatisticsDTO::getIssueCount));
            }
            for (CertificateIssueDetailStatisticsDTO detail : detailList) {
                // 计算占比，保留两位小数
                if (totalIssueCount != null && totalIssueCount > 0) {
                    double issueRatio = detail.getIssueCount() * 100.0 / totalIssueCount;
                    detail.setIssueRatio(Math.round(issueRatio * 100) / 100.0);
                } else {
                    detail.setIssueRatio(0.0);
                }

                // 获取上一个周期的签发量并计算环比率，保留两位小数
                Long previousIssueCount = StrUtil.equals(param.getOrgType(), "1") ?
                        map.getOrDefault(detail.getOrgCode(),  0L) :
                        map.getOrDefault(detail.getOrgName(),  0L);
                double detailChainRatio = calculateChainRatio(detail.getIssueCount(), previousIssueCount);
                detail.setChainRatio(detailChainRatio);
            }
        }
        // 计算上一个周期的总签发量
        Long previousTotalIssueCount = previousPeriodIssueCountList.stream()
                .mapToLong(CertificateIssueDetailStatisticsDTO::getIssueCount)
                .sum();
        log.info("【详细统计证照签发情况】获取上一个周期的总签发量:{}", previousTotalIssueCount);
        // 计算环比率，保留两位小数
        double chainRatio = calculateChainRatio(totalIssueCount, previousTotalIssueCount);
        summaryData.setChainRatio(chainRatio);

        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);

        return resultList;
    }

    /**
     * 按证照类型统计签发情况
     * 
     * @param param 查询参数
     * @return 按证照类型统计结果
     */
    @Override
    public List<CertificateTypeStatisticsDTO> statisticsByCertificateType(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按证照类型统计
        List<CertificateTypeStatisticsDTO> certificateTypeStatisticsDTOList = baseMapper
                .statisticsByCertificateType(param);

        // 构建树形结构，用于旭日图展示
        List<CertificateTypeStatisticsDTO> treeList = buildCertificateTypeTree(certificateTypeStatisticsDTOList);

        return treeList;
    }

    /**
     * 构建证照类型的树形结构
     * 
     * @param flatList 平铺的证照类型统计列表
     * @return 树形结构的证照类型统计列表
     */
    private List<CertificateTypeStatisticsDTO> buildCertificateTypeTree(List<CertificateTypeStatisticsDTO> flatList) {
        if (CollUtil.isEmpty(flatList)) {
            return Collections.emptyList();
        }

        // 创建结果列表
        List<CertificateTypeStatisticsDTO> resultList = new ArrayList<>();

        // 创建总计节点
        CertificateTypeStatisticsDTO rootNode = new CertificateTypeStatisticsDTO();
        rootNode.setCertificateTypeCode("total");
        rootNode.setCertificateTypeName("总计");
        rootNode.setParentTypeCode("-1");
        rootNode.setIsSummary(true);
        rootNode.setLevel(0);
        rootNode.setChildren(new ArrayList<>());

        // 按parentTypeCode进行分组
        Map<String, List<CertificateTypeStatisticsDTO>> parentChildMap = new HashMap<>();

        // 计算各级别节点的签发总量
        long totalIssueCount = 0;

        // 根据parentTypeCode将节点分组
        for (CertificateTypeStatisticsDTO node : flatList) {
            // 设置初始值
            node.setChildren(new ArrayList<>());
            node.setIsSummary(false);
            // 确定节点的父节点编码
            String parentCode = node.getParentTypeCode();
            if (StrUtil.equals(parentCode, node.getCertificateTypeCode())) {
                // 一级类型节点
                parentCode = "total";
                node.setParentTypeCode(parentCode);
                node.setLevel(1);
            } else {
                // 设置节点级别，子级别比父级别高1
                // 这里简化处理，假设所有带有parentTypeCode的都是二级节点
                node.setLevel(2);
            }
            // 将节点添加到对应的父节点组
            List<CertificateTypeStatisticsDTO> children = parentChildMap.getOrDefault(parentCode, new ArrayList<>());
            children.add(node);
            parentChildMap.put(parentCode, children);

            // 累加二级节点的签发量到总量
            if (node.getLevel() == 2) {
                totalIssueCount += node.getIssueCount();
            }
        }

        // 计算一级节点的签发量(各自子节点的总和)
        List<CertificateTypeStatisticsDTO> firstLevelNodes = parentChildMap.getOrDefault("total", new ArrayList<>());
        for (CertificateTypeStatisticsDTO firstLevelNode : firstLevelNodes) {
            long firstLevelIssueCount = 0;
            List<CertificateTypeStatisticsDTO> subNodes = parentChildMap
                    .getOrDefault(firstLevelNode.getCertificateTypeCode(), new ArrayList<>());
            for (CertificateTypeStatisticsDTO subNode : subNodes) {
                firstLevelIssueCount += subNode.getIssueCount();
            }
            firstLevelNode.setIssueCount(firstLevelIssueCount);
        }

        // 设置根节点的签发总量
        rootNode.setIssueCount(totalIssueCount);
        rootNode.setIssueRatio(100.0);

        // 构建树结构
        // 1. 将一级类型节点添加为根节点的子节点
        rootNode.setChildren(firstLevelNodes);

        // 2. 为每个一级类型节点设置其子节点
        for (CertificateTypeStatisticsDTO firstLevelNode : firstLevelNodes) {
            // 计算占比
            if (totalIssueCount > 0) {
                double issueRatio = firstLevelNode.getIssueCount() * 100.0 / totalIssueCount;
                firstLevelNode.setIssueRatio(Math.round(issueRatio * 100) / 100.0);
            } else {
                firstLevelNode.setIssueRatio(0.0);
            }

            // 设置子节点
            List<CertificateTypeStatisticsDTO> subNodes = parentChildMap
                    .getOrDefault(firstLevelNode.getCertificateTypeCode(), new ArrayList<>());
            firstLevelNode.setChildren(subNodes);

            // 为二级节点计算占比（相对于其父节点）
            long firstLevelIssueCount = firstLevelNode.getIssueCount();
            for (CertificateTypeStatisticsDTO subNode : subNodes) {
                if (firstLevelIssueCount > 0) {
                    double issueRatio = subNode.getIssueCount() * 100.0 / firstLevelIssueCount;
                    subNode.setIssueRatio(Math.round(issueRatio * 100) / 100.0);
                } else {
                    subNode.setIssueRatio(0.0);
                }
            }
        }

        // 将根节点添加到结果列表
        resultList.add(rootNode);

        return resultList;
    }

    /**
     * 按证照类型和状态统计签发情况
     * 
     * @param param 查询参数
     * @return 按证照类型和状态统计结果
     */
    @Override
    public List<CertificateTypeStatusStatisticsDTO> statisticsByCertificateTypeAndStatus(
            CertificateIssueStatisticsVO param) {
        // 1. 一次性获取所有证照类型的统计数据
        List<CertificateTypeStatusStatisticsDTO> detailList = baseMapper.statisticsByCertificateTypeAndStatus(param);

        if (CollUtil.isEmpty(detailList)) {
            return Collections.emptyList();
        }

        // 2. 一次性获取所有证照类型的上一周期数据
        List<CertificateTypeStatusStatisticsDTO> previousPeriodData = baseMapper
                .getPreviousPeriodIssueCountBatch(param);

        Map<String, List<CertificateTypeStatusStatisticsDTO>> previousGroup = previousPeriodData.stream()
                .collect(Collectors.groupingBy(CertificateTypeStatusStatisticsDTO::getFirstTypeCode));

        // 3. 计算总计数据
        CertificateTypeStatusStatisticsDTO summaryData = new CertificateTypeStatusStatisticsDTO();
        summaryData.setCertificateTypeCode("total");
        summaryData.setCertificateTypeName("总计");
        summaryData.setIsSummary(true);
        summaryData.setIssueRatio(100.0);

        // 4. 使用Stream API计算总量
        Long totalIssueCount = detailList.stream()
                .mapToLong(CertificateTypeStatusStatisticsDTO::getIssueCount)
                .sum();
        Long totalValidCount = detailList.stream()
                .mapToLong(CertificateTypeStatusStatisticsDTO::getValidCount)
                .sum();
        Long totalInvalidCount = detailList.stream()
                .mapToLong(CertificateTypeStatusStatisticsDTO::getInvalidCount)
                .sum();

        summaryData.setIssueCount(totalIssueCount);
        summaryData.setValidCount(totalValidCount);
        summaryData.setInvalidCount(totalInvalidCount);

        // 5. 计算总的环比率
        Long previousTotalIssueCount = CollUtil.isEmpty(previousPeriodData) ? 0L
                : previousPeriodData.stream()
                        .mapToLong(CertificateTypeStatusStatisticsDTO::getIssueCount)
                        .sum();

        summaryData.setChainRatio(calculateChainRatio(totalIssueCount, previousTotalIssueCount));

        // 6. 处理父级分类汇总数据
        List<CertificateTypeStatusStatisticsDTO> resultList = new ArrayList<>();
        Map<String, List<CertificateTypeStatusStatisticsDTO>> groupedByFirstType = detailList.stream()
                .collect(Collectors.groupingBy(CertificateTypeStatusStatisticsDTO::getFirstTypeCode));

        groupedByFirstType.forEach((code, list) -> {
            CertificateTypeStatusStatisticsDTO firstTypeData = new CertificateTypeStatusStatisticsDTO();
            firstTypeData.setCertificateTypeCode(code);
            firstTypeData.setCertificateTypeName(list.get(0).getFirstTypeName());
            firstTypeData.setIsSummary(false);

            // 计算父级分类的汇总数据
            firstTypeData
                    .setIssueCount(list.stream().mapToLong(CertificateTypeStatusStatisticsDTO::getIssueCount).sum());
            firstTypeData
                    .setValidCount(list.stream().mapToLong(CertificateTypeStatusStatisticsDTO::getValidCount).sum());
            firstTypeData.setInvalidCount(
                    list.stream().mapToLong(CertificateTypeStatusStatisticsDTO::getInvalidCount).sum());
            firstTypeData.setIssueRatio(calculateRatio(firstTypeData.getIssueCount(), totalIssueCount));

            // 计算父级分类的环比率
            List<CertificateTypeStatusStatisticsDTO> preChainRatioList = previousGroup.get(code);
            LongStream preCount = preChainRatioList.stream()
                    .mapToLong(CertificateTypeStatusStatisticsDTO::getIssueCount);
            firstTypeData.setChainRatio(calculateChainRatio(firstTypeData.getIssueCount(), preCount.sum()));

            resultList.add(firstTypeData);
        });

        // 8. 按签发量倒序排序并添加总计数据
        List<CertificateTypeStatusStatisticsDTO> sortedList = resultList.stream()
                .sorted(Comparator.comparing(CertificateTypeStatusStatisticsDTO::getIssueCount).reversed())
                .collect(Collectors.toList());

        sortedList.add(0, summaryData);
        return sortedList;
    }

    /**
     * 计算比率
     */
    private double calculateRatio(Long part, Long total) {
        if (total == null || total == 0) {
            return 0.0;
        }
        return Math.round(part * 100.0 / total * 100) / 100.0;
    }

    /**
     * 计算环比率
     */
    private double calculateChainRatio(Long current, Long previous) {
        if (previous == null || previous == 0) {
            return 0.0;
        }
        // 1. 计算原始环比增长率
        double currentValue = current.doubleValue();
        double previousValue = previous.doubleValue();
        double rawRatio = (currentValue - previousValue) / previousValue * 100;

        // 2. 高精度四舍五入保留两位小数
        BigDecimal ratio = BigDecimal.valueOf(rawRatio)
                .setScale(2, RoundingMode.HALF_UP);

        return ratio.doubleValue();
    }

    /**
     * 按申请来源统计签发情况
     * 
     * @param param 查询参数
     * @return 按申请来源统计结果
     */
    @Override
    public List<ApplicationSourceStatisticsDTO> statisticsByApplicationSource(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按申请来源统计
        List<ApplicationSourceStatisticsDTO> detailList = baseMapper.statisticsByApplicationSource(param);
        ApplicationSourceStatisticsDTO applicationSourceStatisticsDTO = new ApplicationSourceStatisticsDTO();
        applicationSourceStatisticsDTO.setApplicationSourceCode("total");
        applicationSourceStatisticsDTO.setApplicationSourceName("总计");
        applicationSourceStatisticsDTO.setIssueRatio(100.0);
        applicationSourceStatisticsDTO.setIsSummary(true);
        // 创建新的结果列表，用于添加汇总数据
        List<ApplicationSourceStatisticsDTO> resultList = new ArrayList<>();

        // 计算总签发量，用于计算占比
        Long totalIssueCount = 0L;

        // 累加各申请来源的数据到总量
        if (detailList != null && !detailList.isEmpty()) {
            for (ApplicationSourceStatisticsDTO detail : detailList) {
                totalIssueCount += detail.getIssueCount();
            }
        }
        applicationSourceStatisticsDTO.setIssueCount(totalIssueCount);
        // 计算各申请来源的占比
        if (detailList != null && !detailList.isEmpty()) {
            for (ApplicationSourceStatisticsDTO detail : detailList) {
                // 计算占比
                if (totalIssueCount != null && totalIssueCount > 0) {
                    double issueRatio = detail.getIssueCount() * 100.0 / totalIssueCount;
                    detail.setIssueRatio(Math.round(issueRatio * 100) / 100.0);
                } else {
                    detail.setIssueRatio(0.0);
                }
            }
        }
        resultList.add(applicationSourceStatisticsDTO);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);

        return resultList;
    }

    /**
     * 按时间统计签发情况
     * 
     * @param param 查询参数
     * @return 按时间统计结果
     */
    @Override
    public List<TimeStatisticsDTO> statisticsByTime(CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按时间统计
        List<TimeStatisticsDTO> detailList = baseMapper.statisticsByTime(param);

        // 创建新的结果列表，用于添加汇总数据
        List<TimeStatisticsDTO> resultList = new ArrayList<>();

        // 创建汇总数据对象
        TimeStatisticsDTO summaryData = new TimeStatisticsDTO();
        summaryData.setTimePoint("总计");
        summaryData.setIssueCount(0L);
        summaryData.setIssueRatio(100.0); // 汇总数据占比为100%
        summaryData.setIsSummary(true);

        // 计算总签发量，用于计算占比
        Long totalIssueCount = 0L;

        // 累加各时间点的数据到汇总数据
        if (detailList != null && !detailList.isEmpty()) {
            for (TimeStatisticsDTO detail : detailList) {
                totalIssueCount += detail.getIssueCount();
                summaryData.setIssueCount(summaryData.getIssueCount() + detail.getIssueCount());
            }
        }

        // 计算各时间点的占比
        if (detailList != null && !detailList.isEmpty()) {
            for (TimeStatisticsDTO detail : detailList) {
                // 计算占比
                if (totalIssueCount != null && totalIssueCount > 0) {
                    double issueRatio = detail.getIssueCount() * 100.0 / totalIssueCount;
                    detail.setIssueRatio(Math.round(issueRatio * 100) / 100.0);
                } else {
                    detail.setIssueRatio(0.0);
                }
            }
        }

        // 将汇总数据添加到结果列表的最前面
        resultList.add(summaryData);
        // 将原始数据添加到结果列表
        resultList.addAll(detailList);

        return resultList;
    }

    /**
     * 按证照类型统计签发情况（树形结构，用于旭日图展示）
     * 
     * @param param 查询参数
     * @return 树形结构的证照类型统计结果
     */
    @Override
    public List<CertificateTypeStatisticsDTO> statisticsByCertificateTypeForSunburst(
            CertificateIssueStatisticsVO param) {
        // 调用Mapper进行按证照类型统计
        List<CertificateTypeStatisticsDTO> certificateTypeStatisticsDTOList = baseMapper
                .statisticsByCertificateType(param);

        // 构建旭日图需要的树形结构
        if (CollUtil.isEmpty(certificateTypeStatisticsDTOList)) {
            return Collections.emptyList();
        }

        // 按照类型层级分组
        Map<String, List<CertificateTypeStatisticsDTO>> typeCodeMap = new HashMap<>();

        // 一级类型列表
        List<CertificateTypeStatisticsDTO> rootLevelNodes = new ArrayList<>();

        // 计算总签发量
        long totalIssueCount = 0;

        // 第一轮遍历，分类节点并计算总量
        for (CertificateTypeStatisticsDTO node : certificateTypeStatisticsDTOList) {
            // 设置初始值
            node.setChildren(new ArrayList<>());
            node.setIsSummary(false);

            String typeCode = node.getCertificateTypeCode();

            // 建立类型代码映射
            List<CertificateTypeStatisticsDTO> sameTypeNodes = typeCodeMap.getOrDefault(typeCode, new ArrayList<>());
            sameTypeNodes.add(node);
            typeCodeMap.put(typeCode, sameTypeNodes);

            // 如果是一级节点（无父节点或父节点为空），加入根节点列表
            if (node.getParentTypeCode() == null || node.getParentTypeCode().isEmpty()) {
                node.setLevel(1);
                rootLevelNodes.add(node);
                totalIssueCount += node.getIssueCount();
            } else {
                // 否则为二级或更低级别节点
                node.setLevel(2); // 简化处理，暂时都设为2级
            }
        }

        // 创建根节点（总计）
        CertificateTypeStatisticsDTO rootNode = new CertificateTypeStatisticsDTO();
        rootNode.setCertificateTypeCode("total");
        rootNode.setCertificateTypeName("总计");
        rootNode.setParentTypeCode("-1");
        rootNode.setLevel(0);
        rootNode.setIsSummary(true);
        rootNode.setIssueCount(totalIssueCount);
        rootNode.setIssueRatio(100.0);
        rootNode.setChildren(new ArrayList<>());

        // 第二轮遍历，构建父子关系
        for (CertificateTypeStatisticsDTO node : certificateTypeStatisticsDTOList) {
            // 计算各节点占比
            if (node.getLevel() == 1) {
                // 一级节点占总量的比例
                if (totalIssueCount > 0) {
                    double ratio = node.getIssueCount() * 100.0 / totalIssueCount;
                    node.setIssueRatio(Math.round(ratio * 100) / 100.0);
                } else {
                    node.setIssueRatio(0.0);
                }

                // 将一级节点添加到根节点的子节点中
                rootNode.getChildren().add(node);
            } else {
                // 为子节点找到父节点
                String parentCode = node.getParentTypeCode();
                if (parentCode != null && !parentCode.isEmpty() && typeCodeMap.containsKey(parentCode)) {
                    // 获取父节点
                    CertificateTypeStatisticsDTO parentNode = typeCodeMap.get(parentCode).get(0);

                    // 计算子节点占父节点的比例
                    if (parentNode.getIssueCount() > 0) {
                        double ratio = node.getIssueCount() * 100.0 / parentNode.getIssueCount();
                        node.setIssueRatio(Math.round(ratio * 100) / 100.0);
                    } else {
                        node.setIssueRatio(0.0);
                    }

                    // 将子节点添加到父节点的子节点列表中
                    parentNode.getChildren().add(node);
                }
            }
        }

        // 构建最终的树形结构
        List<CertificateTypeStatisticsDTO> result = new ArrayList<>();
        result.add(rootNode);

        return result;
    }
}
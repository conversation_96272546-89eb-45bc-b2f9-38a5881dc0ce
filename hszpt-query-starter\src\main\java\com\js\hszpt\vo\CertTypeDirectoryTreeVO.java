package com.js.hszpt.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "证照类型目录树形结构")
public class CertTypeDirectoryTreeVO {
    
    @ApiModelProperty(value = "证照类型目录ID")
    private String certTypeDirId;
    
    @ApiModelProperty(value = "证照类型名称")
    private String certificateTypeName;
    
    @ApiModelProperty(value = "父节点ID")
    private String parentId;
    
    @ApiModelProperty(value = "子节点")
    private List<CertTypeDirectoryTreeVO> children;
} 
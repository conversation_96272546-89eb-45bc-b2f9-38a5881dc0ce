package com.js.hszpt.dto;

import lombok.Data;

/**
 * 证照签发统计结果DTO
 */
@Data
public class CertificateIssueStatisticsDTO {

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构类型
     */
    private String orgType;
    
    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 签发数量占比（百分比）
     */
    private Double issueRatio;
    
    /**
     * 签发数量
     */
    private Long issueCount;

    /**
     * 窗口办理签发数量
     */
    private Long windowCount;

    /**
     * 在线办理签发数量
     */
    private Long onlineCount;

    /**
     * 签发数量环比率（百分比）
     */
    private Double chainRatio;

    /**
     * 是否为汇总数据
     */
    private boolean isSummary;
} 
package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.CertificateType;
import com.js.hszpt.mapper.CertificateTypeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CertificateTypeService extends ServiceImpl<CertificateTypeMapper, CertificateType> {


    public CertificateType getCertificateTypeByName(String name){
        QueryWrapper<CertificateType> queryWrapper = Wrappers.<CertificateType>query();
        queryWrapper.lambda()
                .eq(CertificateType::getCertificateName,name)
                .last("LIMIT 1");
        return baseMapper.selectOne(queryWrapper);
    }
}

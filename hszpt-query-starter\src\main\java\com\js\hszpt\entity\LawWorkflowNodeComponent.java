package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  LawWorkflowNodeComponent   
 * @Description:TODO(工作流前端节点组件展示配置表)   
 * @author:   System Generation 
 */
@Data

@TableName("LAW_WORKFLOW_NODE_COMPONENT")
@ApiModel(value = "工作流前端节点组件展示配置表")
public class LawWorkflowNodeComponent extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "LAW_WORKFLOW_NODE_COMPONENT_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
    * 模板编码
    */
    @ApiModelProperty(value = "模板编码")
    private String itemsCode;


    /**
    * 节点代码
    */
    @ApiModelProperty(value = "节点代码")
    private String nodeCode;


    /**
    * 节点类型 1审核 2审批 3复核 4备案
    */
    @ApiModelProperty(value = "节点类型 1审核 2审批 3复核 4备案")
    private Integer nodeType;


    /**
    * 节点顺序
    */
    @ApiModelProperty(value = "节点顺序")
    private Integer nodeSort;


    /**
    * 申请人机构层级 1-部局 2-直属局 3-分支局或海事处
    */
    @ApiModelProperty(value = "申请人机构层级 1-部局 2-直属局 3-分支局或海事处")
    private Integer applyUserLevel;


    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    private String createOperId;


    /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    private String modifyOperId;


    /**
    * 修改时间
    */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;


}
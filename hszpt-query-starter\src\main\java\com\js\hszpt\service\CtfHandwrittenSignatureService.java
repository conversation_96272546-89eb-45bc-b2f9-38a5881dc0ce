package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.entity.CtfHandwrittenSignature;
import com.js.hszpt.mapper.CtfHandwrittenSignatureDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
 
/**
 * 
 * @ClassName:  CtfHandwrittenSignatureService    
 * @Description:TODO(证照手写签名表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CtfHandwrittenSignatureService extends ServiceImpl<CtfHandwrittenSignatureDao,CtfHandwrittenSignature> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CtfHandwrittenSignature> findByCondition(CtfHandwrittenSignature param, SearchVo searchVo, PageVo pageVo) {
		Page<CtfHandwrittenSignature> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CtfHandwrittenSignature> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<CtfHandwrittenSignature>      
	 * @throws
	 */
	public List<CtfHandwrittenSignature> findByCondition(CtfHandwrittenSignature param, SearchVo searchVo){
		QueryWrapper<CtfHandwrittenSignature> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CtfHandwrittenSignature>      
	 * @throws
	 */
	private QueryWrapper<CtfHandwrittenSignature> getCondition(CtfHandwrittenSignature param, SearchVo searchVo){
		QueryWrapper<CtfHandwrittenSignature> queryWrapper = new QueryWrapper<CtfHandwrittenSignature>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}
}
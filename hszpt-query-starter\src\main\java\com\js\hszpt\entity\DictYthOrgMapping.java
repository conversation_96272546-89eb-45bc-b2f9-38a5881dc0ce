package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@TableName("dict_yth_org_mapping")
@Data
public class DictYthOrgMapping {

    /**
     * 主键标识
     */
    @TableId(value = "id")
    private String id;

    /**
     * 原海事机构代码
     */
    private String srcOrgCode;

    /**
     * 原海事机构名称
     */
    private String srcOrgName;

    /**
     * 海事机构代码
     */
    private String orgCode;

    /**
     * 海事机构名称
     */
    private String orgName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 源系统类型代码
     */
    private Character sourceTypeCode;

    /**
     * 源系统代码
     */
    private String sourceCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录创建期
     */
    private Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    /**
     * 记录修改日期
     */
    private Date recModifyDate;
}

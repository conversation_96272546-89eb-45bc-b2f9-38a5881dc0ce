package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.dto.*;
import com.js.hszpt.dto.TimeTypeSubOrgUsageStatisticsDTO;
import com.js.hszpt.entity.DwsCertificateUsage;
import com.js.hszpt.vo.CertificateUsageStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 电子证照使用情况统计Mapper接口
 */
@Mapper
@DS("dzzzdws")
public interface DwsCertificateUsageMapper extends BaseMapper<DwsCertificateUsage> {

    /**
     * 按下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按下级机构统计结果
     */
    List<SubOrgUsageStatisticsDTO> statisticsBySubOrg(@Param("param") CertificateUsageStatisticsVO param);

    /**
     * 根据机构编码获取机构名称
     * 
     * @param orgCode 机构编码
     * @return 机构名称
     */
    String getOrgNameByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 获取机构的直接下级机构列表
     * 
     * @param parentOrgCode 父级机构编码
     * @return 下级机构列表
     */
    List<String> getDirectSubOrgCodes(@Param("parentOrgCode") String parentOrgCode);

    /**
     * 按不同时间维度和下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按不同时间维度和下级机构统计结果
     */
    List<TimeTypeSubOrgUsageStatisticsDTO> statisticsByTimeTypeAndSubOrg(
            @Param("param") CertificateUsageStatisticsVO param);

    /**
     * 按年份和下级机构统计使用情况
     * 
     * @param param 查询参数
     * @return 按年份和下级机构统计结果
     */
    List<TimeTypeSubOrgUsageStatisticsDTO> statisticsByYearAndSubOrg(
            @Param("param") CertificateUsageStatisticsVO param);

    /**
     * 按下级机构统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按下级机构统计结果带占比
     */
    List<SubOrgUsageRatioStatisticsDTO> statisticsBySubOrgWithRatio(@Param("param") CertificateUsageStatisticsVO param);

    /**
     * 获取登录人所在机构的总使用量
     * 
     * @param param 查询参数
     * @return 总使用量
     */
    Long getTotalUsageCount(@Param("param") CertificateUsageStatisticsVO param);

    /**
     * 按持证主体类别统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按持证主体类别统计结果带占比
     */
    List<HolderCategoryUsageStatisticsDTO> statisticsByHolderCategoryWithRatio(
            @Param("param") CertificateUsageStatisticsVO param);

    /**
     * 获取持证主体类别名称
     * 
     * @param holderCategoryCode 持证主体类别代码
     * @return 持证主体类别名称
     */
    String getHolderCategoryName(@Param("holderCategoryCode") String holderCategoryCode);

    /**
     * 按时间统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按时间统计结果带占比
     */
    List<TimeUsageStatisticsDTO> statisticsByTimeWithRatio(@Param("param") CertificateUsageStatisticsVO param);

    /**
     * 按证照类型统计使用情况并计算占比
     * 
     * @param param 查询参数
     * @return 按证照类型统计结果
     */
    List<CertificateTypeUsageStatisticsDTO> statisticsByType(
            @Param("param") CertificateUsageStatisticsVO param);
}
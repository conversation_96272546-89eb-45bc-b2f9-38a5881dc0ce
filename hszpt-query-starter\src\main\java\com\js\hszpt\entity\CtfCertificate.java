package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CtfCertificate   
 * @Description:TODO(证照基本信息表)   
 * @author:   System Generation 
 */
@Data

@TableName("ctf_certificate")
@ApiModel(value = "证照基本信息表")
public class CtfCertificate extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certPrintNo;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateTypeCode;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String issuOrgNameCn;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String issuOrgCode2;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String issuOrgCode3;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String holderName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String holderIdentityNumber;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String statusFlag;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String createStatus;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String applyId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String shipId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String shipName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String createOperId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String modifyOperId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String delFlag;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateNum;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String affairName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String applicantName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String applyType;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String acceptOrgCode2;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String apprOrgCode2;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date apprDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String shipImo;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String shipCallSign;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String shipMmsi;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String shipNameEn;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String applyNo;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String proType;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String acceptOrgCode3;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String apprOrgCode3;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateType;


}
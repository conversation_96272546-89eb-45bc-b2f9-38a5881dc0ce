package com.js.hszpt.api;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.dto.*;
import com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO;
import com.js.hszpt.service.DwsCertificateIssueService;
import com.js.hszpt.vo.CertificateIssueStatisticsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 证照签发统计API接口
 */
@Slf4j
@RestController
@RequestMapping("/certificate/statistics")
@Api(tags = "证照签发统计接口")
@DS("dzzzdws")
public class CertificateIssueStatisticsApi {

    @Autowired
    private DwsCertificateIssueService dwsCertificateIssueService;

    /**
     * 统计证照签发情况
     * @param param 查询参数
     * @return 统计结果
     */
    @PostMapping("/issue")
    @ApiOperation(value = "统计证照签发情况", notes = "根据条件统计证照签发情况")
    public Result<List<CertificateIssueStatisticsDTO>> statisticsCertificateIssue(@RequestBody CertificateIssueStatisticsVO param) {
        log.info("开始统计证照签发情况，参数：{}", param);
        
        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态不能为空");
        }
        
        try {
            // 调用Service进行统计
            List<CertificateIssueStatisticsDTO> result = dwsCertificateIssueService.statisticsCertificateIssue(param);
            log.info("证照签发情况统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照签发情况统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }

    /**
     * 按不同时间维度统计证照签发情况
     * @param param 查询参数
     * @return 按不同时间维度统计结果
     */
    @PostMapping("/issueByTimeType")
    @ApiOperation(value = "按不同时间维度统计证照签发情况", notes = "根据条件按不同时间维度统计证照签发情况")
    public Result<Map<String, List<CertificateIssueTimeTypeStatisticsDTO>>> statisticsCertificateIssueByTimeType(@RequestBody CertificateIssueStatisticsVO param) {
        log.info("开始按年统计证照签发情况，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态不能为空");
        }

        if (!StrUtil.equalsAny(param.getTimeType(),"year","quarter","month","day")) {
            return ResultUtil.error("不支持该时间维度");
        }

        if (!StrUtil.equalsAny(param.getOrgType(),"1","2")) {
            return ResultUtil.error("不支持该机构类型");
        }


        try {
            // 调用Service进行按时间维度统计
            Map<String, List<CertificateIssueTimeTypeStatisticsDTO>> result = dwsCertificateIssueService.statisticsCertificateIssueByTimeType(param);
            log.info("证照签发情况按时间维度统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照签发情况按时间维度统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
    
    /**
     * 按年统计证照签发情况
     * @param param 查询参数
     * @return 按年统计结果
     */
    @PostMapping("/issueByYear")
    @ApiOperation(value = "按年统计证照签发情况", notes = "根据条件按年统计证照签发情况")
    public Result<Map<String, List<CertificateIssueTimeTypeStatisticsDTO>>> statisticsCertificateIssueByYear(@RequestBody CertificateIssueStatisticsVO param) {
        log.info("开始按年统计证照签发情况，参数：{}", param);

        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态不能为空");
        }

        // 设置统计类型为年
        param.setTimeType("year");

        try {
            // 调用Service进行按年统计
            Map<String, List<CertificateIssueTimeTypeStatisticsDTO>> result = dwsCertificateIssueService.statisticsCertificateIssueByTimeType(param);
            log.info("证照签发情况按年统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照签发情况按年统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
    
    /**
     * 详细统计证照签发情况
     * @param param 查询参数
     * @return 详细统计结果
     */
    @PostMapping("/issueDetail")
    @ApiOperation(value = "详细统计证照签发情况", notes = "根据条件详细统计证照签发情况，包括占比、窗口/在线办理数量和环比率")
    public Result<List<CertificateIssueDetailStatisticsDTO>> statisticsCertificateIssueDetail(@RequestBody CertificateIssueStatisticsVO param) {
        log.info("开始详细统计证照签发情况，参数：{}", param);
        
        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态不能为空");
        }

        if (!StrUtil.equalsAny(param.getOrgType(),"1","2")) {
            return ResultUtil.error("不支持该机构类型");
        }
        
        try {
            // 调用Service进行详细统计
            List<CertificateIssueDetailStatisticsDTO> result = dwsCertificateIssueService.statisticsCertificateIssueDetail(param);
            log.info("证照签发情况详细统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照签发情况详细统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
    
    /**
     * 按证照类型统计签发情况
     * @param param 查询参数
     * @return 按证照类型统计结果
     */
    @PostMapping("/issueByType")
    @ApiOperation(value = "按证照类型统计签发情况", notes = "根据条件按证照类型统计签发情况，包括第一级证照类型信息")
    public Result<List<CertificateTypeStatisticsDTO>> statisticsByCertificateType(@RequestBody CertificateIssueStatisticsVO param) {
        log.info("开始按证照类型统计签发情况，参数：{}", param);
        
        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态不能为空");
        }
        
        try {
            // 调用Service进行按证照类型统计
            List<CertificateTypeStatisticsDTO> result = dwsCertificateIssueService.statisticsByCertificateType(param);
            log.info("证照签发情况按证照类型统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照签发情况按证照类型统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
    
    /**
     * 按证照类型和状态统计签发情况
     * @param param 查询参数
     * @return 按证照类型和状态统计结果
     */
    @PostMapping("/issueByTypeAndStatus")
    @ApiOperation(value = "按证照类型和状态统计签发情况", notes = "根据条件按证照类型和状态统计签发情况，包括有效/无效数量和环比率")
    public Result<List<CertificateTypeStatusStatisticsDTO>> statisticsByCertificateTypeAndStatus(@RequestBody CertificateIssueStatisticsVO param) {
        log.info("开始按证照类型和状态统计签发情况，参数：{}", param);
        
        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态不能为空");
        }
        
        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }
        
        try {
            // 调用Service进行按证照类型和状态统计
            List<CertificateTypeStatusStatisticsDTO> result = dwsCertificateIssueService.statisticsByCertificateTypeAndStatus(param);
            log.info("证照签发情况按证照类型和状态统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照签发情况按证照类型和状态统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
    
    /**
     * 按申请来源统计签发情况
     * @param param 查询参数
     * @return 按申请来源统计结果
     */
    @PostMapping("/issueByApplicationSource")
    @ApiOperation(value = "按申请来源统计签发情况", notes = "根据条件按申请来源统计签发情况，包括占比")
    public Result<List<ApplicationSourceStatisticsDTO>> statisticsByApplicationSource(@RequestBody CertificateIssueStatisticsVO param) {
        log.info("开始按申请来源统计签发情况，参数：{}", param);
        
        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态不能为空");
        }
        
        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }
        
        try {
            // 调用Service进行按申请来源统计
            List<ApplicationSourceStatisticsDTO> result = dwsCertificateIssueService.statisticsByApplicationSource(param);
            log.info("证照签发情况按申请来源统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照签发情况按申请来源统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
    
    /**
     * 按时间统计签发情况
     * @param param 查询参数
     * @return 按时间统计结果
     */
    @PostMapping("/issueByTime")
    @ApiOperation(value = "按时间统计签发情况", notes = "根据条件按时间统计签发情况，支持按年、季、月、日统计")
    public Result<List<TimeStatisticsDTO>> statisticsByTime(@RequestBody CertificateIssueStatisticsVO param) {
        log.info("开始按时间统计签发情况，参数：{}", param);
        
        // 参数校验

        if (param.getIssuerCodes() == null || param.getIssuerCodes().isEmpty()) {
            return ResultUtil.error("签发机构不能为空");
        }

        if (param.getStatusCode() == null || param.getStatusCode().isEmpty()) {
            return ResultUtil.error("证照状态不能为空");
        }
        
        // 如果未指定统计类型，默认按月统计
        if (param.getTimeType() == null || param.getTimeType().isEmpty()) {
            param.setTimeType("month");
        }
        
        // 验证统计类型是否有效
        if (!param.getTimeType().equals("year") && !param.getTimeType().equals("quarter") && 
            !param.getTimeType().equals("month") && !param.getTimeType().equals("day")) {
            return ResultUtil.error("统计类型无效，支持的类型：year, quarter, month, day");
        }
        
        try {
            // 调用Service进行按时间统计
            List<TimeStatisticsDTO> result = dwsCertificateIssueService.statisticsByTime(param);
            log.info("证照签发情况按时间统计完成，共计{}条记录", result.size());
            return ResultUtil.data(result);
        } catch (Exception e) {
            log.error("证照签发情况按时间统计异常", e);
            return ResultUtil.error("统计异常：" + e.getMessage());
        }
    }
} 
package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CtfCertificateAttribute   
 * @Description:TODO(证照照面属性信息表)   
 * @author:   System Generation 
 */
@Data

@TableName("ctf_certificate_attribute")
@ApiModel(value = "证照照面属性信息表")
public class CtfCertificateAttribute extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateAttributeId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String certificateId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String attributeName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String attributeColumnName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String attributeValue;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;



}
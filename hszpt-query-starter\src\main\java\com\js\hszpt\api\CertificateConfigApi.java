package com.js.hszpt.api;

import com.js.core.api.BaseApiPlus;
import com.js.core.common.vo.Result;
import com.js.hszpt.entity.CertificateConfig;
import com.js.hszpt.service.CertificateConfigService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/certificateConfig")
public class CertificateConfigApi extends BaseApiPlus<CertificateConfigService, CertificateConfig, String> {

    /**
     * 根据证书编号查询证书配置信息
     * @param certificateNumber 证书编号
     * @return
     */
    @GetMapping("/getByCertificateCertificateNumber/{certificateNumber}")
    public Result<CertificateConfig> getByCertificateCertificateNumber(@PathVariable String certificateNumber) {
        CertificateConfig certificateConfig = baseService.getByCertificateCertificateNumber(certificateNumber);
        return Result.success(certificateConfig);
    }
}

package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.common.entity.CurrentUser;import com.js.common.service.SecurityService;import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.entity.CertAccessLog;
import com.js.hszpt.mapper.CertAccessLogDao;
import com.js.util.JasyptUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 *
 * @ClassName:  CertAccessLogService
 * @Description:TODO(电子证照访问记录表接口实现)
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CertAccessLogService extends ServiceImpl<CertAccessLogDao,CertAccessLog> {

    @Autowired
    private  SecurityService securityService;
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CertAccessLog> findByCondition(CertAccessLog param, SearchVo searchVo, PageVo pageVo) {
		Page<CertAccessLog> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CertAccessLog> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}

	/**
	 *
	 * @Title: findByCondition
	 * @Description: TODO(多条件列表查询)
	 * @param param
	 * @param searchVo
	 * @return  List<CertAccessLog>
	 * @throws
	 */
	public List<CertAccessLog> findByCondition(CertAccessLog param, SearchVo searchVo){
		QueryWrapper<CertAccessLog> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");

		return this.list(queryWrapper);
	}


	/**
	 *
	 * @Title: getCondition
	 * @Description: TODO(构建自定义查询条件)
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CertAccessLog>
	 * @throws
	 */
	private QueryWrapper<CertAccessLog> getCondition(CertAccessLog param, SearchVo searchVo){
		QueryWrapper<CertAccessLog> queryWrapper = new QueryWrapper<CertAccessLog>();
		//请根据实际字段自行扩展

		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;

	}

	public static void main(String[] args) {

		System.out.println(JasyptUtil.encyptPwd("jsdp", "rs@2015#0505"));

		String encryptPwd = JasyptUtil.decyptPwd("jsdp","s4AzKRUmHOxCraqbap71YAM1ZKOinB4nDvWuwcd2uHQ=");
		System.out.println(encryptPwd);

	}

    /**
         *
         * @Title: certOperationLog
         * @Description: TODO(电子证照查询、电子证照核验逻辑配合统计功能进行改造，记录次数，
         * @param accessSource 访问来源
         * @param OperType 操作类型
         * @return  QueryWrapper<CertAccessLog>
         * @throws
         */
        public void certOperationLog(String accessSource, String OperType,String accessResult, String failReason,String certificateId){
            CertAccessLog certAccessLog = new CertAccessLog();
            // 访问来源 1-海事通APP 2-海事一网通办 3-智慧海事
            certAccessLog.setAccessSource(accessSource);
            // 操作类型：1-电子证照下载 2-查询 3-海事通APP扫描核验 4-其他APP扫码核验
            certAccessLog.setOperType(OperType);
            try {
                CurrentUser currUser = securityService.getCurrUser();
                if (currUser != null) {
                    certAccessLog.setAccessUserId(currUser.getId());
                    certAccessLog.setAccessUserName(currUser.getUsername());
                }
    		} catch (Exception e) {
    			log.error("获取当前用户失败", e);
    			certAccessLog.setAccessUserId("user");
    			certAccessLog.setAccessUserName("外网用户");
    			certAccessLog.setAccessIp("127.0.0.1");
    		}
    		certAccessLog.setAccessIp(securityService.getRequest().getRemoteAddr());
            // 访问结果：1-成功 0-失败
            certAccessLog.setAccessResult(accessResult);
            // 失败原因
            certAccessLog.setFailReason(failReason);
            //访问时间
            certAccessLog.setAccessTime(new Date());
            //创建时间
            certAccessLog.setCreateTime(new Date());
            certAccessLog.setCertificateId(StrUtil.isNotBlank(certificateId)?certificateId:"");
            baseMapper.insert(certAccessLog);
        }
}

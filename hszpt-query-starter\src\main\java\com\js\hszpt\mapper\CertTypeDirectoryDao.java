package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.hszpt.entity.CertTypeDirectory;
import com.js.hszpt.vo.CertTypeDirectoryDetailVO;
import com.js.hszpt.vo.CertTypeDirectoryListVO;
import com.js.hszpt.vo.CertTypeDirectoryQueryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * @ClassName:  CertTypeDirectory   
 * @Description:TODO(证照类型目录表数据处理层)   
 * @author:   System Generation 
 */
public interface CertTypeDirectoryDao extends BaseMapper<CertTypeDirectory> {

    List<CertTypeDirectoryListVO> queryCertTypeDirectoryList(@Param("page") Page<CertTypeDirectoryListVO> page,
                                                             @Param("query") CertTypeDirectoryQueryVO queryVO);

    /**
     * 查询证照类型目录详情
     */
    CertTypeDirectoryDetailVO getCertTypeDirInfo(@Param("certTypeDirId") String certTypeDirId);

}
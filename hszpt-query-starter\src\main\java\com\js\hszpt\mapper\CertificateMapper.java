package com.js.hszpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.hszpt.dto.ExternalNetworkQueryDto;
import com.js.hszpt.dto.IntranetQueryQueryDto;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.vo.CertificateIntranetQueryQueryVo;
import com.js.hszpt.vo.certificateExternalNetworkQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface CertificateMapper extends BaseMapper<Certificate> {

    IPage<certificateExternalNetworkQueryVo> certificateExternalNetworkQuery(Page page, @Param("externalNetworkQueryDto") ExternalNetworkQueryDto externalNetworkQueryDto);
    IPage<CertificateIntranetQueryQueryVo> certificateIntranetQueryQuery(Page page, @Param("intranetQueryQueryDto") IntranetQueryQueryDto intranetQueryQueryDto);
}

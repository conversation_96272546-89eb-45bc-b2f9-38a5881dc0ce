package com.js.hszpt.dto;

import lombok.Data;

/**
 * 证照签发详细统计结果DTO
 */
@Data
public class CertificateIssueDetailStatisticsDTO {

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构类型
     */
    private String orgType;
    
    /**
     * 机构名称
     */
    private String orgName;
    
    /**
     * 签发数量总和
     */
    private Long issueCount;
    
    /**
     * 签发数量占比
     */
    private Double issueRatio;
    
    /**
     * 窗口办理签发数量
     */
    private Long counterIssueCount;
    
    /**
     * 在线办理签发数量
     */
    private Long onlineIssueCount;
    
    /**
     * 环比率
     */
    private Double chainRatio;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 是否为汇总
     */
    private boolean isSummary;
} 
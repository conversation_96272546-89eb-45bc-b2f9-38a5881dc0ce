package com.js.hszpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.js.hszpt.dto.CertificateIssueDetailStatisticsDTO;
import com.js.hszpt.dto.CertificateIssueStatisticsDTO;
import com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO;
import com.js.hszpt.dto.CertificateTypeStatisticsDTO;
import com.js.hszpt.dto.CertificateTypeStatusStatisticsDTO;
import com.js.hszpt.dto.ApplicationSourceStatisticsDTO;
import com.js.hszpt.vo.CertificateIssueStatisticsVO;
import com.js.hszpt.dto.TimeStatisticsDTO;
import com.js.hszpt.entity.DwsCertificateIssue;

import java.util.List;
import java.util.Map;

/**
 * 电子证照签发情况统计Service接口
 */
public interface DwsCertificateIssueService extends IService<DwsCertificateIssue> {

    /**
     * 统计证照签发情况
     * 
     * @param param 查询参数
     * @return 统计结果
     */
    List<CertificateIssueStatisticsDTO> statisticsCertificateIssue(CertificateIssueStatisticsVO param);

    /**
     * 按年统计证照签发情况
     * 
     * @param param 查询参数
     * @return 按年统计结果
     */
    Map<String, List<CertificateIssueTimeTypeStatisticsDTO>> statisticsCertificateIssueByTimeType(
            CertificateIssueStatisticsVO param);

    /**
     * 详细统计证照签发情况
     * 
     * @param param 查询参数
     * @return 详细统计结果
     */
    List<CertificateIssueDetailStatisticsDTO> statisticsCertificateIssueDetail(CertificateIssueStatisticsVO param);

    /**
     * 按证照类型统计签发情况
     * 
     * @param param 查询参数
     * @return 按证照类型统计结果
     */
    List<CertificateTypeStatisticsDTO> statisticsByCertificateType(CertificateIssueStatisticsVO param);

    /**
     * 按证照类型和状态统计签发情况
     * 
     * @param param 查询参数
     * @return 按证照类型和状态统计结果
     */
    List<CertificateTypeStatusStatisticsDTO> statisticsByCertificateTypeAndStatus(CertificateIssueStatisticsVO param);

    /**
     * 按申请来源统计签发情况
     * 
     * @param param 查询参数
     * @return 按申请来源统计结果
     */
    List<ApplicationSourceStatisticsDTO> statisticsByApplicationSource(CertificateIssueStatisticsVO param);

    /**
     * 按时间统计签发情况
     * 
     * @param param 查询参数
     * @return 按时间统计结果
     */
    List<TimeStatisticsDTO> statisticsByTime(CertificateIssueStatisticsVO param);

    /**
     * 按证照类型统计签发情况（树形结构，用于旭日图展示）
     * 
     * @param param 查询参数
     * @return 树形结构的证照类型统计结果
     */
    List<CertificateTypeStatisticsDTO> statisticsByCertificateTypeForSunburst(CertificateIssueStatisticsVO param);
}
package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  Affair   
 * @Description:TODO(事项表)   
 * @author:   System Generation 
 */
@Data

@TableName("CTF_AFFAIR")
@ApiModel(value = "事项表")
public class CtfAffair extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 记录唯一标识
    */
    @TableId(value = "AFFAIR_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "记录唯一标识")
    private String id = String.valueOf(SnowFlakeUtil.nextId());


    /**
    * 父ID
    */
    @TableField("PARENT_ID")
    @ApiModelProperty(value = "父ID")
    private String parentId;


    /**
    * 序号
    */
    @TableField("SEQ")
    @ApiModelProperty(value = "序号")
    private Integer seq;


    /**
    * 事项编号
    */
    @TableField("AFFAIR_NUM")
    @ApiModelProperty(value = "事项编号")
    private String affairNum;


    /**
    * 事项类型 01 行政许可 02 行政处罚 03 行政强制 04 行政确认 06 行政给付 07 行政确认 10 其他行政权力 30 公共服务
    */
    @TableField("AFFAIR_TYPE")
    @ApiModelProperty(value = "事项类型 01 行政许可 02 行政处罚 03 行政强制 04 行政确认 06 行政给付 07 行政确认 10 其他行政权力 30 公共服务")
    private String affairType;


    /**
    * 事项名称
    */
    @TableField("AFFAIR_NAME")
    @ApiModelProperty(value = "事项名称")
    private String affairName;


    /**
    * 服务对象 0 全部 1 个人 2 企业 9 其他
    */
    @TableField("SERVE_USER")
    @ApiModelProperty(value = "服务对象 0 全部 1 个人 2 企业 9 其他")
    private String serveUser;


    /**
    * 业务类型 01 通行管理 02 船舶管理 03 人员管理 04 危险管理 05 安全管理 06 港口管理 07 航道管理
    */
    @TableField("SERVE_BUSINESS")
    @ApiModelProperty(value = "业务类型 01 通行管理 02 船舶管理 03 人员管理 04 危险管理 05 安全管理 06 港口管理 07 航道管理")
    private String serveBusiness;


    /**
    * 事项性质
    */
    @TableField("AFFAIR_NATURE")
    @ApiModelProperty(value = "事项性质")
    private String affairNature;


    /**
    * 服务层级
    */
    @TableField("SERVE_TOP")
    @ApiModelProperty(value = "服务层级")
    private String serveTop;


    /**
    * 业务办理方式：1"线上办理",2"预约办理",3"临柜办理",4"无",5"即办即办"
    */
    @TableField("HANDLE_STATUS")
    @ApiModelProperty(value = "业务办理方式：1\"线上办理\",2\"预约办理\",3\"临柜办理\",4\"无\",5\"即办即办\"")
    private String handleStatus;


    /**
    * 跳转类型 0 不跳转 1 跳转
    */
    @TableField("SKIP_TYPE")
    @ApiModelProperty(value = "跳转类型 0 不跳转 1 跳转")
    private String skipType;


    /**
    * 跳转地址
    */
    @TableField("SKIP_URL")
    @ApiModelProperty(value = "跳转地址")
    private String skipUrl;


    /**
    * 提交数
    */
    @TableField("COMMIT_COUNT")
    @ApiModelProperty(value = "提交数")
    private Integer commitCount;


    /**
    * 浏览数
    */
    @TableField("VIEW_COUNT")
    @ApiModelProperty(value = "浏览数")
    private Integer viewCount;


    /**
    * 创建人
    */
    @TableField("CREATE_OPEN_ID")
    @ApiModelProperty(value = "创建人")
    private String createOpenId;


    /**
    * 创建时间
    */
    @TableField("CREATE_DATA")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createData;


    /**
    * 修改人
    */
    @TableField("MODIFY_OPER_ID")
    @ApiModelProperty(value = "修改人")
    private String modifyOperId;


    /**
    * 修改时间
    */
    @TableField("MODITY_DATE")
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modityDate;


    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remark;


    /**
    * 扩展字段1
    */
    @ApiModelProperty(value = "扩展字段1")
    private String col1;


    /**
    * 扩展字段2
    */
    @ApiModelProperty(value = "扩展字段2")
    private String col2;


    /**
    * 扩展字段3
    */
    @ApiModelProperty(value = "扩展字段3")
    private String col3;


    /**
    * 扩展字段4
    */
    @ApiModelProperty(value = "扩展字段4")
    private String col4;


    /**
    * 扩展字段5
    */
    @ApiModelProperty(value = "扩展字段5")
    private String col5;


    /**
    * 文件类型ID
    */
    @ApiModelProperty(value = "文件类型ID")
    private String fileCategoryId;


    /**
    * 模板类型
    */
    @ApiModelProperty(value = "模板类型")
    private String modelType;


    /**
    * 服务对象明细（指当前服务事项所针对的单位名称）
    */
    @ApiModelProperty(value = "服务对象明细（指当前服务事项所针对的单位名称）")
    private String serveUserDetail;


    /**
    * 展示图片类别ID(存放文件类型ID)
    */
    @ApiModelProperty(value = "展示图片类别ID(存放文件类型ID)")
    private String vImgCategoryId;


    /**
    * 图片移入效果图
    */
    @ApiModelProperty(value = "图片移入效果图")
    private String pcIn;


    /**
    * 图片移出效果图
    */
    @ApiModelProperty(value = "图片移出效果图")
    private String pcOut;


    /**
    * 法定办结期限（天）
    */
    @ApiModelProperty(value = "法定办结期限（天）")
    private String legalDate;


    /**
    * 办理结果
    */
    @ApiModelProperty(value = "办理结果")
    private String dealResult;


    /**
    * 单点登录链接
    */
    @ApiModelProperty(value = "单点登录链接")
    private String pointUrl;


    /**
    * 印章
    */
    @ApiModelProperty(value = "印章")
    private String seal;


    /**
    * 删除标志：0-未删除 1-已删除
    */
    @TableField("DELETE_FLAG")
    @ApiModelProperty(value = "删除标志：0-未删除 1-已删除")
    private String deleteFlag;


    /**
    * 事项目录编码
    */
    @TableField("DIRECTORY_CODE")
    @ApiModelProperty(value = "事项目录编码")
    private String directoryCode;


    /**
    * wechat效果图展示
    */
    @TableField("PC_WECHAT")
    @ApiModelProperty(value = "wechat效果图展示")
    private String pcWechat;


    /**
    * 拼音
    */
    @TableField("JOIN_NUM")
    @ApiModelProperty(value = "拼音")
    private String joinNum;


    /**
    * 工作流模板
    */
    @TableField("WORKFLOW_TEMPLATE")
    @ApiModelProperty(value = "工作流模板")
    private String workflowTemplate;


    /**
    * 是否是父事项 0 否 1 是
    */
    @TableField("IS_FATHER")
    @ApiModelProperty(value = "是否是父事项 0 否 1 是")
    private String isFather;


    /**
    * 是否是子事项 0 否 1 是
    */
    @TableField("IS_SON")
    @ApiModelProperty(value = "是否是子事项 0 否 1 是")
    private String isSon;


    /**
    * 父事项名称
    */
    @TableField("PARENT_AFFAIR_NAME")
    @ApiModelProperty(value = "父事项名称")
    private String parentAffairName;


}
package com.js.hszpt.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;
import java.util.List;

/**
 * 子平台配置
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "zpt")
public class ZptProperties implements Serializable {

    private boolean mock;
    private String url;
    private String urlOracle;
    private String filePath;
    private List<DataReceiveJobProperties> dataReceiveJob;
}

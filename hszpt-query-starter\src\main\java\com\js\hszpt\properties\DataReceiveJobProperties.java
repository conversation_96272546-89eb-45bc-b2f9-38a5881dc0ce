package com.js.hszpt.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;

/**
 * 数据接收定时任务配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "zpt.data-receive-job")
public class DataReceiveJobProperties implements Serializable {

    private String taskName;
    private boolean enable = false;
    private String cron;
}

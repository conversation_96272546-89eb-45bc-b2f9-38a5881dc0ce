<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.SysDeptIambMapper">
    <select id="deptSon" resultType="com.js.hszpt.vo.SysDeptInfoResponse">
        select *
        from (select dept.DEPT_ID tree_id,
        dept.*,
        (select count(1)
        from SYS_DEPT deptSon
        where deptSon.PARENT_ID = dept.DEPT_ID and GOV_LEVEL &lt;= '3') is_More
        from (select dept.DEPT_ID, dept.name, dept.PARENT_ID, dept.CODE
        from SYS_DEPT dept
        <where>
            <if test="orgCode != null and orgCode != '' and flag == 1">
                and dept.DEPT_ID = #{orgCode}
            </if>
            <if test="orgCode != null and orgCode != '' and flag == 2">
                and dept.parent_id = #{orgCode}
            </if>
        </where>
        ) dept) as "tid.*iM"
        order by CODE
    </select>
</mapper>

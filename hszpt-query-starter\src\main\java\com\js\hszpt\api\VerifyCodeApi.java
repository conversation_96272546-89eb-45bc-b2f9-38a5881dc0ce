package com.js.hszpt.api;

import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.utils.CreateVerifyCode;
import com.js.hszpt.vo.Captcha;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/11/5 14:08
 */
@RestController
@RequestMapping("/VerifyCode")
public class VerifyCodeApi {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @GetMapping("/init")
    public Result initCaptcha(){
        String captchaId = UUID.randomUUID().toString().replace("-", "");
        String code = (new CreateVerifyCode()).randomStr(4);
        Captcha captcha = new Captcha();
        captcha.setCaptchaId(captchaId);
        this.redisTemplate.opsForValue().set("captcha::"+captchaId, code, 2L, TimeUnit.MINUTES);
        return ResultUtil.data(captcha);
    }

    @GetMapping("/draw/{captchaId}")
    public void drawCaptcha(@PathVariable("captchaId") String captchaId, HttpServletResponse response) throws IOException {
        String code = this.redisTemplate.opsForValue().get("captcha::"+captchaId);
        CreateVerifyCode vCode = new CreateVerifyCode(90, 40, 4, 19,20, code);
        response.setContentType("image/png");
        vCode.write(response.getOutputStream());
    }


}

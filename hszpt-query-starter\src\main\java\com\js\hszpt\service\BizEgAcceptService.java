package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.BizAffairApply;
import com.js.hszpt.entity.BizEgAccept;
import com.js.hszpt.mapper.BizEgAcceptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class BizEgAcceptService extends ServiceImpl<BizEgAcceptMapper, BizEgAccept> {

    @Autowired
    private BizAffairApplyService bizAffairApplyService;

    /**
     * 根据申请ID获取受理信息
     *
     * @param applyId 申请ID
     * @return 受理信息
     */
    public BizEgAccept getByApplyId(String applyId) {
        LambdaQueryWrapper<BizEgAccept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizEgAccept::getApplyId, applyId)
               .orderByDesc(BizEgAccept::getCreateDate)
               .last("LIMIT 1");
        return this.getOne(wrapper);
    }

}
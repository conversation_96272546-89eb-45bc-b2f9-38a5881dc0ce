package com.js.hszpt.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.CertQueryOrg;
import com.js.hszpt.mapper.CertQueryOrgMapper;
import com.js.hszpt.service.CertQueryOrgService;
import com.js.hszpt.vo.CertQueryOrgVO;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * 机构查询辅助表Service实现类
 */
@Service
@DS("dzzzdwdz")
public class CertQueryOrgServiceImpl extends ServiceImpl<CertQueryOrgMapper, CertQueryOrg> implements CertQueryOrgService {
    
    /**
     * 根据条件查询机构
     * 
     * @param queryVO 查询条件
     * @return 机构列表
     */
    @Override
    public List<CertQueryOrg> queryOrgs(CertQueryOrgVO queryVO) {
        LambdaQueryWrapper<CertQueryOrg> queryWrapper = new LambdaQueryWrapper<>();
        
        // 机构类型等于条件
        if (StrUtil.isNotBlank(queryVO.getQueryOrgType())) {
            queryWrapper.eq(CertQueryOrg::getQueryOrgType, queryVO.getQueryOrgType());
        }
        
        // 机构名称模糊匹配
        if (StrUtil.isNotBlank(queryVO.getQueryOrgName())) {
            queryWrapper.like(CertQueryOrg::getQueryOrgName, queryVO.getQueryOrgName());
        }
        
        return this.list(queryWrapper);
    }
} 
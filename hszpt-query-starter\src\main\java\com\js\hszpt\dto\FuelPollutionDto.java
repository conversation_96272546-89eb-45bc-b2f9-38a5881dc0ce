package com.js.hszpt.dto;

import com.js.hszpt.entity.Certificate;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class FuelPollutionDto extends Certificate {

    private String validDate1;
    private String validDate2;
    private String date1;
    private String date2;
    private String SecurityType1;
    private String SecurityType2;
    private String securityDate1;
    private String Duration2;
    private String name;
    private String address;
    private String shipName1;
    private String shipName2;
    private String grossTonnage;
    private String shipNumber;
    private String IMONumber;
    private String port1;
    private String port2;
    private String address1;
    private String address2;
    private String issueDept1;
    private String issueDept2;
    private String place1;
    private String place2;
    private String official1;
    private String official2;
    private String IssueJob1;
    private String IssueJob2;
    private String certificateNumber;
}

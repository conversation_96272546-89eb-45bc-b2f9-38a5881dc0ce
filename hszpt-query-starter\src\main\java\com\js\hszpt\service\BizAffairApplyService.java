package com.js.hszpt.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.enmus.AffairApplyCleanStatus;
import com.js.hszpt.enmus.ModelType;
import com.js.hszpt.entity.BizAffairApply;
import com.js.hszpt.mapper.BizAffairApplyMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BizAffairApplyService extends ServiceImpl<BizAffairApplyMapper, BizAffairApply> {

    public List<BizAffairApply> getApplyByModelType(ModelType modelType, AffairApplyCleanStatus applyCleanStatus) {
        QueryWrapper<BizAffairApply> queryWrapper = Wrappers.<BizAffairApply>query();
        queryWrapper.lambda()
                .and(wrapper -> wrapper
                        .inSql(BizAffairApply::getApplyId, "select apply_id from biz_eg_send_cert besc where besc.apply_id = apply_id")
                        .or()
                        .inSql(BizAffairApply::getApplyId, "select apply_id from biz_eg_cert_check becc where becc.apply_id = apply_id and becc.check_flag_code = '1'")
                )
                .eq(BizAffairApply::getModelType, modelType.getCode())
                .eq(BizAffairApply::getCleanStatus, applyCleanStatus.getCode())
                .last("LIMIT 100");
        return this.list(queryWrapper);
    }

    /**
     * 待清洗数据
     * @param modelType
     * @param applyCleanStatus
     * @return
     */
    public List<BizAffairApply> getApply(ModelType modelType, AffairApplyCleanStatus applyCleanStatus) {
        return baseMapper.getApply(modelType.getCode(),applyCleanStatus.getCode());
    }
}


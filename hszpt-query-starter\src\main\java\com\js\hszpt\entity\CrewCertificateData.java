package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableField;import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Transient;import java.util.Date;

@Data
@TableName("dwdz_certificate_data")
public class CrewCertificateData {
    @TableId
    private String dataId;                            // 证照数据主键
    private String certificateId;                     // 电子证照业务标识码
    private String certificateTypeName;               // 证照类型名称
    private String certificateTypeCode;               // 证照类型代码，21位编码
    private String certificateDefineAuthorityName;    // 证照定义机构
    private String certificateDefineAuthorityCode;    // 证照定义机构代码（统一社会信用代码）
    private String relatedItemName;                   // 关联事项名称
    private String relatedItemCode;                   // 关联事项编码
    private String certificateHolderCategory;         // 持证主体类别：1-自然人 2-法人或其他组织 3-混合 4-其他
    private String certificateHolderCategoryName;     // 持证主体类别名称：自然人/法人或其他组织/混合/其他
    private String validityRange;                     // 有效期限范围，多个用^分隔不同期限
    private String certificateIdentifier;             // 电子证照唯一标识码
    private String certificateName;                   // 证照名称
    private String certificateNumber;                 // 证照编号
    private String certificateIssuingAuthorityName;   // 证照颁发机构
    private String certificateIssuingAuthorityCode;   // 证照颁发机构代码（统一社会信用代码）
    private String certificateIssuedDate;             // 证照颁发日期（yyyy-mm-dd）
    private String certificateHolderName;             // 持证主体名称（自然人姓名/法人全称）
    private String certificateHolderCode;             // 持证主体代码（信用代码/身份证号等）
    private String certificateHolderTypeName;         // 持证主体代码类型：统一社会信用代码/公民身份号码/护照号/其他
    private String certificateEffectiveDate;          // 证照有效期开始日期（yyyy-mm-dd）
    private String certificateExpiringDate;           // 证照有效期截止日期（yyyy-mm-dd或"长期"）
    private String issueDeptCode2;                    // 证照颁发机构代码（海事内部统一编码2.0版本）
    private String issueDeptCode3;                    // 证照颁发机构代码（海事内部统一编码3.0版本）
    private String certificateAreaCode;               // 证照所属地区编码
    private String certificateStatus;                 // 证照状态（-5 异常,-4 撤回,-3 撤销,-2 注销,-1 作废,0 首次生成,1 生成已同步,2 修改未同步,3 过期,4 修改已同步,5 预览）
    private String creatorId;                         // 登记人员ID
    private String createTime;                        // 创建时间
    private String operatorId;                        // 最后操作人
    private String updateTime;                        // 修改时间
    private String filePath;                          // 文件保存位置
    private String syncStatus;                        // 同步状态（0-新增未上传,1-已上传，2-更新未同步，3-更新已上传,4-删除未上传,5-删除未上传）
    private String remarks;                           // 备注
    private String deptId;                            // 登记部门
    private String applyNum;                          // 申请编号
    private String affairType;                        // 事项类型
    private String serveBusiness;                     // 服务对象
    private String affairId;                          // 事项ID
    private String affairNum;                         // 事项编号
    private String sortName;                          // 归档编号
    private String certPrintNo;                       // 证书印刷号
    @TableField(exist = false)
    private String applyId;                           // 申请唯一ID，冗余该证书申请关联的唯一业务id
    @TableField(exist = false)
    private String shipId;                            // 船舶识别号 永久识别中国籍船舶的唯一编码
    @TableField(exist = false)
    private String shipName;                          // 船舶名称
    @TableField(exist = false)
    private String shipNameEn;                        // 船舶英文名
    @TableField(exist = false)
    private String shipImo;                           // IMO
    @TableField(exist = false)
    private String shipCallSign;                      // 船舶呼号
    @TableField(exist = false)
    private String shipMmsi;                          // MMSI
    @TableField(exist = false)
    private String applyType;                         // 申请来源
    @TableField(exist = false)
    private Date applyDate;                           // 申请日期
    @TableField(exist = false)
    private String acceptOrgCode2;                    // 受理机构编码2.0
    @TableField(exist = false)
    private Date acceptDate;                          // 受理日期
    @TableField(exist = false)
    private String apprOrgCode2;                      // 审批机构编码2.0
    @TableField(exist = false)
    private Date apprDate;                            // 审批日期
    @TableField(exist = false)
    private String acceptOrgCode3;                    // 受理机构编码3.0
    @TableField(exist = false)
    private String apprOrgCode3;                      // 审批机构编码3.0
    @TableField(exist = false)
    private String sourceCode;                        // 源系统代码
    @TableField(exist = false)
    private Date recCreateDate;                       // 记录创建日期
    @TableField(exist = false)
    private Date recModifyDate;                       // 记录修改日期
    //private String msaOrgCode;                        // 数据归属机构代码
}

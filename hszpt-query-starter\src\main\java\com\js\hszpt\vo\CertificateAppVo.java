package com.js.hszpt.vo;

import cn.hutool.core.bean.BeanUtil;
import com.js.hszpt.entity.Certificate;
import lombok.Data;

@Data
public class CertificateAppVo extends Certificate {

    private String certificateNameEn;

    private String holderNameEn;

    private String status;

    private String statusEn;

    public static CertificateAppVo build(Certificate certificate){
        CertificateAppVo certificateAppVo = new CertificateAppVo();
        BeanUtil.copyProperties(certificate,certificateAppVo);
        return certificateAppVo;
    }

}

package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CertTypeBatchOperationDetail   
 * @Description:TODO()   
 * @author:   System Generation 
 */
@Data

@TableName("CERT_TYPE_BATCH_OPERATION_DETAIL")
@ApiModel(value = "批量操作明细表")
public class CertTypeBatchOperationDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 批量操作明细记录主键ID
     */
    @TableId(value = "CERT_TYPE_BATCH_OPER_DTL_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "批量操作明细记录主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
     * 批量操作主记录的ID
     */
    @TableField("CERT_TYPE_BATCH_OPER_ID")
    @ApiModelProperty(value = "批量操作主记录的ID")
    private String batchId;

    /**
     * 关联证照类型目录表的ID
     */
    @TableField("CERT_TYPE_DIR_ID")
    @ApiModelProperty(value = "关联证照类型目录表的ID")
    private String certTypeDirId;

    /**
     * 操作结果：1-成功 2-失败
     */
    @TableField("OPERATION_RESULT")
    @ApiModelProperty(value = "操作结果：1-成功 2-失败")
    private String operationResult;

    /**
     * 操作失败的原因
     */
    @TableField("FAIL_REASON")
    @ApiModelProperty(value = "操作失败的原因")
    private String failReason;

    /**
     * 操作执行的时间
     */
    @TableField(value = "OPERATION_TIME", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "操作执行的时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;
}
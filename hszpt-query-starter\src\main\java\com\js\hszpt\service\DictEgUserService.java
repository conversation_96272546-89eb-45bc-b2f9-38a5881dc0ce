package com.js.hszpt.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.hszpt.entity.DictEgUser;
import com.js.hszpt.mapper.DictEgUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DictEgUserService extends ServiceImpl<DictEgUserMapper, DictEgUser> {


    public DictEgUser getDictEgUser(String idcardNo) {
        QueryWrapper<DictEgUser> queryWrapper = Wrappers.<DictEgUser>query();
        queryWrapper.lambda()
                .eq(DictEgUser::getIdcardNo, idcardNo)
                .last("LIMIT 1");
        return baseMapper.selectOne(queryWrapper);
    }
}

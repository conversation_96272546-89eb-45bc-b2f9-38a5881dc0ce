package com.js.hszpt.config;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.websocket.servlet.WebSocketMessagingAutoConfiguration;
import org.springframework.context.annotation.Configuration;

/**
 * 禁用 WebSocket 配置
 */
@Configuration
@EnableAutoConfiguration(exclude = {
    WebSocketMessagingAutoConfiguration.class
})
public class WebSocketConfig {
    // 空配置类
} 
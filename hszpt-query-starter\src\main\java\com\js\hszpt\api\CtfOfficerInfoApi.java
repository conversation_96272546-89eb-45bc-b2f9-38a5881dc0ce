package com.js.hszpt.api;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.annotation.SystemLog;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.enums.LogType;
import com.js.hszpt.entity.CtfOfficerInfo;
import com.js.hszpt.service.CtfOfficerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


 /**
 * 
 * @ClassName: CtfOfficerInfoApi  
 * @Description:TODO(官员信息表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "官员信息表接口")
@RequestMapping("/ctfOfficerInfo")
public class CtfOfficerInfoApi extends BaseApiPlus<CtfOfficerInfoService,CtfOfficerInfo,String>{

	@SystemLog(description = "官员信息表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CtfOfficerInfo>> getPage(@ModelAttribute CtfOfficerInfo param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CtfOfficerInfo> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "官员信息表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CtfOfficerInfo>> getList(@ModelAttribute CtfOfficerInfo param, @ModelAttribute SearchVo searchVo) {
		List<CtfOfficerInfo> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
}

package com.js.hszpt.constants;

import com.js.hszpt.entity.DictYthOrgMapping;
import com.js.hszpt.mapper.DictYthOrgMappingMapper;
import com.js.hszpt.service.DictYthOrgMappingService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Common类
 * <AUTHOR>
 */
@Data
@Component
@Slf4j
public class Common {

    // 加密/解密KEY
    public static final String ENCODE_KEY = "micsrvcloudframe";

    public static final String APPLY_NUM_PROFIX = "1401";

    // 定时器开关
    public static boolean TASK_JOB_OPEN = true;

    public static boolean TASK_JOB_CLOSE = false;
    /**
     * 新增静态的HashMap，用于存储旧机构编码和新机构编码的映射关系
     */
    public static final Map<String, String> codeMap = new HashMap<>();

    @Autowired
    private DictYthOrgMappingService dictYthOrgMappingService;

    @PostConstruct
    public void initCodeMap() {
        // List<DictYthOrgMapping> mappings = dictYthOrgMappingService.getAll();
        // log.info("获取dict_yth_org_Mapping表的旧机构和新机构编码执行开始：当前共 {} 条记录",  mappings.size());
        // for (DictYthOrgMapping mapping : mappings) {
        //     codeMap.put(mapping.getSrcOrgCode(), mapping.getOrgCode());
        // }
        // log.info("获取dict_yth_org_Mapping表的旧机构和新机构编码执行结束 当前 codeMap 包含 {} 条记录", codeMap.size());
    }

    public interface DateFormat {
        SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat SDF1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat SDF2 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat SDF3 = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat SDF4 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        SimpleDateFormat SDF5 = new SimpleDateFormat("yyyy/MM/dd");
        SimpleDateFormat SDF6 = new SimpleDateFormat("HH:mm:ss");
        SimpleDateFormat SDF7 = new SimpleDateFormat("yyyy年MM月dd日");
        SimpleDateFormat SDF8 = new SimpleDateFormat("yyyy");
        SimpleDateFormat SDF9 = new SimpleDateFormat("yyMMdd");
        SimpleDateFormat SDF10 = new SimpleDateFormat("yyMM");
        SimpleDateFormat SDF11 = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat SDF12 = new SimpleDateFormat("yyyyMMddHHmmss");
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        DateTimeFormatter DTF1 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        DateTimeFormatter DTF2 = DateTimeFormatter.ofPattern("HHmmss");
        DateTimeFormatter DTF3 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    }

    //未登录标识
    public static final String ANONYMOUS_USER = "anonymousUser";
    public static final String ANONYMOUS_USER_MESSAGE = "未获取到登录信息,请重新登录！";

    public static final String UP_PRO = "UP_PRO";

    public static final String APPLY_NUM = "APPLY_NUM";

    public static final String CD_BATCH = "CD_BATCH";

    public static final String UP_PRO_ACCEPT_COUNT = "UP_PRO_ACCEPT_COUNT";
    public static final String UP_PRO_PRECESS_COUNT = "UP_PRO_PRECESS_COUNT";
    public static final String UP_PRO_RESULT_COUNT = "UP_PRO_RESULT_COUNT";

    //部局官员信息接口
    public static final String OFFICER_LIST_URL = "/officerInfo/officerInfoList";
    public static final String OFFICER_INFO_URL = "/officerInfo/getOfficerInfo";

    /**
     * 业务申请:前端申请状态
     */
    public interface ApplyStatusHall {
        /**
         * 待提交
         */
        String TO_BE_SUBMITTED = "00";
        /**
         * 待受理
         */
        String TO_BE_ACCEPT = "01";
        /**
         * 待补正
         */
        String TO_BE_CORRECTION = "02";
        /**
         * 不予受理
         */
        String DENY_CORRECTION = "03";
        /**
         * 已受理
         */
        String ACCEPTED = "04";
        /**
         * 已受理(技术评审)
         */
        String ACCEPTED_TECHNICAL = "05";
        /**
         * 已办结
         */
        String BEEN_COMPLETED = "06";
        /**
         * 已办结(不予许可)
         */
        String BEEN_COMPLETED_NOT_ALLOW = "07";
        /**
         * 已办结(不予办理)
         */
        String BEEN_COMPLETED_NOT_HANDLE = "08";
        /**
         * 已取消
         */
        String BE_ALREADY_CANCELED = "09";
        /**
         * 已提交
         */
        String SUBMITTED = "14";
        /**
         * 已提交(需修改)
         */
        String SUBMITTED_HEED_MODIFY = "15";
        /**
         * 已提交(已审查)
         */
        String HAVE_REVIEW_HEED_MODIFY = "16";
        /**
         * 已撤回
         */
        String HAS_WITHDRAWN = "17";
        /**
         * 已提交(技术测定)
         */
        String submitted_technical = "18";
        /**
         * 待领证
         */
        String TO_BE_SEND_CERT = "23";
        /**
         * 管理平台已删除
         */
        String BEEN_DELATED = "27";
        /**
         * 航运公司工作流状态:
         */

        /**
         * 方案制作
         */
        String FANGAN_ZHIZUO = "30";
        /**
         * 方案审批  处长  局长
         */
        String FENGAN_SHENPI_CHU = "311";
        String FENGAN_SHENPI_JU = "312";
        /**
         * 方案分发
         */
        String FANGAN_FENFA = "32";
        /**
         * 审核实施
         */
        String SHENHE_SHISHI = "33";
        /**
         * 待初审
         */
        String DAI_CHUSHEN = "34";
        /**
         * 待复审
         */
        String DAI_FUSHEN = "35";
        /**
         * 待审批
         */
        String DAI_SHENPI = "36";
        /**
         * 内审材料提交
         */
        String INTERNAL_AUDIT = "37";
        /**
         * 建议受理意见
         */
        String ACCEPT_OPINION = "42";
        /**
         * 展期中
         */
        String ZHAN_QI = "38";
        /**
         * 变更中
         */
        String BIAN_GENG = "39";
        /**
         * 已办结(展期完)
         */
        String ZHAN_QI_END = "40";
        /**
         * 已办结(不予展期)
         */
        String NO_ZHAN_QI = "60";
        /**
         * 已办结(变更完)
         */
        String BIAN_GENG_END = "41";
        /**
         * 已办结(不予变更)
         */
        String NO_BIAN_GENG = "61";
        /**
         * 不符合规定子流程
         */
        String BFU_ZI = "43";
        /**
         * 整改完
         */
        String ZHENG_GAI_END = "44";
        /**
         * 整改中
         */
        String ZHENG_GAI_ZHONG = "45";

        //已办结（不予签注）
        String TO_QIAN_ZHU = "76";

        //疑似未报告船舶
        String SUSPECTED_SHIP_NOT_REPORTED ="1001";

        //不适用报告船舶
        String NOT_APPLICABLE_TO_REPORTING_VESSEL ="1002";


    }

    /**
     * 业务申请:后端申请状态
     */
    public interface ApplyStatus {
        //高速客船-待审核(现场核查中)
        String ON_SITE_VERIFICATION2 = "100";
        //高速客船-待审核(随船审查中)
        String ON_BOARD_REVIEW2 = "101";

        //待审核（审批退回）
        String TO_BE_AUDITED_FROM_APPROVAL = "111";
        //已办结（不同意）
        String BEEN_COMPLETED_DISAGREE = "112";
        /**
         * 待提交
         * 证书修正等使用
         */
        String TO_BE_SUBMITTED = "00";
        //待受理
        String TO_BE_ACCEPT = "01";
        //二次受理
        String TO_BE_ACCEPT_TWO = "011";
        //待补正
        String TO_BE_CORRECTION = "02";
        //不予受理
        String DENY_CORRECTION = "03";
        //待审核
        String TO_BE_AUDITED = "04";
        //待审批
        String TO_BE_APPROVAL = "05";
        //已办结
        String BEEN_COMPLETED = "06";
        //已办结(不予许可)
        String BEEN_COMPLETED_NOT_ALLOW = "07";
        //已办结(不予办理)
        String BEEN_COMPLETED_NOT_HANDLE = "08";
        //已取消
        String BE_ALREADY_CANCELED = "09";
        //已接收
        String BEEN_ACCEPT = "10";
        //驳回
        String BEEN_REJECT = "11";
        //部分办结
        String PART_OF_THE_TRANSFERRED = "12";
        //已审查
        String HAVE_REVIEW = "13";
        //已提交
        String HAS_BEEN_SUBMITTED = "14";
        //待现场检查
        String TO_BE_INSPECTED_ON_SITE = "15";
        //待审查
        String TO_BE_HAVE_REVIEW = "16";
        //待审查（修改）
        String TO_BE_HAVE_REVIEW_EDIT = "1601";
        //已审查（需修改）
        String HAVE_REVIEW_HEED_MODIFY = "17";
        //已撤回
        String HAS_WITHDRAWN = "18";
        //待现场确认
        String TO_BE_SITE_CONFIRM = "55";
        //上传确认书
        String TO_BE_UPLOAD_CONFIRM_BOOK = "56";
        //待审核(初审)
        String PENDING_REVIEW_FIRST_TRIAL = "22";
        //待审核(初审)(技术审查)
        String PENDING_REVIEW_FIRST_TRIAL_TECHNICAL_REVIEW = "23";
        //待审核(复审)
        String PENDING_REVIEW_REVIEW = "24";
        //待效能验收
        String PENDING_PERFORMANCE_ACCEPTANCE = "25";
        //待效能验收(技术测定)
        String PENDING_ACCEPTANCE_TECHNICAL_DETERMINATION = "26";
        //管理平台已删除
        String BEEN_DELATED = "27";

        // 航运公司工作流状态:
        //方案制作
        String FANGAN_ZHIZUO = "30";
        //方案审批  处长  局长
        String FENGAN_SHENPI_CHU = "311";
        String FENGAN_SHENPI_JU = "312";
        //方案分发
        String FANGAN_FENFA = "32";
        //审核实施
        String SHENHE_SHISHI = "33";
        //待初审
        String DAI_CHUSHEN = "34";
        //待复审
        String DAI_FUSHEN = "35";
        //待审批
        String DAI_SHENPI = "36";
        //内审材料提交
        String INTERNAL_AUDIT = "37";
        //建议受理意见
        String ACCEPT_OPINION = "42";
        //展期中
        String ZHAN_QI = "38";
        //变更中
        String BIAN_GENG = "39";
        //已办结(展期完)
        String ZHAN_QI_END = "40";
        //已办结(不予展期)
        String NO_ZHAN_QI = "60";
        //已办结(变更完)
        String BIAN_GENG_END = "41";
        //已办结(不予变更)
        String NO_BIAN_GENG = "61";
        //不符合规定子流程
        String BFU_ZI = "43";
        //整改完
        String ZHENG_GAI_END = "44";
        //整改中
        String ZHENG_GAI_ZHONG = "45";

        //待作业时间确报
        String WORK_TIME_CONFIRM = "47";
        //待作业数量确报
        String WORK_NUM_CONFIRM = "48";

        //待制证
        String TO_BE_ACCREDITATION = "49";
        //待校核 改为 待核证
        String TO_BE_CERTCHECK = "50";
        //待发证
        String TO_BE_SEND_CERT = "51";
        //已核证
        String TO_BE_CERTIFIED = "52";
        //待立案审核
        String LI_AN_REVIEW = "70";
        //待立案审批
        String LI_AN_APPROVE = "71";
        //待调查
        String REPORT = "72";
        //待异议申请
        String Y_Y_APPLY = "73";
        //已办结（不予撤回）
        String TO_CHE_HUI = "74";
        //已办结（不予撤销）
        String TO_CHE_XIAO = "84";
        //退回（不予立案）
        String TO_LI_AN = "75";
        //已办结（不予注销）
        String TO_ZHU_XIAO = "77";
        //预报待提交
        String TO_FORECAST_SUBMITTED = "62";
        //报告已提交
        String TO_BE_FORECAST_SUBMITTED = "63";
        //补报已提交
        String TO_BE_SUPPLEMENTARY_REPORT_SUBMITTED = "64";
        //确报已提交
        String TO_BE_CONFIRMED_SUBMISSION = "65";
        //已办结（核报）
        String TO_REPORT_CHECKING = "78";
        //已办结（已注销）
        String OVER_AND_LOGOUT = "80";
        //已办结（已撤销）
        String OVER_AND_CHEXIAO = "81";
        //已办结（已撤回）
        String OVER_AND_CHEHUI = "82";
        // 已办结（未通过）
        String NOT_ALLOW_IBR = "701";

        //疑似未报告船舶
        String SUSPECTED_SHIP_NOT_REPORTED ="1001";

        //不适用报告船舶
        String NOT_APPLICABLE_TO_REPORTING_VESSEL ="1002";

        //已超期
        String HAVE_EXTENDED = "0101";

        //已办结（超期）
        String BEEN_COMPLETED_EXTENDED  = "0801";

    }

    /**
     * 工作流业务状态
     */
    public interface ProcessStatus {
        //待受理
        String ACCEPT = "accept";
        //待审核
        String CHECK = "check";
        //初审
        String FIRSTCHECK = "firstCheck";
        //复审
        String AGAINCHECK = "againCheck";
        //待审批
        String APPROVE = "approve";
        //待制证
        String MAKECERT = "makeCert";
        //待证书校核
        String CERTCHECK = "certCheck";
        //待发证
        String SENDCERT = "sendCert";
        //结束
        String OVER = "over";
    }


    /**
     * 审核时的操作类型
     */
    public interface CheckOperationType {
        //技术评审
        String IS_TECHNICAL_REVIEW = "1";
        String NOT_TECHNICAL_REVIEW = "0";
        //现场核查
        String IS_SITE_VERIFICATION = "1";
        String NOT_SITE_VERIFICATION = "0";
        //发布通告
        String IS_VOYAGE_NOTICE = "1";
        String NOT_VOYAGE_NOTICE = "0";
        //部门评审
        String IS_DEPARTMENT_REVIEW = "1";
        String NOT_DEPARTMENT_REVIEW = "0";
        //重大关系
        String IS_IMPORTANT_PROFIT = "1";
        String NOT_IMPORTANT_PROFIT = "0";
    }

    /**
     * 技术评审操作类型
     */
    public interface TechnicalReview {
        //开始技术审核
        String BEGIN_TECHNICAL_REVIEW = "1";
        //技术审核通过
        String PASS_TECHNICAL_REVIEW = "2";
        //审核不通过
        String NOT_PASS_TECHNICAL_REVIEW = "3";
        //保存技术审核
        String SAVE_TECHNICAL_REVIEW = "4";
    }

    public interface IsCorrectedType {
        /**
         * 已补正
         */
        String BEEN_CORRECTION = "1";
        /**
         * 技术评审不通过,需要修改方案
         */
        String TO_BE_REVISED = "2";
        /**
         * 技术评审不通过,已修改方案,技术评审通过
         */
        String BEEN_REVISED = "3";
        /**
         * 审批过后:可变更
         */
        String VARIABLE_CHANGE = "4";
    }

    /**
     * 操作类型
     */
    public interface ApplyOperationType {
        /**
         * 保存
         */
        String SAVE = "1";
        /**
         * 提交
         */
        String COMMIT = "2";
    }

    /**
     * 审批结果
     */
    public interface ApproveResult {
        /**
         * 同意
         */
        String AGREEMENT = "1";
        /**
         * 拒绝
         */
        String DISAGREE = "2";
        /**
         * 退回
         */
        String BACK = "3";
        /**
         * 准予许可并发起跟踪审核
         */
        public static final String AGREEMENTFUA = "4";
    }

    /**
     * 证书校核结果
     */
    public interface CheckResult {
        /**
         * 校核通过
         */
        String PASS_CERT_CHECK = "1";
        /**
         * 校核不通过
         */
        String NOT_PASS_CERT_CHECK = "2";
        /**
         * 待校核
         */
        String AWAIT_CERT_CHECK = "3";
        /**
         * 空白证
         */
        String BLANK_CARD = "4";
    }

    /**
     * 事项类型
     */
    public interface AffairType {
        /**
         * 行政许可
         */
        String ADMINISTRATIVE_LICENSING = "01";
        /**
         * 行政确认
         */
        String ADMINISTRATIVE_CONFIRMATION = "07";
        /**
         * 行政备案
         */
        String ADMINISTRATIVE_RECORD = "30";
        /**
         * 其他
         */
        String OTHER = "99";
    }

    /**
     * 审批操作类型
     */
    public interface ApproveOperationType {
        /**
         * 同意
         */
        String SAVE = "1";
        /**
         * 拒绝
         */
        String COMMIT = "2";
    }

    /**
     * 锁定状态
     */
    public interface IsLocked {
        /**
         * 未锁定
         */
        String TO_BE_LOCK = "0";
        /**
         * 已锁定
         */
        String BEEN_LOCKED = "1";
    }

    /**
     * 消息模板
     */
    public interface NotifyModel {
        String SINGLE_LINK_40 = "单环节40";
        String WHOLR_80 = "整体80";
        String TECHNOLOGY_80 = "技术评审80";
        String ACCEPT_80 = "受理80";
        String RECORD_12 = "备案12小时";
    }

    /**
     * 短信模板
     */
    public interface ShortMessageModel {
        String WHOLR_80 = "整体80";
        String TO_BE_CORRECTION = "许可/确认材料补正";
        String ACCEPTED = "许可/确认申请受理";
        String DENY_CORRECTION = "许可/确认申请不予受理";
        String AGREEMENT = "许可/确认审批通过";
        String DISAGREE = "许可/确认审批不通过";
        String TECHNICAL_REVIEW = "技术评审";
        String TECHNICAL_REVIEW_PASS = "技术评审通过";
        String TECHNICAL_REVIEW_DENY = "技术评审不通过";
        String CERTIFICATES_TO_BE_CHECKED = "电子证照待查收";
        String TO_BE_CHECK = "校核通过";
        String PASSED_PERFORMANCE_ACCEPTANCE = "效能验收通过";
        String FAILURE_IN_PERFORMANCE_ACCEPTANCE = "效能验收不通过";
        String BEACON_TECHNICAL_REVIEW = "航标技术审查";
        String NAVIGATION_MARK_ACCEPTANCE_MATERIALS_HAVE_BEEN_SUBMITTED = "航标验收材料已提交";
        String NAVIGATION_MARK_ACCEPTANCE_MATERIALS_NEED_TO_BE_MODIFIED = "航标验收材料需修改";
        String BEACON_TECHNOLOGY_DETERMINATION = "航标技术测定";
        String SBMSHIPBUILDDATEAGREEMENT = "船舶建造审批通过";

    }

    /**
     * 消息状态
     */
    public interface NotifyStatus {
        //消息/短信未读
        String UNREAD = "0";
        //已读
        String READ = "1";
    }

    /**
     * 消息/短信发送对象
     */
    public interface NotifySendType {
        //前端
        String HALL = "0";
        //后天
        String MANAGE = "1";
    }

    /**
     * 消息/短息处理状态
     */
    public interface NotifyDealStatus {
        //未处理
        String NOT_DEAL = "0";
        //已处理
        String BEEN_DEAL = "1";
    }

    /**
     * 消息类型1:通知2:站内消息3:短信
     */
    public interface MessageType {
        //通知
        String NOTICE = "1";
        //站内消息
        String NOTIFY = "2";
        //短信
        String MESSAGE = "3";
    }

    /**
     * 业务类型 01 通航管理 02 船舶管理 03 船员管理 04 危防管理 05 安全管理 06 船检管理 07 航标管理
     */
    public interface ServeBusiness {
        //通航管理
        String NAVIGATION_MANAGEMENT = "01";
        //船舶管理
        String SHIP_MANAGEMENT = "02";
        //船员管理
        String CREW_MANAGEMENT = "03";
        //危防管理
        String DANGER_PREVENTION_MANAGEMENT = "04";
        //安全管理
        String SECURITY_MANAGEMENT = "05";
        //船检管理
        String SHIP_INSPECTION_MANAGEMENT = "06";
        //航标管理
        String NAVIGATION_MARK_MANAGEMENT = "07";
    }

    /**
     * 受理结果
     */
    public interface AcceptResult {
        String ACCEPT = "1";
        String NOT_ACCEPT = "3";
        String TO_BE_CORRECTION = "2";
        String SEND_FIRST_PERSON = "4";
    }

    /**
     * 审核结果
     */
    public interface ReviewResult {
        // 拟准予许可
        String PASS = "1";
        // 拟不予许可
        String NOT_PASS = "2";
        // 跳过复审
        String SKIP_PASS = "3";
        // 拟准予许可并发起跟踪审核
        String TO_BE_TRACK = "4";
        // 复审-办结通过
        String AGREEMENT = "5";
        // 复审-办结不通过
        String DISAGREE = "6";
    }

    public interface find {
        String url = "http://***************:9001/workflow/queryToDoTasks";
    }

    /**
     * 修改事项时的操作类型
     */
    public interface AffairOperationType {
        //新增
        String INSERT = "0";
        //删除
        String DELETE = "1";
        //更新
        String UPDATE = "2";
    }

    public interface DeptCode {
        String FIRSE_CODE = "00";
        String FIRSE_NAME = "交通运输部海事局";
    }

    public static final String SUCCESS = "success";

    /**
     * 电子证照账号
     */
    public static final String ELE_PERMIT_ACCOUNT_ID = "msaeg";
    /**
     * 电子证照身份认证信息
     */
    public static final String ELE_PERMIT_ACCESS_TOKEN = "msaeg";
    /**
     * 电子证照详情获取服务地址
     */
    public static final String FILE_QUERY_INFO_BY_CERTIFICATE_ID = "/certificateDataService/fileDownLoadByCertificateID";

    public static final String PIC_FILE_QUERY_INFO_BY_CERTIFICATE_ID = "/certificateDataService/picture";

    /**
     * 电子证照详情获取服务地址(包含作废电子证照)
     */
    public static final String FILE_QUERY_All_INFO_BY_CERTIFICATE_ID = "/certificateDataService/fileQueryAllByCertificateID";
    /**
     * 电子证照文件预览地址
     */
    public static final String FILE_VIEW_URL = "/admin/fdfs/download?fileUrl=";
    public static final String CORRECTION = "correction";

    /**
     * 电子证照文件下载服务地址-通过申请编号
     */
    public static final String FILE_DOWN_LOAD_BY_APPLY_NUM = "/certificateDataService/fileDownLoadByApplyNum";
    /**
     * 生成证照------>电子证照信息采集服务 上传证照元数据信息，实现证照的增删改操作
     */
    public static final String FILELICENSEUPLOAD = "/certificateDataService/fileLicenseUpload";

    public static final String SELECTIMPORTNUMBERLIST = "/certificateDataService/selectImportNumberList";
    /**
     * 查询证照------>电子证照检索服务 通过持证主体代码检索法人和自然人的电子证照，获取证照标识
     */
    public static final String FILERETRIEVAL = "/certificateDataService/fileRetrieval";
    /**
     * 下载证照------>基于证照标识下载服务 通过证照标识申请下载电子证照文件，此文件是根据‘’电子证照检索服务‘’获取的证照标识，生成的电子证照加注件或者电子证照原件
     */
    public static final String FILEDOWNLOADBYCERTIFICATEI = "/certificateDataService/fileDownLoadByCertificateI";
    /**
     * 通过申请编号删除证照
     */
    public static final String DEL_CERTIFICATE_BY_NUMBER = "/certificateDataService/fileDeleteByApplyNum";
    /**
     * 通过证照ID删除证照
     */
    public static final String DEL_CERTIFICATE_BY_ID = "/certificateDataService/fileDeleteByCertificateID";

    public static final String SEQ_TOP = "1";
    public static final String SEQ_ZORE = "0";

    public static final String PREFIX = "PREFIX";
    public static final String PREFIX_A = "PREFIX_A";

    // 行政区划代码
    public static Map<String, Object> govAreaCode = new HashMap<>();

    static {
        govAreaCode.put("北京", "110000000000");
        govAreaCode.put("天津", "120000000000");
        govAreaCode.put("河北", "130000000000");
        govAreaCode.put("山西", "140000000000");
        govAreaCode.put("内蒙古", "150000000000");
        govAreaCode.put("辽宁", "210000000000");
        govAreaCode.put("吉林", "220000000000");
        govAreaCode.put("黑龙江", "230000000000");
        govAreaCode.put("上海", "310000000000");
        govAreaCode.put("江苏", "320000000000");
        govAreaCode.put("浙江", "330000000000");
        govAreaCode.put("安徽", "340000000000");
        govAreaCode.put("福建", "350000000000");
        govAreaCode.put("江西", "360000000000");
        govAreaCode.put("山东", "370000000000");
        govAreaCode.put("河南", "410000000000");
        govAreaCode.put("湖北", "420000000000");
        govAreaCode.put("湖南", "430000000000");
        govAreaCode.put("广东", "440000000000");
        govAreaCode.put("广西", "450000000000");
        govAreaCode.put("海南", "460000000000");
        govAreaCode.put("重庆", "500000000000");
        govAreaCode.put("四川", "510000000000");
        govAreaCode.put("贵州", "520000000000");
        govAreaCode.put("云南", "530000000000");
        govAreaCode.put("西藏", "540000000000");
        govAreaCode.put("陕西", "610000000000");
        govAreaCode.put("甘肃", "620000000000");
        govAreaCode.put("青海", "630000000000");
        govAreaCode.put("宁夏", "640000000000");
        govAreaCode.put("新疆", "650000000000");
    }

    /**
     * 根据本系统层级编码匹配出机构简称
     */
    public static Map<String, String> shortName = new HashMap<>();
    public static final String CERT_NUMBER_DEPT_SHORT_NAME = "CERT_NUMBER_DEPT_SHORT_NAME";
    static {
        shortName.put("00", "部");
        shortName.put("01", "沪");
        shortName.put("02", "津");
        shortName.put("03", "辽");
        shortName.put("04", "冀");
        shortName.put("05", "鲁");
        shortName.put("06", "苏");
        shortName.put("07", "浙");
        shortName.put("08", "闽");
        shortName.put("09", "粤");
        shortName.put("10", "桂");
        shortName.put("11", "琼");
        shortName.put("12", "长");
        shortName.put("13", "黑");
        shortName.put("14", "深");
        shortName.put("15", "辽营");
        shortName.put("16", "鲁烟");
        shortName.put("17", "云");
        shortName.put("18", "闽厦");
        shortName.put("19", "粤汕");
        shortName.put("20", "粤湛江");
        shortName.put("0101", "沪吴淞");
        shortName.put("0102", "沪闵行");
        shortName.put("0103", "沪黄浦");
        shortName.put("0104", "沪杨浦");
        shortName.put("0105", "沪金山");
        shortName.put("0106", "沪崇明");
        shortName.put("0107", "沪浦东");
        shortName.put("0108", "沪宝山");
        shortName.put("0110", "沪洋山");
        shortName.put("0201", "津新港");
        shortName.put("0202", "津南疆");
        shortName.put("0203", "津海河");
        shortName.put("0204", "津北疆");
        shortName.put("0205", "津北疆");
        shortName.put("0208", "津东疆");
        shortName.put("0209", "津大沽");
        shortName.put("0210", "津大港");
        shortName.put("0301", "辽大");
        shortName.put("0302", "辽丹");
        shortName.put("0303", "辽锦");
        shortName.put("0304", "辽葫");
        shortName.put("0401", "冀秦");
        shortName.put("0402", "冀沧");
        shortName.put("0403", "冀唐");
        shortName.put("0404", "冀曹");
        shortName.put("0501", "鲁青");
        shortName.put("0502", "鲁济");
        shortName.put("0503", "鲁威");
        shortName.put("0504", "鲁日");
        shortName.put("0505", "鲁潍");
        shortName.put("0506", "鲁东");
        shortName.put("0507", "鲁滨");
        shortName.put("0508", "鲁董");
        shortName.put("0601", "苏宁");
        shortName.put("0602", "苏张");
        shortName.put("0603", "苏通");
        shortName.put("0604", "苏镇");
        shortName.put("0606", "苏澄");
        shortName.put("0607", "苏扬");
        shortName.put("0608", "苏常");
        shortName.put("0609", "苏泰");
        shortName.put("0610", "苏虞");
        shortName.put("0611", "苏太");
        shortName.put("0701", "浙甬");
        shortName.put("0702", "浙嘉");
        shortName.put("0703", "浙舟");
        shortName.put("0704", "浙温");
        shortName.put("0705", "浙台");
        shortName.put("0707", "浙杭");
        shortName.put("0801", "闽榕");
        shortName.put("0802", "闽宁");
        shortName.put("0803", "闽莆");
        shortName.put("0804", "闽泉");
        shortName.put("0806", "闽岚");
        shortName.put("0901", "粤穗");
        shortName.put("0902", "粤莞");
        shortName.put("0903", "粤珠");
        shortName.put("0904", "粤惠");
        shortName.put("0905", "粤河");
        shortName.put("0906", "粤汕尾");
        shortName.put("0907", "粤江");
        shortName.put("0908", "粤阳");
        shortName.put("0909", "粤中");
        shortName.put("0910", "粤佛");
        shortName.put("0911", "粤肇");
        shortName.put("0912", "粤云");
        shortName.put("0913", "粤清");
        shortName.put("0914", "粤韶");
        shortName.put("0915", "粤梅");
        shortName.put("0916", "粤茂");
        shortName.put("0917", "粤潮");
        shortName.put("0918", "粤揭");
        shortName.put("0919", "粤桥");
        shortName.put("1001", "桂南");
        shortName.put("1002", "桂北");
        shortName.put("1003", "桂防");
        shortName.put("1004", "桂钦");
        shortName.put("1005", "桂柳");
        shortName.put("1006", "桂河");
        shortName.put("1007", "桂桂林");
        shortName.put("1008", "桂贵");
        shortName.put("1009", "桂梧");
        shortName.put("1010", "桂百");
        shortName.put("1011", "桂来");
        shortName.put("1101", "琼海口");
        shortName.put("1102", "琼清澜");
        shortName.put("1103", "琼三亚");
        shortName.put("1104", "琼八所");
        shortName.put("1105", "琼浦");
        shortName.put("1106", "琼三沙");
        shortName.put("1201", "长渝");
        shortName.put("1205", "长宜");
        shortName.put("1206", "长荆");
        shortName.put("1207", "长岳");
        shortName.put("1208", "长汉");
        shortName.put("1209", "长黄");
        shortName.put("1210", "长浔");
        shortName.put("1211", "长安");
        shortName.put("1212", "长芜");
        shortName.put("1215", "长峡");
        shortName.put("1216", "长宜宾");
        shortName.put("1217", "长泸");
        shortName.put("1301", "黑哈");
        shortName.put("1302", "黑佳");
        shortName.put("1303", "黑黑");
        shortName.put("1304", "黑齐");
        shortName.put("1305", "黑牡");
        shortName.put("1306", "黑伊");
        shortName.put("1307", "黑大");
        shortName.put("1308", "黑鸡");
        shortName.put("1401", "深大亚湾");
        shortName.put("1402", "深蛇口");
        shortName.put("1403", "深盐田");
        shortName.put("1404", "深宝安");
        shortName.put("1405", "深南山");
        shortName.put("1406", "深大铲");
        shortName.put("1503", "盘");
        shortName.put("1701", "云盐");
        shortName.put("1801", "闽漳");
    }

    /**
     * 根据本系统层级编码匹配出机构代码
     */
    public static Map<String, String> deptCode = new HashMap<>();
    public static final String CERT_NUMBER_DEPT_CODE = "CERT_NUMBER_DEPT_CODE";
    static {
        deptCode.put("00", "0000");
        deptCode.put("01", "0100");
        deptCode.put("02", "0200");
        deptCode.put("03", "0300");
        deptCode.put("04", "0400");
        deptCode.put("05", "0500");
        deptCode.put("06", "0600");
        deptCode.put("07", "0700");
        deptCode.put("08", "0800");
        deptCode.put("09", "0900");
        deptCode.put("10", "1000");
        deptCode.put("11", "1100");
        deptCode.put("12", "1200");
        deptCode.put("13", "1300");
        deptCode.put("14", "1400");
        deptCode.put("15", "0301");
        deptCode.put("16", "0501");
        deptCode.put("17", "1500");
        deptCode.put("18", "0801");
        deptCode.put("19", "0901");
        deptCode.put("20", "0902");
        deptCode.put("0101", "0101");
        deptCode.put("0102", "0102");
        deptCode.put("0103", "0103");
        deptCode.put("0104", "0104");
        deptCode.put("0105", "0105");
        deptCode.put("0106", "0106");
        deptCode.put("0107", "0107");
        deptCode.put("0108", "0108");
        deptCode.put("0110", "0110");
        deptCode.put("0201", "0201");
        deptCode.put("0202", "0202");
        deptCode.put("0203", "0203");
        deptCode.put("0204", "0204");
        deptCode.put("0205", "0204");
        deptCode.put("0208", "0205");
        deptCode.put("0209", "0206");
        deptCode.put("0210", "0207");
        deptCode.put("0301", "0302");
        deptCode.put("0302", "0303");
        deptCode.put("0303", "0304");
        deptCode.put("0304", "0305");
        deptCode.put("0401", "0401");
        deptCode.put("0402", "0403");
        deptCode.put("0403", "0402");
        deptCode.put("0404", "0404");
        deptCode.put("0501", "0503");
        deptCode.put("0502", "0502");
        deptCode.put("0503", "0505");
        deptCode.put("0504", "0504");
        deptCode.put("0505", "0506");
        deptCode.put("0506", "0507");
        deptCode.put("0507", "0508");
        deptCode.put("0508", "0509");
        deptCode.put("0601", "0602");
        deptCode.put("0602", "0606");
        deptCode.put("0603", "0607");
        deptCode.put("0604", "0603");
        deptCode.put("0606", "0605");
        deptCode.put("0607", "0604");
        deptCode.put("0608", "0609");
        deptCode.put("0609", "0608");
        deptCode.put("0610", "0610");
        deptCode.put("0611", "0611");
        deptCode.put("0701", "0701");
        deptCode.put("0702", "0705");
        deptCode.put("0703", "0702");
        deptCode.put("0704", "0703");
        deptCode.put("0705", "0704");
        deptCode.put("0707", "0706");
        deptCode.put("0801", "0802");
        deptCode.put("0802", "0803");
        deptCode.put("0803", "0805");
        deptCode.put("0804", "0806");
        deptCode.put("0806", "0808");
        deptCode.put("0901", "0903");
        deptCode.put("0902", "0904");
        deptCode.put("0903", "0905");
        deptCode.put("0904", "0906");
        deptCode.put("0905", "0907");
        deptCode.put("0906", "0908");
        deptCode.put("0907", "0909");
        deptCode.put("0908", "0910");
        deptCode.put("0909", "0911");
        deptCode.put("0910", "0912");
        deptCode.put("0911", "0913");
        deptCode.put("0912", "0914");
        deptCode.put("0913", "0915");
        deptCode.put("0914", "0916");
        deptCode.put("0915", "0917");
        deptCode.put("0916", "0918");
        deptCode.put("0917", "0919");
        deptCode.put("0918", "0920");
        deptCode.put("0919", "0921");
        deptCode.put("1001", "1001");
        deptCode.put("1002", "1002");
        deptCode.put("1003", "1003");
        deptCode.put("1004", "1004");
        deptCode.put("1005", "1005");
        deptCode.put("1006", "1006");
        deptCode.put("1007", "1007");
        deptCode.put("1008", "1008");
        deptCode.put("1009", "1009");
        deptCode.put("1010", "1010");
        deptCode.put("1011", "1011");
        deptCode.put("1101", "1101");
        deptCode.put("1102", "1102");
        deptCode.put("1103", "1103");
        deptCode.put("1104", "1104");
        deptCode.put("1105", "1105");
        deptCode.put("1106", "1106");
        deptCode.put("1201", "1201");
        deptCode.put("1205", "1204");
        deptCode.put("1206", "1206");
        deptCode.put("1207", "1207");
        deptCode.put("1208", "1208");
        deptCode.put("1209", "1209");
        deptCode.put("1210", "1210");
        deptCode.put("1211", "1211");
        deptCode.put("1212", "1212");
        deptCode.put("1215", "1205");
        deptCode.put("1216", "1202");
        deptCode.put("1217", "1203");
        deptCode.put("1301", "1301");
        deptCode.put("1302", "1302");
        deptCode.put("1303", "1303");
        deptCode.put("1304", "1304");
        deptCode.put("1305", "1305");
        deptCode.put("1306", "1306");
        deptCode.put("1307", "1307");
        deptCode.put("1308", "1308");
        deptCode.put("1401", "1401");
        deptCode.put("1402", "1402");
        deptCode.put("1403", "1403");
        deptCode.put("1404", "1404");
        deptCode.put("1405", "1405");
        deptCode.put("1406", "1406");
        deptCode.put("1503", "1503");
        deptCode.put("1701", "1501");
        deptCode.put("1801", "0807");
    }

    /**
     * 业务对应的业务字母简称
     */
    public static Map<String, String> businessCode = new HashMap<>();

    static {
        businessCode.put("01", "TH");
        businessCode.put("02", "CB");
        businessCode.put("03", "CY");
        businessCode.put("04", "WF");
        businessCode.put("05", "GS");
        businessCode.put("06", "JY");
        businessCode.put("07", "HB");
    }

    public interface MsgStatus {
        String SUCCESS = "success";
        String FAIL = "fail";
    }

    public interface MethodType {
        String ADD = "add";
        String EDIT = "edit";
        String DELETE = "delete";
        String QUERY = "query";
        String DEL = "del";
    }

    public interface CommitType {
        String SAVE = "1";
        String COMMIT = "2";
    }

    /**
     * 编号类型
     */
    public interface NumberType {
        // 文书
        String DOCUMENT = "1";
        // 证书
        String CERTIFICATE = "2";
        // 航标专用证书编号类型
        String BEACONNUM = "3";
        // 航运专用证书编号类型
        String SHIPPINGNUM = "4";
        // 航运公司船舶专用证书编号类型
        String SHIPNUM = "5";
    }

    public interface ApplySecond {
        String FIRST = "1";
        String SECOND = "2";
    }

    /**
     * 已开通事项
     */
    public interface AffairNum {
        /**
         * 船舶进入或者穿越禁航区许可
         */
        String JTB15028 = "JTB15028";
        /**
         * 通航水域岸线安全使用许可/水上水下活动许可
         */
        String JTB15024 = "JTB15024";
        /**
         * 打捞或者拆除沿海水域内沉船沉物审批
         */
        String JTB15007 = "JTB15007";
        /**
         * 大型设施、移动式平台、超限物体水上拖带审批
         */
        String JTB15041 = "JTB15041";
        /**
         * 沿海水域划定禁航区和安全作业区审批
         */
        String JTB15006 = "JTB15006";
        /**
         * 专用航标的设置、撤除、位移和其他状况改变审批专用航标的设置、撤除、位移和其他状况改变审批
         */
        String JTB15043 = "JTB15043";
        /**
         * 残骸清除责任保险或其他财务保证证书签发
         */
        String MSAQR013 = "MSAQR013";
        /**
         * 船舶油污损害民事责任保险或其他财务保证证书核发
         */
        String JTB15020 = "JTB15020";
        /**
         * 香港籍残骸清除责任保险或其他财务保证证书签发
         */
        String MSAQR013XG = "MSAQR013XG";
        /**
         * 航运公司安全营运与防污染能力符合证明核发
         */
        String JTB15014 = "JTB15014";


    }

    /**
     * 子事项
     */
    public interface AffairSonNum {
        /**
         * 通航水域岸线安全使用许可
         */
        String JTB15024_1 = "JTB15024-1";
        /**
         * 水上水下活动许可
         */
        String JTB15024_2 = "JTB15024-2";
        /**
         * 公司审核
         */
        String JTB15014_1 = "JTB15014-1";
        /**
         * 船舶审核
         */
        String JTB15014_2 = "JTB15014-2";
    }


    /**
     * 业务对应的业务字母简称
     */
    public static Map<String, String> mechanismCode = new HashMap<>();

    static {
        mechanismCode.put("黑龙江海事局","HL");
        mechanismCode.put("哈尔滨海事局","HE");
        mechanismCode.put("佳木斯海事局","JM");
        mechanismCode.put("黑河海事局","HH");
        mechanismCode.put("齐齐哈尔海事局","QQ");
        mechanismCode.put("牡丹江海事局","MD");
        mechanismCode.put("伊春海事局","YC");
        mechanismCode.put("大庆海事局","DQ");
        mechanismCode.put("鸡西海事局","JX");
        mechanismCode.put("上海海事局","SH");
        mechanismCode.put("吴淞海事局","WS");
        mechanismCode.put("杨浦海事局","YP");
        mechanismCode.put("黄浦海事局","HP");
        mechanismCode.put("闵行海事局","MH");
        mechanismCode.put("宝山海事局","BS");
        mechanismCode.put("浦东海事局","PD");
        mechanismCode.put("崇明海事局","CM");
        mechanismCode.put("金山海事局","JS");
        mechanismCode.put("洋山港海事局","YS");
        mechanismCode.put("河北海事局","HB");
        mechanismCode.put("秦皇岛海事局","QH");
        mechanismCode.put("唐山海事局","TS");
        mechanismCode.put("沧州海事局","CZ");
        mechanismCode.put("曹妃甸海事局","CF");
        mechanismCode.put("深圳海事局","SZ");
        mechanismCode.put("大亚湾海事局","DY");
        mechanismCode.put("南山海事局","NS");
        mechanismCode.put("蛇口海事局","SK");
        mechanismCode.put("盐田海事局","YT");
        mechanismCode.put("宝安海事局","BA");
        mechanismCode.put("大铲海事局","DC");
        mechanismCode.put("广东海事局","GD");
        mechanismCode.put("汕头海事局","ST");
        mechanismCode.put("湛江海事局","ZJ");
        mechanismCode.put("广州海事局","GZ");
        mechanismCode.put("东莞海事局","DG");
        mechanismCode.put("珠海海事局","ZH");
        mechanismCode.put("惠州海事局","HZ");
        mechanismCode.put("河源海事局","HY");
        mechanismCode.put("汕尾海事局","SW");
        mechanismCode.put("江门海事局","JM");
        mechanismCode.put("阳江海事局","YJ");
        mechanismCode.put("中山海事局","ZS");
        mechanismCode.put("佛山海事局","FS");
        mechanismCode.put("肇庆海事局","ZQ");
        mechanismCode.put("云浮海事局","YF");
        mechanismCode.put("清远海事局","QY");
        mechanismCode.put("韶关海事局","SG");
        mechanismCode.put("梅州海事局","MZ");
        mechanismCode.put("茂名海事局","MM");
        mechanismCode.put("潮州海事局","CZ");
        mechanismCode.put("揭阳海事局","JY");
        mechanismCode.put("港珠澳大桥海事局","GZA");
        mechanismCode.put("辽宁海事局","LN");
        mechanismCode.put("营口海事局","YK");
        mechanismCode.put("盘锦海事局","PJ");
        mechanismCode.put("大连海事局","DL");
        mechanismCode.put("丹东海事局","DD");
        mechanismCode.put("锦州海事局","JZ");
        mechanismCode.put("葫芦岛海事局","HL");
        mechanismCode.put("浙江海事局","ZJ");
        mechanismCode.put("宁波海事局","NB");
        mechanismCode.put("舟山海事局","ZS");
        mechanismCode.put("温州海事局","WZ");
        mechanismCode.put("台州海事局","TZ");
        mechanismCode.put("嘉兴海事局","JX");
        mechanismCode.put("杭州海事局","HZ");
        mechanismCode.put("海南海事局","HN");
        mechanismCode.put("海口海事局","HK");
        mechanismCode.put("三亚海事局","SY");
        mechanismCode.put("八所海事局","BS");
        mechanismCode.put("洋浦海事局","YP");
        mechanismCode.put("清澜海事局","QL");
        mechanismCode.put("三沙海事局","SS");
        mechanismCode.put("广西海事局","GX");
        mechanismCode.put("南宁海事局","NN");
        mechanismCode.put("北海海事局","BH");
        mechanismCode.put("钦州海事局","QZ");
        mechanismCode.put("防城港海事局","FC");
        mechanismCode.put("贵港海事局","GG");
        mechanismCode.put("梧州海事局","WZ");
        mechanismCode.put("柳州海事局","LZ");
        mechanismCode.put("桂林海事局","GL");
        mechanismCode.put("河池海事局","HC");
        mechanismCode.put("百色海事局","BS");
        mechanismCode.put("来宾海事局","LB");
        mechanismCode.put("天津海事局","TJ");
        mechanismCode.put("新港海事局","XG");
        mechanismCode.put("南疆海事局","NJ");
        mechanismCode.put("海河海事局","HH");
        mechanismCode.put("北疆海事局","BJ");
        mechanismCode.put("东疆海事局","DJ");
        mechanismCode.put("大沽口海事局","DGK");
        mechanismCode.put("大港海事局","DG");
        mechanismCode.put("连云港海事局","LY");
        mechanismCode.put("盐城海事局","YC");
        mechanismCode.put("山东海事局","SD");
        mechanismCode.put("烟台海事局","YT");
        mechanismCode.put("济南海事局","JN");
        mechanismCode.put("青岛海事局","QD");
        mechanismCode.put("董家口海事局","DJ");
        mechanismCode.put("日照海事局","RZ");
        mechanismCode.put("威海海事局","WH");
        mechanismCode.put("潍坊海事局","WF");
        mechanismCode.put("东营海事局","DY");
        mechanismCode.put("滨州海事局","BZ");
        mechanismCode.put("福建海事局","FJ");
        mechanismCode.put("厦门海事局","XM");
        mechanismCode.put("漳州海事局","ZZ");
        mechanismCode.put("泉州海事局","QZ");
        mechanismCode.put("莆田海事局","PTI");
        mechanismCode.put("平潭海事局","PTA");
        mechanismCode.put("福州海事局","FZ");
        mechanismCode.put("宁德海事局","ND");
        mechanismCode.put("江苏海事局","JS");
        mechanismCode.put("南京海事局","NJ");
        mechanismCode.put("镇江海事局","ZJ");
        mechanismCode.put("扬州海事局","YZ");
        mechanismCode.put("泰州海事局","TZ");
        mechanismCode.put("常州海事局","CZ");
        mechanismCode.put("江阴海事局","JY");
        mechanismCode.put("南通海事局","NT");
        mechanismCode.put("张家港海事局","ZJG");
        mechanismCode.put("常熟海事局","CS");
        mechanismCode.put("太仓海事局","TC");
        mechanismCode.put("长江海事局","CJ");
        mechanismCode.put("重庆海事局","QQ");
        mechanismCode.put("宜宾海事局","YB");
        mechanismCode.put("泸州海事局","LZ");
        mechanismCode.put("宜昌海事局","YC");
        mechanismCode.put("三峡海事局","SX");
        mechanismCode.put("荆州海事局","JZ");
        mechanismCode.put("岳阳海事局","YY");
        mechanismCode.put("武汉海事局","WHA");
        mechanismCode.put("黄石海事局","HS");
        mechanismCode.put("九江海事局","JJ");
        mechanismCode.put("安庆海事局","AQ");
        mechanismCode.put("芜湖海事局","WHU");
    }

    public static String COMMA = ",";

    /**
     * 业务申请:前端申请状态
     */
    public interface SystemId {
        /**
         * 一网通办政务
         */
        String MSAEG = "00";
    }

    public interface ExceptionMessage {
        String CHECK_COMPANY_MSG = "检测到您是首次发起体系审核申请，请先对相关信息进行完善";
        String PARAM_IS_NOT_ENOUGH = "缺少撤回节点名称";
        String CODE_ERROR = "参数异常";
        String STATUS_ERROR = "状态异常";
        String NO_PERMISSION_BACK_APPLY = "撤回失败，此流程已被下一环节处理或您不具备撤回权限";
    }

    public static final String NUMBER_GENERATION_REDIS_KEY = "NUMBER_GENERATION";

    public enum RedisNamespaceNativeEnum {
        开发环境("dev-181", "zj_dev"),
        国交测试("test-181", "zj_dev"),
        国交UAT("test-hall", "zj_test_hall"),
        海事UAT("uat2", "zj_test_hall"),
        生产大厅("prod-hall", "zj_prod_hall"),
        生产内网("prod-manage", "zj_prod_manage");
        private String profilesNatice;
        private String redisNamespace;
        RedisNamespaceNativeEnum(String profilesNatice, String redisNamespace) {
            this.profilesNatice = profilesNatice;
            this.redisNamespace = redisNamespace;
        }
        public static String getRedisNamespace(String profilesNatice) {
            String str = "";
            RedisNamespaceNativeEnum[] values = RedisNamespaceNativeEnum.values();
            for (RedisNamespaceNativeEnum v : values) {
                if (v.profilesNatice.equals(profilesNatice)) {
                    str = v.redisNamespace;
                    break;
                }
            }
            return str;
        }
    }

}

package com.js.hszpt.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "certificate")
public class UrlConfig {
    /**
     * 电子证照接口IP+PORT
     */
    private String urlPermitHost;
    /**
     * 证书生成接口地址
     */
    private String chromatographyPrintingUrl;
    /**
     * 证书查看地址
     */
    private String checkChromatographyPrintingUrl;

    /**
     * 账号ID
     */
    private String accountId;

    /**
     * 身份认证信息
     */
    private String accessToken;
}

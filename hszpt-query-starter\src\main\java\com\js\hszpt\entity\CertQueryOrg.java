package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 机构查询辅助表实体类
 */
@Data
@TableName("dwdz_ctf_cert_query_org")
public class CertQueryOrg {
    
    /**
     * 主键
     */
    @TableId
    private String queyrOrgId;
    
    /**
     * 机构查询类型 1-签发机关 2-培训机构 3-主管医师机构 4-外派机构 5-审核机构
     */
    private String queryOrgType;
    
    /**
     * 机构查询名称
     */
    private String queryOrgName;
    
    /**
     * 记录创建日期
     */
    private Date recCreateDate;
    
    /**
     * 记录修改日期
     */
    private Date recModifyDate;
} 
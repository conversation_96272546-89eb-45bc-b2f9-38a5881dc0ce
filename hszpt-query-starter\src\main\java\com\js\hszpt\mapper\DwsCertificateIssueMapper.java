package com.js.hszpt.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.hszpt.dto.CertificateIssueDetailStatisticsDTO;
import com.js.hszpt.dto.CertificateIssueStatisticsDTO;
import com.js.hszpt.dto.CertificateIssueTimeTypeStatisticsDTO;
import com.js.hszpt.dto.CertificateTypeStatisticsDTO;
import com.js.hszpt.dto.CertificateTypeStatusStatisticsDTO;
import com.js.hszpt.dto.ApplicationSourceStatisticsDTO;
import com.js.hszpt.dto.TimeStatisticsDTO;
import com.js.hszpt.entity.DwsCertificateIssue;
import com.js.hszpt.vo.CertificateIssueStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 电子证照签发情况统计Mapper接口
 */
@Mapper
@DS("dzzzdws")
public interface DwsCertificateIssueMapper extends BaseMapper<DwsCertificateIssue> {

    /**
     * 统计证照签发情况
     * 
     * @param param 查询参数
     * @return 统计结果
     */
    List<CertificateIssueStatisticsDTO> statisticsCertificateIssue(@Param("param") CertificateIssueStatisticsVO param);

    /**
     * 按年统计证照签发情况
     * 
     * @param param 查询参数
     * @return 按年统计结果
     */
    List<CertificateIssueTimeTypeStatisticsDTO> statisticsCertificateIssueByYear(
            @Param("param") CertificateIssueStatisticsVO param);

    /**
     * 按时间维度统计非海事机构证照签发情况
     * 
     * @param param 查询参数
     * @return 按年统计结果
     */
    List<CertificateIssueTimeTypeStatisticsDTO> statisticsCertificateIssueByTimeType(
            @Param("param") CertificateIssueStatisticsVO param);

    /**
     * 按时间维度统计海事机构证照签发情况
     * 
     * @param param 查询参数
     * @return 按年统计结果
     */
    List<CertificateIssueTimeTypeStatisticsDTO> statisticsCertificateIssueOrgByTimeType(
            @Param("param") CertificateIssueStatisticsVO param);

    /**
     * 详细统计证非海事机构照签发情况
     * 
     * @param param 查询参数
     * @return 详细统计结果
     */
    List<CertificateIssueDetailStatisticsDTO> statisticsCertificateIssueDetail(
            @Param("param") CertificateIssueStatisticsVO param);

    /**
     * 详细统计海事机构证照签发情况
     * 
     * @param param 查询参数
     * @return 详细统计结果
     */
    List<CertificateIssueDetailStatisticsDTO> statisticsCertificateIssueOrgDetail(
            @Param("param") CertificateIssueStatisticsVO param);

    /**
     * 获取上一个周期的签发总量
     * 
     * @param param   查询参数
     * @param orgCode 机构编码
     * @return 上一个周期的签发总量
     */
    Long getPreviousPeriodIssueCount(@Param("param") CertificateIssueStatisticsVO param,
            @Param("orgCode") String orgCode);

    /**
     * 获取上一个周期的签发总量
     * 
     * @param param       查询参数
     * @param orgCodeList 机构编码列表
     * @return 上一个周期的签发总量
     */
    List<CertificateIssueDetailStatisticsDTO> getPreviousPeriodIssueCountByOrgCode(
            @Param("param") CertificateIssueStatisticsVO param, @Param("orgCodeList") List<String> orgCodeList);

    /**
     * 获取当前周期的总签发量
     * 
     * @param param 查询参数
     * @return 当前周期的总签发量
     */
    CertificateIssueDetailStatisticsDTO getTotalIssueCount(@Param("param") CertificateIssueStatisticsVO param);

    /**
     * 根据机构编码获取机构名称
     * 
     * @param orgCode 机构编码
     * @return 机构名称
     */
    String getOrgNameByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 按证照类型统计签发情况
     * 
     * @param param 查询参数
     * @return 按证照类型统计结果
     */
    List<CertificateTypeStatisticsDTO> statisticsByCertificateType(@Param("param") CertificateIssueStatisticsVO param);

    /**
     * 按证照类型和状态统计签发情况
     * 
     * @param param 查询参数
     * @return 按证照类型和状态统计结果
     */
    List<CertificateTypeStatusStatisticsDTO> statisticsByCertificateTypeAndStatus(
            @Param("param") CertificateIssueStatisticsVO param);

    /**
     * 获取上一个周期按证照类型统计的签发量
     * 
     * @param param               查询参数
     * @param certificateTypeCode 证照类型代码
     * @return 上一个周期的签发量
     */
    Long getPreviousPeriodIssueCountByType(@Param("param") CertificateIssueStatisticsVO param,
            @Param("certificateTypeCode") String certificateTypeCode);

    /**
     * 按申请来源统计签发情况
     * 
     * @param param 查询参数
     * @return 按申请来源统计结果
     */
    List<ApplicationSourceStatisticsDTO> statisticsByApplicationSource(
            @Param("param") CertificateIssueStatisticsVO param);

    /**
     * 按时间统计签发情况
     * 
     * @param param 查询参数
     * @return 按时间统计结果
     */
    List<TimeStatisticsDTO> statisticsByTime(@Param("param") CertificateIssueStatisticsVO param);

    /**
     * 批量获取上一周期的签发数量
     *
     * @param param 查询参数
     * @return Map<证照类型代码, 签发数量>
     */
    List<CertificateTypeStatusStatisticsDTO> getPreviousPeriodIssueCountBatch(@Param("param") CertificateIssueStatisticsVO param);
}
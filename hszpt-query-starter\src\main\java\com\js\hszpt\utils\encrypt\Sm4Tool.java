package com.js.hszpt.utils.encrypt;

import java.security.Key;
import java.security.SecureRandom;
import java.security.Security;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;

public class Sm4Tool {

    private static final String ALGORITHM_NAME = "SM4";
    private static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS7Padding";
    private static final String ALGORITHM_NAME_ECB_NOPADDING = "SM4/ECB/NoPadding";
    private static final String ALGORITHM_NAME_CBC_PADDING = "SM4/CBC/PKCS5Padding";
    private static final String ALGORITHM_NAME_CBC_NOPADDING = "SM4/CBC/NoPadding";

    static {
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    public static String generateKey() {
        try {
            KeyGenerator kg = KeyGenerator.getInstance(ALGORITHM_NAME, BouncyCastleProvider.PROVIDER_NAME);
            kg.init(128, new SecureRandom()); // 目前只支持16字符长度的Key
            return Hex.toHexString(kg.generateKey().getEncoded());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String encrypt(String data, String key) {
        try {
            Cipher cipher = generateECBCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, Hex.decode(key));
            // BASE64做转码。同时能起到2次加密的作用。
            return new String(Base64.encode(cipher.doFinal(data.getBytes())),"UTF8");
//            return Hex.toHexString(cipher.doFinal(data.getBytes()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String decrypt(String cipherText, String key) {
        try {
            Cipher cipher = generateECBCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, Hex.decode(key));
            return new String(cipher.doFinal(Base64.decode(cipherText)));
//            return new String(cipher.doFinal(Hex.decode(cipherText)));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static Cipher generateECBCipher(String algorithmName, int mode, byte[] key) {
        try {
            Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
            Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
            cipher.init(mode, sm4Key);
            return cipher;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
//        String s = Sm4Tool.generateKey();
//        System.out.println(s);

//        String uuid = java.util.UUID.randomUUID().toString();
//        uuid = uuid.replaceAll("-","");
//        System.out.println(uuid);
//        System.out.println(Hex.decode(uuid));

        String testData = "0100_1879402722806026242";
        String testKey = "5d3b282609644b4f8992b31a2e92f0f3";

        String encrypt = Sm4Tool.encrypt(testData, testKey);
        System.out.println("sm4加密：" + encrypt);

        String decrypt = Sm4Tool.decrypt(encrypt, testKey);
        // 修复敏感信息泄露漏洞：不直接输出解密后的敏感数据
        // System.out.println("sm4解密：" + decrypt);

        // 仅验证加解密功能是否正常，不输出敏感信息
        boolean isDecryptionSuccessful = testData.equals(decrypt);
        System.out.println("sm4解密验证：" + (isDecryptionSuccessful ? "成功" : "失败"));
    }
}

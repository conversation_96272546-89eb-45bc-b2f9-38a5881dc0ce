package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 业务官员信息表
 */
@Data
@TableName("APPLY_OFFICER_INFO")
public class ZwApplyOfficerInfo {

    @TableId
    private String officerInfoId;

    private String windowApplyId;

    private String officerName;

    private String sex;

    private String department;

    private String position;

    private String mechanism;

    private String processstatus;

    private String col2;

    private String col3;

    private String col4;

    private String modifyOperId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modityDate;

    private String officialNameEn;

    private String officialTitleEn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cdcdate;

}
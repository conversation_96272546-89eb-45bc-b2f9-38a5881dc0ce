package com.js.hszpt.api;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.js.hszpt.entity.CrewCertificateData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.Result;
import com.js.hszpt.dto.CrewCertificateQueryDto;
import com.js.hszpt.dto.IntranetCrewQueryDto;
import com.js.hszpt.entity.Certificate;
import com.js.hszpt.properties.ZptProperties;
import com.js.hszpt.service.BizAffairApplyService;
import com.js.hszpt.service.CertificateService;
import com.js.hszpt.service.CrewCertificateService;
import com.js.hszpt.vo.CertificateIntranetCrewQueryVo;
import com.js.hszpt.vo.CrewCertificateQueryVo;
import com.js.hszpt.entity.CertQueryOrg;
import com.js.hszpt.service.CertQueryOrgService;
import com.js.hszpt.vo.CertQueryOrgVO;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/certificate")
@Slf4j
public class CertificateApi extends BaseApiPlus<CertificateService, Certificate, String> {

    @Autowired
    private BizAffairApplyService bizAffairApplyService;

    @Autowired
    private ZptProperties zptProperties;

    @Value("${qrcode.redirectUrl}")
    private String redirectUrl;

    @Autowired
    private CrewCertificateService crewCertificateService;

    @Autowired
    private CertQueryOrgService certQueryOrgService;

    @Autowired
    private CrewCertificateService  crewCertificateDataService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 生成缓存key
     * @param crewCertificateQueryDto 查询条件
     * @param page 分页参数
     * @return 缓存key
     */
    private String generateCacheKey(CrewCertificateQueryDto crewCertificateQueryDto, Page page) {
        StringBuilder keyBuilder = new StringBuilder("crew_cert_query::");

        try {
            // 处理CrewCertificateQueryDto的字段
            if (crewCertificateQueryDto != null) {
                Field[] fields = CrewCertificateQueryDto.class.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    Object value = field.get(crewCertificateQueryDto);
                    if (value != null && StrUtil.isNotBlank(value.toString())) {
                        keyBuilder.append(field.getName()).append("=").append(value).append("&");
                    }
                }
            }

            // 处理Page的字段
            if (page != null) {
                keyBuilder.append("current=").append(page.getCurrent()).append("&");
                keyBuilder.append("size=").append(page.getSize()).append("&");
            }

            // 移除最后一个&符号
            String key = keyBuilder.toString();
            if (key.endsWith("&")) {
                key = key.substring(0, key.length() - 1);
            }

            log.debug("生成的缓存key: {}", key);
            return key;
        } catch (Exception e) {
            log.error("生成缓存key异常：", e);
            // 如果生成key失败，返回一个基础key加时间戳，确保不会缓存
            return "crew_cert_query::error::" + System.currentTimeMillis();
        }
    }

    /**
     * 外网船员证书查询接口
     *
     * 支持根据多种条件查询船员电子证照信息，包括：
     * - 证书编号（certificateNumber）
     * - 持证人姓名（certificateHolderName）
     * - 身份证号（certificateHolderCode）
     * - 证照类型（certificateTypeCode）- 必填
     * - 证书印刷号（certPrintNo）
     *
     * @param crewCertificateQueryDto 查询参数DTO
     * @param page 分页参数
     * @return 查询结果，包含船员电子证照信息列表
     */
    @GetMapping("/crewCertificateQuery")
    public Result<Map<String, Object>> crewCertificateQuery(
            CrewCertificateQueryDto crewCertificateQueryDto, Page page) {
        log.info("开始处理船员证书查询请求，参数：{}", crewCertificateQueryDto);

        // 生成缓存key
        String cacheKey = generateCacheKey(crewCertificateQueryDto, page);

        try {
            // 先从Redis中查询缓存
            String cachedResult = redisTemplate.opsForValue().get(cacheKey);
            if (StrUtil.isNotBlank(cachedResult)) {
                log.info("从缓存中获取到查询结果，key: {}", cacheKey);
                // 将JSON字符串转换为Result对象
                Map<String, Object> resultMap = JSON.parseObject(cachedResult, Map.class);
                return Result.success(resultMap);
            }

            log.info("缓存中未找到数据，执行数据库查询，key: {}", cacheKey);
            // 执行原来的查询逻辑
            Result<Map<String, Object>> result = crewCertificateService.queryCrewCertificates(crewCertificateQueryDto, page);

            // 只有查询成功时才缓存结果
            if (result != null && result.isSuccess() && result.getResult() != null) {
                try {
                    // 将查询结果转换为JSON字符串并存入Redis，设置30分钟过期时间
                    String jsonResult = JSON.toJSONString(result.getResult());
                    redisTemplate.opsForValue().set(cacheKey, jsonResult, 30, TimeUnit.MINUTES);
                    log.info("查询结果已缓存到Redis，key: {}, 过期时间: 30分钟", cacheKey);
                } catch (Exception cacheException) {
                    log.error("缓存查询结果异常，但不影响正常返回，key: {}", cacheKey, cacheException);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("处理船员证书查询请求异常：", e);
            return ResultUtil.error("系统异常：" + e.getMessage());
        }
    }

    /**
     * 内网船员证书查询
     */
    @GetMapping("/certificateIntranetCrewQuery")
    public Result<IPage<CertificateIntranetCrewQueryVo>> certificateIntranetCrewQueryAuth(
            IntranetCrewQueryDto intranetCrewQueryDto) {
        try {
            Page page = new Page(intranetCrewQueryDto.getCurrent(), intranetCrewQueryDto.getSize());
            return Result.success(crewCertificateService.certificateIntranetCrewQueryAuth(intranetCrewQueryDto, page));
        } catch (RuntimeException e) {
            log.error("船员证书查询失败: {}", e.getMessage());
            return Result.failed(500, e.getMessage());
        } catch (Exception e) {
            log.error("船员证书查询失败", e);
            return Result.failed(500, "系统异常");
        }
    }

    /**
     * 查询证书相关机构
     *
     * @param queryVO 查询条件
     * @return 机构列表
     */
    @GetMapping("/queryCertOrgs")
    public Result<List<CertQueryOrg>> queryCertOrgs(CertQueryOrgVO queryVO) {
        try {
            log.info("开始查询证书相关机构，参数：{}", queryVO);
            List<CertQueryOrg> orgList = certQueryOrgService.queryOrgs(queryVO);
            log.info("查询证书相关机构完成，共查询到 {} 条记录", orgList.size());
            return Result.success(orgList);
        } catch (Exception e) {
            log.error("查询证书相关机构异常：", e);
            return Result.failed(500, "系统异常：" + e.getMessage());
        }
    }

    /**
    * 透传 certificateId获取证照
     */
    @GetMapping("/certById/{certificateId}")
    public CrewCertificateData certById(@PathVariable String certificateId) {
        return crewCertificateDataService.getOne(new QueryWrapper<CrewCertificateData>().eq("CERTIFICATE_ID",certificateId));
    }
}

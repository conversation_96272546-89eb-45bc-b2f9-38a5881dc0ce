package com.js.hszpt.api;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.annotation.SystemLog;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.enums.LogType;
import com.js.hszpt.entity.CtfCertificateType;
import com.js.hszpt.service.CtfCertificateTypeService;
import com.js.hszpt.dto.CtfCertificateTypeQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


 /**
 * 
 * @ClassName: CtfCertificateTypeApi  
 * @Description:TODO(证照分类表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "证照分类表接口")
@RequestMapping("/ctfCertificateType")
public class CtfCertificateTypeApi extends BaseApiPlus<CtfCertificateTypeService,CtfCertificateType,String>{

	@SystemLog(description = "证照分类表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<CtfCertificateType>> getPage(@ModelAttribute CtfCertificateType param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<CtfCertificateType> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "证照分类表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<CtfCertificateType>> getList(@ModelAttribute CtfCertificateType param, @ModelAttribute SearchVo searchVo) {
		List<CtfCertificateType> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
	@SystemLog(description = "获取证照名称下拉列表", type = LogType.OPERATION)
	@RequestMapping(value = "/getCertificateNameList", method = RequestMethod.GET)
	@ApiOperation(value = "获取证照名称下拉列表")
	public Result<List<String>> getCertificateNameList() {
		log.info("开始查询证照名称下拉列表");
		List<String> nameList = this.baseService.getCertificateNameList();
		log.info("查询到 {} 个证照名称", nameList.size());
		return ResultUtil.data(nameList);
	}

	@SystemLog(description = "根据条件查询证照分类", type = LogType.OPERATION)
	@RequestMapping(value = "/queryCertificateTypeList", method = RequestMethod.GET)
	@ApiOperation(value = "根据条件查询证照分类")
	public Result<List<CtfCertificateType>> queryCertificateTypeList(@ModelAttribute CtfCertificateTypeQueryDto queryDto) {
		log.info("开始根据条件查询证照分类，参数：{}", queryDto);
		List<CtfCertificateType> list = this.baseService.queryByCondition(queryDto);
		log.info("条件查询证照分类完成，查询到 {} 条记录", list.size());
		return ResultUtil.data(list);
	}
}

package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.entity.CtfCertificateType;
import com.js.hszpt.dto.CtfCertificateTypeQueryDto;
import com.js.hszpt.mapper.CtfCertificateTypeDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
 
/**
 * 
 * @ClassName:  CtfCertificateTypeService    
 * @Description:TODO(证照分类表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CtfCertificateTypeService extends ServiceImpl<CtfCertificateTypeDao,CtfCertificateType> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CtfCertificateType> findByCondition(CtfCertificateType param, SearchVo searchVo, PageVo pageVo) {
		Page<CtfCertificateType> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CtfCertificateType> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<CtfCertificateType>      
	 * @throws
	 */
	public List<CtfCertificateType> findByCondition(CtfCertificateType param, SearchVo searchVo){
		QueryWrapper<CtfCertificateType> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<CtfCertificateType>      
	 * @throws
	 */
	private QueryWrapper<CtfCertificateType> getCondition(CtfCertificateType param, SearchVo searchVo){
		QueryWrapper<CtfCertificateType> queryWrapper = new QueryWrapper<CtfCertificateType>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	/**
	 * 获取证照名称列表
	 * @return 证照名称列表
	 */
	public List<String> getCertificateNameList() {
		QueryWrapper<CtfCertificateType> queryWrapper = new QueryWrapper<>();
		queryWrapper.select("DISTINCT certificate_name")
				   .orderByAsc("certificate_name");
		
		return this.list(queryWrapper)
				   .stream()
				   .map(CtfCertificateType::getCertificateName)
				   .filter(name -> name != null && !name.isEmpty())
				   .collect(Collectors.toList());
	}

	/**
	 * 根据条件查询证照分类
	 * @param queryDto 查询条件
	 * @return 证照分类列表
	 */
	public List<CtfCertificateType> queryByCondition(CtfCertificateTypeQueryDto queryDto) {
		LambdaQueryWrapper<CtfCertificateType> queryWrapper = new LambdaQueryWrapper<>();
		
		// 添加查询条件
		if (StrUtil.isNotBlank(queryDto.getCertificateName())) {
			queryWrapper.eq(CtfCertificateType::getCertificateName, queryDto.getCertificateName());
		}
		
		if (StrUtil.isNotBlank(queryDto.getCertificateCnEn())) {
			queryWrapper.eq(CtfCertificateType::getCertificateCnEn, queryDto.getCertificateCnEn());
		}
		
		if (StrUtil.isNotBlank(queryDto.getCertTypeDirId())) {
			queryWrapper.eq(CtfCertificateType::getCertTypeDirId, queryDto.getCertTypeDirId());
		}
		
		// 只查询未删除的记录
		queryWrapper.eq(CtfCertificateType::getDelFlag, "0");
		
		// 按创建时间降序排序
		queryWrapper.orderByDesc(CtfCertificateType::getCreateDate);
		
		return this.list(queryWrapper);
	}
}
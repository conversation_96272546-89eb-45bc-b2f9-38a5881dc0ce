package com.js.sys.init;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 系统启动初始化
 */
@Component
@Order(1)
public class SysStartRunner implements CommandLineRunner {
    
    private static final Logger log = LoggerFactory.getLogger(SysStartRunner.class);

    @Override
    public void run(String... args) {
        log.info("【sys服务】初始化被禁用，跳过加载公共数据...");
        // 不执行任何初始化操作
    }
}
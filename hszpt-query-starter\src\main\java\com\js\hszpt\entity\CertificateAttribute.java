package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("CTF_CERTIFICATE_ATTRIBUTE")
@ApiModel(value = "证照照面属性信息表")
public class CertificateAttribute {

    // 主键
    @TableId
    private String certificateAttributeId;

    // 关联到证照基本信息表的ID主键
    private String certificateId;

    // 属性名称：例如 结束时间(中文)
    private String attributeName;

    // 属性字段名称：例如：vaildDate
    private String attributeColumnName;

    // 属性值
    private String attributeValue;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 创建时间
    private Date createDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 修改时间
    private Date modifyDate;

}

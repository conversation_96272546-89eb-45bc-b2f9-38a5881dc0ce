package com.js.hszpt.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.service.SysDeptIambService;
import com.js.hszpt.utils.ZptUtil;
import com.js.hszpt.vo.SysDeptInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.js.core.common.vo.Result;

import java.util.List;

@RestController
@RequestMapping("/sysDept")
@Slf4j
public class SysDeptIambApi {

    @Autowired
    private SysDeptIambService sysDeptIambService;

    /**
     * 获取机构树形状接口
     *
     * @param orgCode 机构编码
     * @param flag 1 查本级 ， 2查下级
     * @return
     */
    @GetMapping("/deptSon")
    public Result<List<SysDeptInfoResponse>> getSysDept(@RequestParam("orgCode") String orgCode, @RequestParam("flag") String flag) {
        // 处理orgCode
        String processedOrgCode = ZptUtil.processOrgCode(orgCode);
        log.info("原始orgCode: {}, 处理后orgCode: {}", orgCode, processedOrgCode);
        return Result.success(sysDeptIambService.deptSon(processedOrgCode, flag));
    }

    /**
     *
     * 根据机构code查询
     * @param code
     * @return
     */
    @GetMapping("/getDeptByCode/{code}")
    public Result<SysDeptIamb> getSysDeptByCode(@PathVariable String code) {
        // 处理orgCode
        String processedOrgCode = ZptUtil.processOrgCode(code);
        log.info("原始orgCode: {}, 处理后orgCode: {}", code, processedOrgCode);
        return Result.success(sysDeptIambService.getOne(new LambdaQueryWrapper<SysDeptIamb>().eq(SysDeptIamb::getCode, processedOrgCode).last("LIMIT 1")));
    }
}

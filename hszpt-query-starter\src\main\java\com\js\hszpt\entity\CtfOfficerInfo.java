package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CtfOfficerInfo   
 * @Description:TODO(官员信息表)   
 * @author:   System Generation 
 */
@Data

@TableName("ctf_officer_info")
@ApiModel(value = "官员信息表")
public class CtfOfficerInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String officerInfoId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String officerName;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String officialNameEn;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String sex;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String department;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String departmentEn;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String orgCode;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String position;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String positionEn;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String createOperId;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;


    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String modifyOperId;



    /**
    * 
    */
    @ApiModelProperty(value = "")
    private String delFlag;


}
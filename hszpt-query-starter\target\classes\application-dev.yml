# 配置文件加密key(加密的salt)
jasypt:
  encryptor:
    password: jsdp

server:
  port: 8285
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 数据源
  datasource:
    dynamic:
      primary: dzzzdwdz  # 设置主数据源名称
      strict: false
      datasource:        
        dzzzdwdz: # 数据中台-主题库
          url: *****************************************************************************************************************
          username: system
          password: system
          driverClassName: org.postgresql.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        dzzzdws: # 数据中台-专题库
          url: ***************************************************************************************************************
          username: system
          password: system
          driverClassName: org.postgresql.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        dzzzcert: # 正孚数据库
          url: *************************************************************************************************************
          username: system
          password: zE$HvDuK!e29h8s$dx
          driverClassName: org.postgresql.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      # Druid StatViewServlet配置
      druid:
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 100
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        #属性类型是字符串，通过别名的方式配置扩展插件，常用的插件有：监控统计用的filter:stat，日志用的filter:log4j，防御sql注入的filter:wall
        #filters: stat,wall,slf4j
        filters: stat,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        stat-view-servlet:
          # 默认true 内置监控页面首页/druid/index.html
          enabled: true
          url-pattern: /druid/*
          # 允许清空统计数据
          reset-enable: true
          login-username: jsdpdba
          login-password: ENC(slvfSjijmP+Qck0iX6OTVw==)
          # IP白名单 多个逗号分隔
          allow:
          # IP黑名单
          deny:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 去除druid配置
  jpa:
    show-sql: false
    # 自动生成表结构
    generate-ddl: false
    hibernate:
      ddl-auto: none
  # Redis
  redis:
    open: true #开启Redis配置
    standalone: true #是否是单机环境。默认单机
    host: **************
    #password: csp@redis@2023
    # 数据库索引 默认0
    database: 9
    port: 16379
    # 超时时间   3秒
    timeout: 3000
      #jedis:
      #pool:
      #max-active: 1000 #连接池最大连接数
      #max-wait: -1 #连接池最大阻塞等待时间(使用负值没有限制)
      #max-idle: 10 #连接池中的最大空闲连接
      #min-idle: 5 #连接池中的最小空闲连接
      #集群配置
      #cluster:
      #nodes: #集群模式ip
      #- **************:7001
      #- **************:7002
      #- **************:7003
      #- **************:7004
    #- **************:7005
    #- **************:7006
  # 定时任务
  quartz:
    # 完全禁用Quartz
    enabled: false
    auto-startup: false
  # 工作流
  activiti:
    check-process-definitions: false
    db-identity-used: true
    # 自动生成Activiti相关表 第一次生成后建议关闭提高运行速度
    database-schema-update: true
    history-level: full
    # 扩展配置
    jsdp:
      # 流程图字体 默认宋体
      activityFontName: Microsoft YaHei
      labelFontName: Microsoft YaHei
  # 文件大小上传配置
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
  # 定时任务多线程配置
  task:
    scheduling:
      pool:
        # 比定时任务个数大
        size: 5
      thread-name-prefix: task-scheduling-
      enabled: false  # 全局禁用定时任务

#启用-webmvc配置功能（单独服务必须启动）
jsdp.webmvc.config.default: true
#启用-服务端渗透拦截器功能
jsdp.interceptor.config.default: false
#启用-服务端跨域请求拦截器功能
jsdp.cors.config.default: false
#启用-服务端tomcat请求限制功能
jsdp.tomcat.config.default: true

jsdp:
  security:
    smProperties:
      #国密2私钥
      sm2PrivateKey: 442024eb61ff92a4119c53ca24b0f8d07ab738706010ee69cc70b95df6a67472
      #国密2公钥
      sm2PublicKey: 04EF46D28BDE3F34CCF5052191B378C0E63AAC216C59AFD6AA2BE5B8E8EBC3F9B9D88024EC1F359D7E413518ECC2E22D4DEB2DF54B9F1DCD25612CD02CD7F8DC1F
      #国密2私钥(B)
      sm2PrivateKeyB: bb4fa3835d570abba3dfb663a614d855fe50b6017b07f55100804f3cecb9d1de
      #国密2公钥(B)
      sm2PublicKeyB: 040a2e021a7547201ecc00ee3fb52e15877b08ca39a954bcfd800e0b11f7bc35b9c237a3c6314c1cba97171956d7f67480203e9f89d65985a5879c3b235caef986
      #国密4秘钥(秘钥明文通过国密2加密后字符串)
      sm4Key: 04dc4b315ce1755b5c2dbd0011f1d59bf879e79cc7dae042587138932173ee752412333a05be1f6f48bdda1c50e17216f37e5a2f2948b0b43f462a1dc49f586c752f25d9358765a8f60c59b73e3e699926fd6bd9c224c9c420e91cba68a71dba714ac07ca7386017924c50c26e7936f2c8
  token:
  #默认true-开启redis模式。
  #redis: false
  # Elasticsearch
  es:
    user-name:
    password:
    #password: ENC(ksa2E3KS4Mk4fxm/DSxfZG1qfpBm2gbq)
    uris:
      - 127.0.0.1:19200
  # 全局限流
  rateLimit:
    enable: true
    # 每1秒内
    timeout: 1000
    # 总限制100个请求
    limit: 100

  #SQL拦截器：多租户配置和数据权限配置
  sqlinterceptor:
    #配置不进行控制的mapper方法，即不进行多租户也不进行数据权限控制
    ignoreSqls:   #需要配置mapper的全路径如：com.yirong.csp.user.mapper.SysUserMapper.findList
    #- com.js.auth.dao.mapper.UserMapper.findByUsername
    #多租户配置
    tenantEnable: true #是否开启多租户
    #tenantIdColumn:    #多租户where 条件 字段
    tenantIgnoreTables:   #配置不进行多租户隔离的表名
    #数据权限配置
    dataAuthEnable: true #是否开启数据权限控制
    dataAuthIdColumn: current_merchant_id    #数据权限where 条件 字段
    dataAuthIgnoreTables:  #配置不进行数据权限控制的表名
    #- mall_region
#  微信小程序配置 appid /appsecret
wx:
  minimode: 0 #//小程序模式：0-miniapp 独立部署模式 ;1-open 开放平台模式 。默认 0-miniapp
  miniapp:
    configs:
      - appid:
        secret:
        token:
        aesKey:
        msgDataFormat: JSON

# Swagger界面内容配置
# Swagger界面内容配置
swagger:
  enabled: false
  title: 统一开发平台接口文档
  description: JSDP Api Documentation
  version: 1.9.0
  termsOfServiceUrl:
  contact:
    name: JS
    url:
    email:
  packages:
    - com.js.hszpt

# Mybatis-plus
mybatis-plus:
  # 放在resource目录 classpath:/mapper/*Mapper.xml
  #mapper-locations: classpath:/mapper/*Mapper.xml
  mapper-locations: classpath*:mapper/**/**.xml
  global-config:
    # 主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 2
    # 字段策略 0:"忽略判断",1:"非 NULL 判断",2:"非空判断"
    field-strategy: 2
    # 驼峰下划线转换
    db-column-underline: true
    # 刷新mapper 调试神器
    refresh-mapper: true
    # SQL 解析缓存，开启后多租户 @SqlParser 注解生效
    sql-parser-cache: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    # 配置JdbcTypeForNull, oracle数据库必须配置
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志
logging:
  #指定日志文件
  config: classpath:jsdp-logback-spring.xml
  file:
    #单个日志文件最大值
    max-size: 200MB
    #日志文件最多保留的天数
    max-history: 60
    #日志文件总体的最大值
    total-size-cap: 10GB
    #日志文件路径
    path: ./logs

# 请求子平台数据服务接口
zpt:
  # 模拟数据
  mock: false
  # 请求地址
  url: https://mjtest.jingsaitech.com/hszpt-etl-api/dataEtl
  urlOracle: http://hszptCertNginx:8284/dataEtl
  # 保存文件目录
  filePath: /tmp/hszpt/
  # 定时任务执行配置 taskName - 任务名称 enable - 是否开启 cron - 任务执行时间
  dataReceiveJob: [
    # 所有任务都设置为false
    { taskName: "biz_affair_apply", enable: false, cron: "0 */1 * * * ?" },
    # 保险表
    { taskName: "biz_affair_insurance", enable: false, cron: "0 */1 * * * ?" },
    # 船舶油污损害民事责任保险或其他财务保证证书核发
    { taskName: "biz_dgr_opic", enable: false, cron: "0 */1 * * * ?" },
    # 审批意见表
    { taskName: "biz_eg_approve", enable: false, cron: "0 */1 * * * ?" },
    # 制证校核表
    { taskName: "biz_eg_cert_check", enable: false, cron: "0 */1 * * * ?" },
    # 证书发放
    { taskName: "biz_eg_send_cert", enable: false, cron: "0 */1 * * * ?" },
    # 海事机构编码
    { taskName: "dict_eg_org", enable: false, cron: "0 */1 * * * ?" },
    # 港口信息
    { taskName: "dict_eg_port", enable: false, cron: "0 */1 * * * ?" },
    # 公共字典表
    { taskName: "dict_eg_public", enable: false, cron: "0 0 3 * * ?" },
    # 人员用户信息
    { taskName: "dict_eg_user", enable: false, cron: "0 */1 * * * ?" },
    # 申请受理审批信息
    { taskName: "biz_eg_apply_info", enable: false, cron: "0 */1 * * * ?" },
    # 船舶基本信息
    { taskName: "ship_info_all", enable: false, cron: "0 */1 * * * ?" },
    # 船舶证书-所有权证书
    { taskName: "cert_ship_ownership", enable: false, cron: "0 */1 * * * ?" },
    # 受理信息
    { taskName: "biz_eg_accept", enable: false, cron: "0 */1 * * * ?" },
    # 签证官信息
    { taskName: "officer_info", enable: false, cron: "0 */1 * * * ?" },
  ]
# 电子证照
certificate:
  # 电子证照接口IP+PORT
  url-permit-host: http://*************:8081
  # 证书生成接口地址
  chromatography-printing-url: http://*************:8081/certificateDataService/fileLicenseUpload
  # 证书查看地址
  checkChromatographyPrintingUrl: http://*************:8081/certificateDataService/fileLicenseUpload
  # 账号ID
  accountId: 1000000000000000000
  # 身份认证信息
  accessToken: 1000000000000000000

#电子证照扫码核验，加密配置
qrcode:
  # 生成二维码的接口请求地址
  scanUrl: https://mjtest.jingsaitech.com/dzzz-prod-api/certificate/cert
  # 扫码后重定向跳转电子证照查询地址
  redirectUrl: https://mjtest.jingsaitech.com/dzzz-web/#/certQuery
  security:
    #私钥配置
    privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDJj894nRSNjAf+a4zT7jJG2zEtGm71Dk1HpV4bS60VyKry977v18iyj55dyRAGVIVOScK2xy9DF+2NQwvZS8gkS+6ut8KkrduAAZHshr0fGoLSlMgT4Ieu6s7o2tPa5TX7dZG+X1wkr+YndfRXiQx2PoKvYMpUteP1kxcNxupgB2awHcmpAE/2VrY57VabEJOoz62TIfgADxLYI9XuaJDevoyEy395pQzBJJd51O/gmuwYzxFzdWVT5tapVbohBAyW1XmbVUy9k8VVZMv2i/gfmeJh8TfpmE8ocG+bl8C5a7c5J5rhaWpRX8VkHHIkRE9+ZQWgsh7xj7+mXuPtJlYDAgMBAAECggEAK7c7Ike/W2vRmAavho+U6/VCVyvygMfXTwC4xmbFZYIeQZAmI1LDvqaTjHe9OAJA31f0GTxeLckUxpySB1D+WZD2dYJ5fsoOX1b31Trr7cEMOqv2MQlzpCZdNfewFzu7V4yKTaXVEEfuPQTCN/ILd+Ha6jqt522DwZFjYKOR1b2iIlPana2YVtAe5MNpjR4xW/qonYNmumFgd9J2xq2NCzv/JzJmXi1G8Jj+QyZeh9eoamRqFVtUwMrTBnDG8iaq46IDQ3s8fLOuoEq1B+upWYR1fV7hZ4jAhx5zL4Wt8+2ngbwYlHrFO+6zIkZwsPjACmLe5smth0occxoXH5SPQQKBgQD732nVbxlEzgQ6cLQEq4VhjalJyl7teh8kKGTZWSEXndUSWg6hK0LTfzzW9ALICgDtv1XactN08gonPq347Dd1FpRhFTUD22oSAhdKh057GPWN451XqrYCLSKLJrp7SOV8YToZGbXWjBtb1SR19Lpyy4xFPkd86hgtboWs8yoUswKBgQDM3ViyPpTYACaUVhiJEvTxu9aqPOSJP5YwcF5eNwr2sy/2hn2o4H2UUgH+6IGw25GrCE26qacfv1f83+gZVR3oeGvojaU89i/6723UF00GyMwv3QUMZlLvzFDEwjizmSmz2TIVvkaVV8Pe7sqwhDvJaDHzryqeQ/0RrIvWhMeBcQKBgA5HDNXHdXQ8BWtWpi25l1b/U8BWE2l8ybUgAQ99CO4wyTpAFqI2NJSOmuFIdbvnLURM204FwkzXx5GP8QayRTaC8ChMrnOohWu96JoV73H06T6Az5N/ns+ixVmD+YAxkqipTkTL+03NJWPgQi/ZjpQhS7NFI4JGNtakdDlok1MXAoGAY+lHipaOzNE1/34FtUQzhXlQdfIAmxcRzknfHB/IDwzQxXBN9ICXfjCInKMULQbIghsonXKD+S2+YatpbIFgdrHUHrSF87c1KzJ948QBYt+nBWjLP4Lfy+dfNLZsJNr7xaSVkpkbn1YQRrc6zt+OsOlKA0hYhpst3uLByDs1eWECgYEAnwgjcV337Lf0bBLNeY3pzInCN82USKMfd7qR8KVcAWTatkC9ovW4FJ/cWRwVQYPJyxm2ZNVO65TaQElKcW9xlUi+esx9mp3go59R9CfnDitMnha/NG7Cb7m4LhBvRvNUtbDCJjj0Uc+wYRn7UtyUjYSxorgt8YGpAEPkwANIa3E=
    sm2PublicKey: 0404f819104243bcc106f9b37a7bf3b75776616e008f312466b6515ebc3914a339cb62b4eff9f6118ccf6f868a9d0818bbd20b53fed9ea3a8c9ca457448e550e03
    sm4SecretKey: 5d3b282609644b4f8992b31a2e92f0f3

# 智慧海事登录验证接口
api:
  login-check:
    url: http://***********:8090/main/getLoginUser

package com.js.hszpt.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.js.core.common.utils.PageUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.SearchVo;
import com.js.hszpt.dto.AffairDropdownDTO;
import com.js.hszpt.entity.CtfAffair;
import com.js.hszpt.mapper.CtfAffairDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
 
/**
 * 
 * @ClassName:  AffairService    
 * @Description:TODO(事项表接口实现)   
 * @author:  System Generation
 *
 */
@Slf4j
@Service
public class CtfAffairService extends ServiceImpl<CtfAffairDao, CtfAffair> {
	/**
	 * 多条件分页查询
	 * @param param
	 * @param searchVo
	 * @param pageVo
	 * @return
	 */
	public Page<CtfAffair> findByCondition(CtfAffair param, SearchVo searchVo, PageVo pageVo) {
		Page<CtfAffair> page = PageUtil.initMpPage(pageVo);
		QueryWrapper<CtfAffair> queryWrapper = this.getCondition(param,searchVo);
		return this.page(page, queryWrapper);
	}
	
	/**
	 * 
	 * @Title: findByCondition   
	 * @Description: TODO(多条件列表查询)   
	 * @param param
	 * @param searchVo
	 * @return  List<Affair>      
	 * @throws
	 */
	public List<CtfAffair> findByCondition(CtfAffair param, SearchVo searchVo){
		QueryWrapper<CtfAffair> queryWrapper = this.getCondition(param, searchVo);
		//添加排序字段
		queryWrapper.orderByAsc("create_time");
		
		return this.list(queryWrapper);
	}
	
	
	/**
	 * 
	 * @Title: getCondition   
	 * @Description: TODO(构建自定义查询条件)   
	 * @param param
	 * @param searchVo
	 * @return  QueryWrapper<Affair>      
	 * @throws
	 */
	private QueryWrapper<CtfAffair> getCondition(CtfAffair param, SearchVo searchVo){
		QueryWrapper<CtfAffair> queryWrapper = new QueryWrapper<CtfAffair>();
		//请根据实际字段自行扩展
		 
		// 创建时间
		if (StrUtil.isNotBlank(searchVo.getStartDate()) && StrUtil.isNotBlank(searchVo.getEndDate())) {
			Date start = DateUtil.parse(searchVo.getStartDate());
			Date end = DateUtil.parse(searchVo.getEndDate());
			queryWrapper.between("create_time",start, DateUtil.endOfDay(end));
		}
		return queryWrapper;
		
	}

	/**
	 * 获取事项下拉列表数据
	 * @return 事项名称和编号列表
	 */
    public List<AffairDropdownDTO> getAffairDropdownList() {
        QueryWrapper<CtfAffair> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("AFFAIR_NAME", "AFFAIR_NUM");
        
        return this.list(queryWrapper).stream()
            .map(affair -> {
                AffairDropdownDTO dto = new AffairDropdownDTO();
                dto.setLabel(affair.getAffairName());
                dto.setValue(affair.getAffairNum());
                return dto;
            })
            .collect(Collectors.toList());
    }
}
package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BizDgrOpic {

    // 保险证书业务标识
    @TableId
    private String certInsurId;

    // 业务申请标识
    private String applyId;

    // 申请单号 电子政务系统定义的申请单编号。15位固定长度编码，由YYYYMMDD+7位顺序号构成。
    private String applyNo;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 申请时间
    private Date applyDate;

    // 申请状态 0：待提交；1：已提交；2：已接收；3：退回修改；5：转发下属机构
    private Integer applyStatus;

    // 提交海事机构代码 DICT_EG_ORG
    private String orgCode;

    // 申请人类型
    private String applicantType;

    // 申请人标识
    private String applicantId;

    // 申请人名称
    private String applicantName;

    // 申请人地址
    private String applicantAddr;

    // 保险证书名称
    private String insurCertName;

    // 联系人
    private String contactName;

    // 联系电话
    private String contactPhone;

    // 中文船名
    private String shipName;

    // 英文船名
    private String shipNameEn;

    // 船舶识别号 永久识别中国籍船舶的唯一编码。由英文字母CN和11位阿拉伯数字构成。CN代表中国，11位阿拉伯数字的前4位表示船舶安放龙骨的年份，第5至10位是随机编号，第11位是校验码。
    private String shipId;

    // 船舶登记号 船舶管理部门在办理船舶所有权登记时授予的所有权登记号码。由P(表示临时证书号，可选)+4位发证机构编码＋2位年＋6位顺序号构成。
    private String shipRegNo;

    // 初次登记号 船舶初次办理船舶所有权登记时，由船舶管理部门授予的所有权登记号码。由P(表示临时证书号，可选)+4位发证机构编码＋2位年＋6位流水号构成。
    private String shipFirstregNo;

    // 船检登记号 由海事局授予的船舶在全国范围内唯一的、始终不变的法定代码标识。由船舶建造完工4位年份＋1位计算机纠错码＋2位中国船级社及各省(自治区\直辖市)船检局(处)的编码＋5位船检登记流水号构成。
    private String shipInspectNo;

    // IMO编号 按照国际海事组织A.600决议授予100总吨及以上自航海船的编号。
    private String shipImo;

    // 牌簿号 对于没有IMO编号的港澳小型船舶，海事备案时需要提供牌簿号作为唯一识别，船舶牌簿相当于是船只的合格证，上面记录着船舶的尺寸、功率、载重、设备等全部数据。由4位年度+8位数字与字母的随机数构成。
    private String shipCardNo;

    // MMSI编号 海上移动通信业务标识，船舶无线电通信系统在其无线电信道上发送的，能独特识别各类台站和成组呼叫台站的一列9位数字码。
    private String shipMmsi;

    // 船舶呼号 国际海事组织用于对船舶进行呼叫的代号。是主管机关授予每一个通讯站点（船舶、飞行器、救生工具等）的一组字母和数字。通常有4个至5个字符，中国籍的船舶第1个字母是B，由无线电委员会授予。
    private String shipCallsign;

    // 国籍
    private String nationality;

    // 船籍港名称
    private String regportName;

    // 船舶种类
    private String shipTypeName;

    // 海船内河船标志
    private String shipRegionType;

    // 船舶航线类型代码 1：国内航线；2：国际/港澳航线
    private String shipRouteCode;

    // 航区
    private String naviAreaName;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 建造完工日期
    private Date shipBuiltDate;

    // 船舶总吨
    private BigDecimal shipGrosston;

    // 船舶净吨
    private BigDecimal shipNetton;

    // 载重吨
    private BigDecimal shipDwt;

    // 主机功率
    private BigDecimal shipEnginePower;

    // 船舶总长
    private BigDecimal shipLength;

    // 船舶型宽
    private BigDecimal shipBreadth;

    // 船舶型深
    private BigDecimal shipDepth;

    // 船舶管理人
    private String shipManager;

    // 船舶经营人
    private String shipOperator;

    // 船舶所有人
    private String shipOwner;

    // 船舶所有人(英文)
    private String shipOwnerEn;

    // 船舶所有人地址
    private String shipOwnerAddr;

    // 船舶所有人地址(英文)
    private String shipOwnerAddrEn;

    // 保单编号
    private String policyNo;

    // 保单时间计量系统 1：北京时间；2：格林威治时间
    private String policyTimeCode;

    // 保单生效日期
    private String policyEffDate;

    // 保单生效时间
    private String policyEffTime;

    // 保单失效日期
    private String policyExpDate;

    // 保单失效时间
    private String policyExpTime;

    // 保险机构名称
    private String insurCompName;

    // 保险机构英文名称
    private String insurCompNameEn;

    // 保险机构地址
    private String insurCompAddr;

    // 保险机构英文地址
    private String insurCompAddrEn;

    // 备注
    private String remark;

    // 创建人标识 DICT_EG_USER
    private String creatorId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 创建日期
    private Date createDate;

    // 创建人所属机构代码
    private String creatorOrgCode;

    // 修改人标识 DICT_EG_USER
    private String modifierId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 修改日期
    private Date modifyDate;

    // 源系统代码
    private String sourceCode;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 记录创建日期
    private Date recCreateDate;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    // 记录修改日期
    private Date recModifyDate;

    // 数据归属机构代码 DICT_EG_ORG
    private String msaOrgCode;
}

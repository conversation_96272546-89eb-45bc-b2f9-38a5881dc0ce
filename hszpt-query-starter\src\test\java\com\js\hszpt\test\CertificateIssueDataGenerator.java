package com.js.hszpt.test;

import java.security.SecureRandom;
import java.sql.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 证照签发数据生成器
 */
public class CertificateIssueDataGenerator {
    // 数据库连接信息
    private static final String URL = "******************************************************************************************************";
    private static final String USERNAME = "system";
    private static final String PASSWORD = "system";

    public static void main(String[] args) {
        try {
            Class.forName("org.postgresql.Driver");
            generateIssueTestData(); // 生成签发数据
            generateUsageTestData(); // 生成使用数据
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成证照签发测试数据
     */
    private static void generateIssueTestData() throws SQLException {
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 查询签发机构编码和名称(树状结构)
            List<Map<String, String>> deptList = queryDeptTree(conn);

            // 2. 查询证照类型编码和名称(树状结构)
            List<Map<String, String>> certTypeList = queryCertTypeTree(conn);

            // 3. 查询字典数据(编码和名称)
            Map<String, List<Map<String, String>>> dictMaps = new HashMap<>();
            dictMaps.put("CERTIFICATE_HOLDER_CATEGORY", queryDictItems(conn, "CERTIFICATE_HOLDER_CATEGORY"));
            dictMaps.put("affair_type", queryDictItems(conn, "affair_type"));
            dictMaps.put("apply_type", queryDictItems(conn, "apply_type"));
            dictMaps.put("certificate_status", queryDictItems(conn, "certificate_status"));

            // 生成1000条测试数据
            List<Object[]> batchArgs = new ArrayList<>();
            // 使用密码学安全的随机数生成器
            SecureRandom random = new SecureRandom();

            // 生成过去两年的数据
            LocalDate startDate = LocalDate.now().minusYears(2);
            LocalDate endDate = LocalDate.now();

            // 生成一些随机的证照代码和名称
            String[] certificateCodes = generateRandomCertificateCodes(50);
            String[] certificateNames = generateRandomCertificateNames(50);
            String[] ctfAffairIds = generateRandomAffairIds(20);
            String[] ctfAffairNames = generateRandomAffairNames(20);

            for (int i = 0; i < 1000; i++) {
                // 生成随机日期
                long startEpochDay = startDate.toEpochDay();
                long endEpochDay = endDate.toEpochDay();
                long randomDay = startEpochDay + random.nextInt((int) (endEpochDay - startEpochDay));
                LocalDate randomDate = LocalDate.ofEpochDay(randomDay);

                // 格式化日期为yyyyMMdd
                String dsDate = randomDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                // 随机选择一个部门(保持父子关系)
                Map<String, String> dept = selectRandomDeptWithParent(deptList);
                String issuerCode = dept.get("code");
                String issuerName = dept.get("name");

                // 随机选择一个证照类型(保持父子关系)
                Map<String, String> certType = selectRandomCertTypeWithParent(certTypeList);
                String certTypeCode = certType.get("cert_type_code");
                String certTypeName = certType.get("cert_type_name");

                // 随机选择持证主体类别
                Map<String, String> holderCategory = getRandomDictItem(dictMaps.get("CERTIFICATE_HOLDER_CATEGORY"));
                String holderCategoryCode = holderCategory.get("value");
                String holderCategoryName = holderCategory.get("label");

                // 随机选择事项性质
                Map<String, String> matterNature = getRandomDictItem(dictMaps.get("affair_type"));
                String matterNatureCode = matterNature.get("value");
                String matterNatureName = matterNature.get("label");

                // 随机选择申请来源
                Map<String, String> applicationSource = getRandomDictItem(dictMaps.get("apply_type"));
                String applicationSourceCode = applicationSource.get("value");
                String applicationSourceName = applicationSource.get("label");

                // 随机选择证照状态
                Map<String, String> status = getRandomDictItem(dictMaps.get("certificate_status"));
                String statusCode = status.get("value");
                String statusName = status.get("label");

                // 随机选择证照代码和名称
                String certificateCode = certificateCodes[random.nextInt(certificateCodes.length)];
                String certificateName = certificateNames[random.nextInt(certificateNames.length)];

                // 随机选择事项ID和名称（有10%的概率为空）
                String ctfAffairId = null;
                String ctfAffairName = null;
                if (random.nextDouble() > 0.1) {
                    ctfAffairId = ctfAffairIds[random.nextInt(ctfAffairIds.length)];
                    ctfAffairName = ctfAffairNames[random.nextInt(ctfAffairNames.length)];
                }

                // 生成UUID
                String issueId = UUID.randomUUID().toString().replace("-", "");

                // 随机生成签发数量(1-100)
                int issueCount = random.nextInt(100) + 1;

                // 当前时间戳
                Timestamp now = new Timestamp(System.currentTimeMillis());

                // 添加到批处理参数
                batchArgs.add(new Object[] {
                        issueId, dsDate, issuerCode, certTypeCode,
                        holderCategoryCode, matterNatureCode, applicationSourceCode, statusCode,
                        certificateCode, ctfAffairId, certificateName, ctfAffairName,
                        certTypeName, issuerName, holderCategoryName, matterNatureName,
                        applicationSourceName, statusName, issueCount, now
                });
            }

            // 批量插入数据
            insertIssueBatch(conn, batchArgs);

            System.out.println("成功生成1000条证照签发测试数据");
        }
    }

    /**
     * 生成证照使用测试数据
     */
    private static void generateUsageTestData() throws SQLException {
        try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
            // 1. 查询签发机构编码和名称(树状结构)
            List<Map<String, String>> deptList = queryDeptTree(conn);

            // 2. 查询证照类型编码和名称(树状结构)
            List<Map<String, String>> certTypeList = queryCertTypeTree(conn);

            // 3. 查询字典数据(编码和名称)
            Map<String, List<Map<String, String>>> dictMaps = new HashMap<>();
            dictMaps.put("CERTIFICATE_HOLDER_CATEGORY", queryDictItems(conn, "CERTIFICATE_HOLDER_CATEGORY"));
            dictMaps.put("affair_type", queryDictItems(conn, "affair_type"));
            dictMaps.put("apply_type", queryDictItems(conn, "apply_type"));
            dictMaps.put("certificate_status", queryDictItems(conn, "certificate_status"));

            // 生成1000条测试数据
            List<Object[]> batchArgs = new ArrayList<>();
            // 使用密码学安全的随机数生成器
            SecureRandom random = new SecureRandom();

            // 生成过去两年的数据
            LocalDate startDate = LocalDate.now().minusYears(2);
            LocalDate endDate = LocalDate.now();

            // 生成一些随机的证照代码和名称
            String[] certificateCodes = generateRandomCertificateCodes(50);
            String[] certificateNames = generateRandomCertificateNames(50);
            String[] ctfAffairIds = generateRandomAffairIds(20);
            String[] ctfAffairNames = generateRandomAffairNames(20);

            for (int i = 0; i < 1000; i++) {
                // 生成随机日期
                long startEpochDay = startDate.toEpochDay();
                long endEpochDay = endDate.toEpochDay();
                long randomDay = startEpochDay + random.nextInt((int) (endEpochDay - startEpochDay));
                LocalDate randomDate = LocalDate.ofEpochDay(randomDay);

                // 格式化日期为yyyyMMdd
                String dsDate = randomDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

                // 随机选择一个部门(保持父子关系)
                Map<String, String> dept = selectRandomDeptWithParent(deptList);
                String issuerCode = dept.get("code");
                String issuerName = dept.get("name");

                // 随机选择一个证照类型(保持父子关系)
                Map<String, String> certType = selectRandomCertTypeWithParent(certTypeList);
                String certTypeCode = certType.get("cert_type_code");
                String certTypeName = certType.get("cert_type_name");

                // 随机选择持证主体类别
                Map<String, String> holderCategory = getRandomDictItem(dictMaps.get("CERTIFICATE_HOLDER_CATEGORY"));
                String holderCategoryCode = holderCategory.get("value");
                String holderCategoryName = holderCategory.get("label");

                // 随机选择事项性质
                Map<String, String> matterNature = getRandomDictItem(dictMaps.get("affair_type"));
                String matterNatureCode = matterNature.get("value");
                String matterNatureName = matterNature.get("label");

                // 随机选择申请来源
                Map<String, String> applicationSource = getRandomDictItem(dictMaps.get("apply_type"));
                String applicationSourceCode = applicationSource.get("value");
                String applicationSourceName = applicationSource.get("label");

                // 随机选择证照状态
                Map<String, String> status = getRandomDictItem(dictMaps.get("certificate_status"));
                String statusCode = status.get("value");
                String statusName = status.get("label");

                // 随机选择证照代码和名称
                String certificateCode = certificateCodes[random.nextInt(certificateCodes.length)];
                String certificateName = certificateNames[random.nextInt(certificateNames.length)];

                // 随机选择事项ID和名称（有10%的概率为空）
                String ctfAffairId = null;
                String ctfAffairName = null;
                if (random.nextDouble() > 0.1) {
                    ctfAffairId = ctfAffairIds[random.nextInt(ctfAffairIds.length)];
                    ctfAffairName = ctfAffairNames[random.nextInt(ctfAffairNames.length)];
                }

                // 生成UUID
                String usageId = UUID.randomUUID().toString().replace("-", "");

                // 随机生成使用数量(1-50)
                int usageCount = random.nextInt(50) + 1;

                // 使用类型：1-被下载 2-被核验
                String usageType = random.nextBoolean() ? "1" : "2";

                // 当前时间戳
                Timestamp now = new Timestamp(System.currentTimeMillis());

                // 添加到批处理参数
                batchArgs.add(new Object[] {
                        usageId, dsDate, issuerCode, certTypeCode,
                        holderCategoryCode, matterNatureCode, applicationSourceCode, statusCode,
                        certificateCode, ctfAffairId, certificateName, ctfAffairName,
                        certTypeName, issuerName, holderCategoryName, matterNatureName,
                        applicationSourceName, statusName, usageCount, usageType, now
                });
            }

            // 批量插入数据
            insertUsageBatch(conn, batchArgs);

            System.out.println("成功生成1000条证照使用测试数据");
        }
    }

    // 查询部门树 - 使用人大金仓支持的Oracle风格CONNECT BY语法
    private static List<Map<String, String>> queryDeptTree(Connection conn) throws SQLException {
        List<Map<String, String>> result = new ArrayList<>();
        String sql = "SELECT dept_id, code, name, parent_id, LEVEL " +
                "FROM dws_ctf_sys_dept " +
                "START WITH parent_id = '-1' " +
                "CONNECT BY PRIOR dept_id = parent_id " +
                "ORDER BY LEVEL, dept_id";

        try (Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                Map<String, String> map = new HashMap<>();
                map.put("dept_id", rs.getString("dept_id"));
                map.put("code", rs.getString("code"));
                map.put("name", rs.getString("name"));
                map.put("parent_id", rs.getString("parent_id"));
                map.put("level", rs.getString("LEVEL"));
                result.add(map);
            }
        }
        return result;
    }

    // 查询证照类型树 - 使用人大金仓支持的Oracle风格CONNECT BY语法
    private static List<Map<String, String>> queryCertTypeTree(Connection conn) throws SQLException {
        List<Map<String, String>> result = new ArrayList<>();
        String sql = "SELECT CERTIFICATE_TYPE_CODE, CERTIFICATE_TYPE_NAME, parent_id, CERT_TYPE_DIR_ID, LEVEL " +
                "FROM dws_ctf_cert_type_directory " +
                "START WITH parent_id = '-1' " +
                "CONNECT BY PRIOR CERT_TYPE_DIR_ID = parent_id " +
                "ORDER BY LEVEL, CERTIFICATE_TYPE_CODE";

        try (Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                Map<String, String> map = new HashMap<>();
                map.put("cert_type_code", rs.getString("CERTIFICATE_TYPE_CODE"));
                map.put("cert_type_name", rs.getString("CERTIFICATE_TYPE_NAME"));
                map.put("parent_id", rs.getString("parent_id"));
                map.put("cert_type_dir_id", rs.getString("CERT_TYPE_DIR_ID"));
                map.put("level", rs.getString("LEVEL"));
                result.add(map);
            }
        }
        return result;
    }

    // 查询字典数据的编码和名称
    private static List<Map<String, String>> queryDictItems(Connection conn, String dictType) throws SQLException {
        List<Map<String, String>> result = new ArrayList<>();
        String sql = "SELECT b.value, b.title FROM dws_ctf_dict a, dws_ctf_dict_data b " +
                "WHERE a.type = ? AND a.id = b.dict_id";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, dictType);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Map<String, String> map = new HashMap<>();
                    map.put("value", rs.getString("value"));
                    map.put("label", rs.getString("title"));
                    result.add(map);
                }
            }
        }
        return result;
    }

    // 随机选择部门(保持父子关系)
    private static Map<String, String> selectRandomDeptWithParent(List<Map<String, String>> deptList) {
        if (deptList == null || deptList.isEmpty()) {
            return new HashMap<>();
        }

        // 按层级分组
        Map<String, List<Map<String, String>>> levelGroups = new HashMap<>();
        for (Map<String, String> dept : deptList) {
            String level = dept.get("level");
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(dept);
        }

        // 随机选择一个层级(倾向于选择子节点)
        // 使用密码学安全的随机数生成器
        SecureRandom random = new SecureRandom();
        String[] levels = levelGroups.keySet().toArray(new String[0]);
        String selectedLevel = levels[random.nextInt(levels.length)];

        // 从选中的层级随机选择一个部门
        List<Map<String, String>> depts = levelGroups.get(selectedLevel);
        return depts.get(random.nextInt(depts.size()));
    }

    // 随机选择证照类型(保持父子关系)
    private static Map<String, String> selectRandomCertTypeWithParent(List<Map<String, String>> certTypeList) {
        if (certTypeList == null || certTypeList.isEmpty()) {
            return new HashMap<>();
        }

        // 按层级分组
        Map<String, List<Map<String, String>>> levelGroups = new HashMap<>();
        for (Map<String, String> certType : certTypeList) {
            String level = certType.get("level");
            levelGroups.computeIfAbsent(level, k -> new ArrayList<>()).add(certType);
        }

        // 随机选择一个层级(倾向于选择子节点)
        // 使用密码学安全的随机数生成器
        SecureRandom random = new SecureRandom();
        String[] levels = levelGroups.keySet().toArray(new String[0]);
        String selectedLevel = levels[random.nextInt(levels.length)];

        // 从选中的层级随机选择一个证照类型
        List<Map<String, String>> certTypes = levelGroups.get(selectedLevel);
        return certTypes.get(random.nextInt(certTypes.size()));
    }

    // 从字典项列表中随机选择一项
    private static Map<String, String> getRandomDictItem(List<Map<String, String>> dictItems) {
        if (dictItems == null || dictItems.isEmpty()) {
            // 返回默认值
            Map<String, String> defaultMap = new HashMap<>();
            defaultMap.put("value", "0");
            defaultMap.put("label", "默认值");
            return defaultMap;
        }
        // 使用密码学安全的随机数生成器
        return dictItems.get(new SecureRandom().nextInt(dictItems.size()));
    }

    // 随机生成证照代码
    private static String[] generateRandomCertificateCodes(int count) {
        String[] codes = new String[count];
        for (int i = 0; i < count; i++) {
            codes[i] = "CTF-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        }
        return codes;
    }

    // 随机生成证照名称
    private static String[] generateRandomCertificateNames(int count) {
        String[] prefixes = { "企业营业", "个体工商户", "建筑施工", "食品经营", "特种设备", "危险品", "道路运输", "医疗器械" };
        String[] types = { "许可证", "资格证", "执业证", "注册证", "登记证", "经营证", "安全证", "生产证" };

        String[] names = new String[count];
        // 使用密码学安全的随机数生成器
        SecureRandom random = new SecureRandom();

        for (int i = 0; i < count; i++) {
            String prefix = prefixes[random.nextInt(prefixes.length)];
            String type = types[random.nextInt(types.length)];
            names[i] = prefix + type;
        }

        return names;
    }

    // 随机生成事项ID
    private static String[] generateRandomAffairIds(int count) {
        String[] ids = new String[count];
        for (int i = 0; i < count; i++) {
            ids[i] = "AFFAIR-" + UUID.randomUUID().toString().substring(0, 8);
        }
        return ids;
    }

    // 随机生成事项名称
    private static String[] generateRandomAffairNames(int count) {
        String[] prefixes = { "企业", "个体", "建筑", "食品", "特种", "危险品", "道路", "医疗" };
        String[] actions = { "申请", "办理", "审批", "变更", "延期", "注销", "补办", "年检" };
        String[] types = { "许可证", "资格证", "执业证", "注册证", "登记证", "经营证", "安全证", "生产证" };

        String[] names = new String[count];
        // 使用密码学安全的随机数生成器
        SecureRandom random = new SecureRandom();

        for (int i = 0; i < count; i++) {
            String prefix = prefixes[random.nextInt(prefixes.length)];
            String action = actions[random.nextInt(actions.length)];
            String type = types[random.nextInt(types.length)];
            names[i] = prefix + type + action;
        }

        return names;
    }

    // 批量插入签发数据
    private static void insertIssueBatch(Connection conn, List<Object[]> batchArgs) throws SQLException {
        String sql = "INSERT INTO dws_ctf_certificate_issue (" +
                "issue_id, ds_certificate_date, issuer_code, certificate_type_code, " +
                "holder_category_code, matter_nature_code, application_source_code, certificate_status_code, " +
                "certificate_code, ctf_affair_id, certificate_name, ctf_affair_name, " +
                "certificate_type_name, issuer_name, holder_category_name, matter_nature_name, " +
                "application_source_name, certificate_status_name, issue_count, create_date) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            for (Object[] args : batchArgs) {
                for (int i = 0; i < args.length; i++) {
                    pstmt.setObject(i + 1, args[i]);
                }
                pstmt.addBatch();
            }
            pstmt.executeBatch();
        }
    }

    // 批量插入使用数据
    private static void insertUsageBatch(Connection conn, List<Object[]> batchArgs) throws SQLException {
        String sql = "INSERT INTO dws_ctf_certificate_uasge (" +
                "uasge_id, ds_certificate_date, issuer_code, certificate_type_code, " +
                "holder_category_code, matter_nature_code, application_source_code, certificate_status_code, " +
                "certificate_code, ctf_affair_id, certificate_name, ctf_affair_name, " +
                "certificate_type_name, issuer_name, holder_category_name, matter_nature_name, " +
                "application_source_name, certificate_status_name, uasge_count, uasge_type, create_date) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            for (Object[] args : batchArgs) {
                for (int i = 0; i < args.length; i++) {
                    pstmt.setObject(i + 1, args[i]);
                }
                pstmt.addBatch();
            }
            pstmt.executeBatch();
        }
    }
}
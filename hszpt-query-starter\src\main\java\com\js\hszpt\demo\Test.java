package com.js.hszpt.demo;
import java.util.*;//工具类包
import java.text.*;
import java.util.regex.*;//正则类的jar包
public class Test {
//假设这是小狗的一个类
    private int age;
    private String name;
    //添加一个构造器
    public Test (String name) {
        this.name = name;
    }
    // 设置 age 的值
    public void setAge(int age) {
        this.age = age;
    }

    // 获取 age 的值
    public int getAge() {
        return age;
    }

    // 获取 name 的值
    public String getName() {
        return name;
    }
    public static void main(String[] args) {
//      实例化对象  后面就可以调用 对应的方法和设置属性内容
        Test myDog = new Test("小黑");
        myDog.setAge(10);
        int age = myDog.getAge();
        String name=myDog.getName();
        System.out.println("小狗的名字是 : " +  name+ " " + age);
        System.out.println("Hello World"); // 输出 Hello World

//       应用程序要求线程安全的情况下，则必须使用 StringBuffer 类。
        StringBuilder sb = new StringBuilder(10);
        sb.append("Runoob..");
        System.out.println(sb);
        sb.append("!");
        System.out.println(sb);
        sb.insert(8, "Java");
        System.out.println(sb);
        sb.delete(5,8);
        System.out.println(sb);
//      java 数组的使用
//        建立一个二维数组
        String[][] s = new String[2][];
        s[0] = new String[2];//
        s[1] = new String[3];
//      上述设置内容等价于
//      s[0] -> ["null", "null"]  (一个包含2个null的数组)
//      s[1] -> ["null", "null", "null"]  (一个包含3个null的数组)
        s[0][0] = new String("Good");
        s[0][1] = new String("Luck");
        s[1][0] = new String("to");
        s[1][1] = new String("you");
        s[1][2] = new String("!");
        System.out.println("第一行第二列的元素: " + s[0][1]); // 输出 Luck
        // 1. 声明并初始化数组
        int[] numbers = {5, 3, 9, 1, 7};

        // 2. 数组排序
        Arrays.sort(numbers);
        System.out.println("排序后的数组: " + Arrays.toString(numbers));
        // 3. 数组拷贝
        int[] copy = Arrays.copyOf(numbers, numbers.length);
        System.out.println("拷贝的数组: " + Arrays.toString(copy));

        // 4. 数组查找
        int index = Arrays.binarySearch(numbers, 7);
        System.out.println("元素 7 的索引: " + index);

        // 5. 数组填充
        int[] filledArray = new int[5];
        Arrays.fill(filledArray, 10);
        System.out.println("填充后的数组: " + Arrays.toString(filledArray));


//        日期对象类的使用
        // 初始化 Date 对象
        Date date = new Date();

        // 使用 toString() 函数显示日期时间
        System.out.println(date.toString());
//      自定义日期格式
        SimpleDateFormat ft = new SimpleDateFormat ("yyyy-MM-dd hh:mm:ss");

        System.out.println("当前时间为: " + ft.format(date));


//      正则表达式的引用
        String content = "I am noob " +
                "from runoob.com.";

        String pattern = ".*runoob.*";

        boolean isMatch = Pattern.matches(pattern, content);
        System.out.println("字符串中是否包含了 'runoob' 子字符串? " + isMatch);

//      Scanner类的使用

//        Scanner scanner = new Scanner(System.in);
//
//        System.out.print("请输入一个整数: ");
//        int number = scanner.nextInt();
//
//        System.out.println("你输入的整数是: " + number);
//
//        scanner.close();


//        Scanner scanner = new Scanner(System.in);
//
//        System.out.print("请输入你的名字: ");
//        String theName = scanner.nextLine();
//
//        System.out.println("你好, " + theName + "!");
//
//        scanner.close();

        Scanner scanner = new Scanner(System.in);
        System.out.print("请输入你的年龄: ");
        int theAge = scanner.nextInt();

        System.out.print("请输入你的名字: ");
        scanner.nextLine(); // 消耗掉nextInt后的换行符
        String theName = scanner.nextLine();

        System.out.println("你的名字是 " + theName + ", 年龄是 " + theAge + " 岁.");

        scanner.close();
    }
}

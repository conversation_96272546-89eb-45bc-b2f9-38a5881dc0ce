package com.js.hszpt.dto;

import lombok.Data;

import java.util.List;

/**
 * 内网查询
 */
@Data
public class IntranetQueryQueryDto {
    private String id;
    // 事项名称
    private String affairName;
    // 申请编号
    private String applyNo;
    // 申请人/申请单位
    private String applicantName;
    // 申请日期
    private String applyDateStart;
    private String applyDateEnd;
    // 受理日期
    private String acceptDateStart;
    private String acceptDateEnd;
    // 审批日期
    private String apprDateStart;
    private String apprDateEnd;
    // 船舶名称 中英文皆可
    private String shipName;
    // 持证人姓名
    private String holderName;
    // 持有人身份标识号码
    private String holderIdentityNumber;
    // 签发机关
    private List<String> issuOrgCode3;

    // 当前登录用户机构，对接智慧海事
    private String onlineDepOrg;
    // 智慧海事token
    private String zhhsCode;

    // 证书名称
    private String certificateTypeCode;
    // 证书名称
    private String certificateName;
    // 证书类型
    private String certificateType;
    // 证书编号
    private String certificateNum;
    // 签发日期
    private String issueDateStart;
    private String issueDateEnd;
    // 证照生效日期
    private String effectDateStart;
    private String effectDateEnd;
    // 截止日期
    private String expireDateStart;
    private String expireDateEnd;
    // 证书状态 1-有效；2-无效
    private String statusFlag;
    // 申请来源
    private String applyType;
    // 受理机构
    private List<String> acceptOrgCode3;
    // 审批机构
    private List<String> apprOrgCode3;
    // IMO船舶识别号
    private String shipImo;
    // 船舶编号或呼号
    private String shipCallSign;
    // clientId
    private String clientId;
    
    // 证书印刷号
    private String certPrintNo;
}

package com.js.hszpt.api;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.annotation.SystemLog;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.enums.LogType;
import com.js.hszpt.entity.SysDeptIamb;
import com.js.hszpt.service.SysDeptIambService;
import com.js.hszpt.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
*
* @ClassName: SysDeptIambApi
* @Description:TODO(组织信息表接口)
* @author:  System Generation
*
*/
@Slf4j
@RestController
@Api(description = "组织信息表接口")
@RequestMapping("/sysDeptIamb")
public class SysDeptApi extends BaseApiPlus<SysDeptIambService,SysDeptIamb,String>{

   @Autowired
   private SysUserService sysUserService;

   @SystemLog(description = "组织信息表-分页查询", type = LogType.OPERATION)
   @RequestMapping(value = "/getPage", method = RequestMethod.GET)
   @ApiOperation(value = "分页查询")
   public Result<Page<SysDeptIamb>> getPage(@ModelAttribute SysDeptIamb param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
       Page<SysDeptIamb> page = this.baseService.findByCondition(param, searchVo, pageVo);
       return ResultUtil.data(page);
   }

   @SystemLog(description = "组织信息表-列表查询", type = LogType.OPERATION)
   @RequestMapping(value = "/getList", method = RequestMethod.GET)
   @ApiOperation(value = "列表查询")
   public Result<List<SysDeptIamb>> getList(@ModelAttribute SysDeptIamb param, @ModelAttribute SearchVo searchVo) {
       List<SysDeptIamb> list = this.baseService.findByCondition(param, searchVo);
       return ResultUtil.data(list);
   }

   @SystemLog(description = "获取机构统一社会信用代码", type = LogType.OPERATION)
   @RequestMapping(value = "/getUscc", method = RequestMethod.GET)
   @ApiOperation(value = "获取机构统一社会信用代码")
   public Result<String> getUscc() {
       // 获取用户机构编码
       String orgCode = sysUserService.getCurrUserOrgCode();

       if(StrUtil.isBlank(orgCode)){
           ResultUtil.error("获取用户机构信息失败.");
       }

       // 查询统一社会信用代码
       String uscc = this.baseService.getUsccByOrgCode(orgCode);

       return ResultUtil.data(uscc);
   }
}

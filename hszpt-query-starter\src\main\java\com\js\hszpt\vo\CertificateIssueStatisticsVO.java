package com.js.hszpt.vo;

import com.js.core.common.vo.PageVo;
import lombok.Data;

import java.util.List;

/**
 * 证照签发统计请求参数VO
 */
@Data
public class CertificateIssueStatisticsVO extends PageVo {

    /**
     * 签发机构编码列表（必填）
     */
    private List<String> issuerCodes;

    /**
     * 机构类型，1-海事机构 2-海事外机构
     */
    private String orgType;

    /**
     * 证照类型代码列表（可选）
     */
    private List<String> certificateTypeCodes;

    /**
     * 持证主体类别代码列表（可选）
     */
    private List<String> holderCategoryCodes;

    /**
     * 事项性质代码列表（可选）
     */
    private List<String> matterNatureCodes;

    /**
     * 申请来源代码（可选）
     */
    private String applicationSourceCode;

    /**
     * 证照状态代码（可选）
     */
    private String statusCode;

    /**
     * 统计时间类型：year-年, quarter-季, month-月, day-日
     */
    private String timeType = "month";

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 关联事项名称（可选）
     */
    private String relatedMatterName;

    /**
     * 证照ID列表（可选）
     */
    private List<String> certificateIds;

    /**
     * 登录人所在机构编码（必填）
     */
    private String loginUserOrgCode;
} 
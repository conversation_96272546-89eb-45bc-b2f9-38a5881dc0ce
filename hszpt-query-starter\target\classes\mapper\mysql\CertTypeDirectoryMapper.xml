<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.hszpt.mapper.CertTypeDirectoryDao">

    <resultMap id="CertTypeDirectoryDetailMap" type="com.js.hszpt.vo.CertTypeDirectoryDetailVO">
        <association property="certTypeDirectory" javaType="com.js.hszpt.vo.CertTypeDirectoryVO">
            <id column="ctd_cert_type_dir_id" property="id"/>
            <result column="ctd_certificate_type_code" property="certificateTypeCode"/>
            <result column="ctd_certificate_type_name" property="certificateTypeName"/>
            <result column="ctd_create_org_name" property="createOrgName"/>
            <result column="ctd_related_item_name" property="relatedItemName"/>
            <result column="ctd_related_item_code" property="relatedItemCode"/>
            <result column="ctd_certificate_holder_category" property="certificateHolderCategory"/>
            <result column="ctd_certificate_holder_category_name" property="certificateHolderCategoryName"/>
            <result column="ctd_validity_range" property="validityRange"/>
            <result column="ctd_approval_status" property="approvalStatus"/>
            <result column="ctd_approval_status_name" property="approvalStatusName"/>
            <result column="ctd_issue_status" property="issueStatus"/>
            <result column="ctd_issue_status_name" property="issueStatusName"/>
            <result column="ctd_issue_date" property="issueDate"/>
            <result column="ctd_create_oper_id" property="createBy"/>
            <result column="ctd_create_time" property="createTime"/>
            <result column="ctd_modify_oper_id" property="updateBy"/>
            <result column="ctd_modify_time" property="updateTime"/>
            <result column="ctd_del_flag" property="delFlag"/>
            <result column="parent_id" property="parentId"/>
        </association>
        <association property="certApprovalApply" javaType="com.js.hszpt.vo.CertApprovalApplyVO">
            <id column="caa_cert_type_approval_apply_id" property="id"/>
            <result column="caa_apply_no" property="applyNo"/>
            <result column="caa_cert_type_dir_id" property="certTypeDirId"/>
            <result column="caa_execution_id" property="executionId"/>
            <result column="caa_apply_type" property="applyType"/>
            <result column="caa_apply_org_code" property="applyOrgCode"/>
            <result column="caa_apply_org_name" property="applyOrgName"/>
            <result column="caa_accept_org_code" property="acceptOrgCode"/>
            <result column="caa_accept_org_name" property="acceptOrgName"/>
            <result column="caa_approval_org_code" property="approvalOrgCode"/>
            <result column="caa_approval_org_name" property="approvalOrgName"/>
            <result column="caa_publish_org_code" property="publishOrgCode"/>
            <result column="caa_publish_org_name" property="publishOrgName"/>
            <result column="caa_applicant_id" property="applicantId"/>
            <result column="caa_applicant_name" property="applicantName"/>
            <result column="caa_apply_reason" property="applyReason"/>
            <result column="caa_apply_time" property="applyTime"/>
            <result column="caa_accept_time" property="acceptTime"/>
            <result column="caa_approval_time" property="approvalTime"/>
            <result column="caa_publish_time" property="publishTime"/>
            <result column="caa_current_node" property="currentNode"/>
            <result column="caa_current_handler_id" property="currentHandlerId"/>
            <result column="caa_current_handler_name" property="currentHandlerName"/>
            <result column="caa_status" property="status"/>
            <result column="caa_del_flag" property="delFlag"/>
        </association>
        <collection property="certTypeApprovals" ofType="com.js.hszpt.vo.CertTypeApprovalVO">
            <id column="cta_cert_type_approval_id" property="id"/>
            <result column="cta_cert_type_dir_id" property="certTypeDirId"/>
            <result column="cta_cert_type_approval_apply_id" property="certApprApplyId"/>
            <result column="cta_applicant_id" property="applicantId"/>
            <result column="cta_applicant_name" property="applicantName"/>
            <result column="cta_apply_time" property="applyTime"/>
            <result column="cta_current_node" property="currentNode"/>
            <result column="cta_approval_type" property="applyType"/>
            <result column="cta_approval_result" property="approvalResult"/>
            <result column="cta_approval_opinion" property="approvalOpinion"/>
            <result column="cta_complete_time" property="completeTime"/>
            <result column="cta_del_flag" property="delFlag"/>
        </collection>
    </resultMap>

    <select id="queryCertTypeDirectoryList" resultType="com.js.hszpt.vo.CertTypeDirectoryListVO">
        SELECT
            caa.publish_org_name as publishOrgName,
            caa.APPLY_NO as applyNo,
            caa.APPLY_TYPE as applyType,
            (select title from t_dict_data
                where dict_id =
                      (select t_dict.id from t_dict where type = 'CERT_APPLY_TYPE')
                  and value = caa.APPLY_TYPE) as apply_type_name,
            caa.publish_org_name,
            ctd.certificate_type_name,
            ctd.certificate_type_code,
            ctd.create_org_name,
            ctd.related_item_name,
            ctd.related_item_code,
            ctd.certificate_holder_category,
            (select title from t_dict_data
                where dict_id =
                      (select t_dict.id from t_dict where type = 'CERTIFICATE_HOLDER_CATEGORY')
                  and value = ctd.certificate_holder_category) as certificate_holder_category_name,
            ctd.validity_range,
            ctd.approval_status,
            (select title from t_dict_data
                where dict_id =
                      (select t_dict.id from t_dict where type = 'approvalStatus')
                  and value = ctd.approval_status) as approval_status_name,
            ctd.issue_status,
            (select title from t_dict_data
                where dict_id =
                      (select t_dict.id from t_dict where type = 'ISSUE_STATUS')
                  and value = ctd.issue_status) as issue_status_name,
            ctd.issue_date,
            caa.accept_org_name,
            caa.approval_org_name,
            ctd.CREATE_BY as createBy,
            ctd.CREATE_TIME as createTime,
            ctd.UPDATE_BY as updateBy,
            ctd.UPDATE_TIME as updateTime,
            caa.current_node,
            caa.status,
            ctd.cert_type_dir_id as certTypeDirId,
            caa.cert_type_appr_apply_id as certApprApplyId,
            cta.cert_type_appr_id as certTypeApprovalId
        FROM cert_type_directory ctd
        LEFT JOIN cert_type_approval cta ON ctd.cert_type_dir_id = cta.cert_type_dir_id
        LEFT JOIN cert_type_approval_apply caa ON ctd.cert_type_dir_id = caa.cert_type_dir_id
        <where>
            ctd.del_flag = '0'
            and caa.del_flag = '0'

            <!-- 证照类型ID列表 -->
            <if test="query.certTypeId != null and query.certTypeId.size() > 0">
                AND ctd.certificate_type_name IN
                <foreach collection="query.certTypeId" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 证照类型代码 -->
            <if test="query.certTypeCode != null and query.certTypeCode != ''">
                AND ctd.certificate_type_code LIKE CONCAT('%', #{query.certTypeCode}, '%')
            </if>

            <!-- 证照名称列表 -->
            <if test="query.certName != null and query.certName.size() > 0">
                AND ctd.certificate_type_name IN
                <foreach collection="query.certName" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>

            <!-- 证照定义机构列表 -->
            <if test="query.orgName != null and query.orgName.size() > 0">
                AND ctd.certificate_define_authority_name IN
                <foreach collection="query.orgName" item="org" open="(" separator="," close=")">
                    #{org}
                </foreach>
            </if>

            <!-- 关联事项名称 -->
            <if test="query.affairName != null and query.affairName != ''">
                AND ctd.related_item_name LIKE CONCAT('%', #{query.affairName}, '%')
            </if>

            <!-- 关联事项代码 -->
            <if test="query.affairNo != null and query.affairNo != ''">
                AND ctd.related_item_code LIKE CONCAT('%', #{query.affairNo}, '%')
            </if>

            <!-- 持证主体类别 -->
            <if test="query.certificateHolderCategory != null and query.certificateHolderCategory != ''">
                AND ctd.certificate_holder_category = #{query.certificateHolderCategory}
            </if>

            <!-- 有效期限范围 -->
            <if test="query.validPeriod != null and query.validPeriod != ''">
                AND ctd.validity_range = #{query.validPeriod}
            </if>

            <!-- 审批状态 -->
            <if test="query.approvalStatus != null and query.approvalStatus != ''">
                AND ctd.approval_status = #{query.approvalStatus}
            </if>

            <!-- 下发状态 -->
            <if test="query.issueStatus != null and query.issueStatus != ''">
                AND ctd.issue_status = #{query.issueStatus}
            </if>

            <!-- 下发日期范围 -->
            <if test="query.issueDateStart != null and query.issueDateEnd != null">
                AND ctd.issue_date BETWEEN #{query.issueDateStart} AND #{query.issueDateEnd}
            </if>

            <!-- 创建日期范围 -->
            <if test="query.createTimeStart != null and query.createTimeEnd != null">
                AND ctd.create_time BETWEEN #{query.createTimeStart} AND #{query.createTimeEnd}
            </if>
        </where>
        ORDER BY ctd.create_time DESC
    </select>

    <select id="getCertTypeDirInfo" resultMap="CertTypeDirectoryDetailMap">
            SELECT
            -- 证照类型目录表(cert_type_directory)全字段
            ctd.cert_type_dir_id as ctd_cert_type_dir_id,
            ctd.certificate_type_code as ctd_certificate_type_code,
            ctd.certificate_type_name as ctd_certificate_type_name,
            ctd.create_org_name as ctd_create_org_name,
            ctd.related_item_name as ctd_related_item_name,
            ctd.related_item_code as ctd_related_item_code,
            ctd.parent_id as ctd_parent_id,
            ctd.certificate_holder_category as ctd_certificate_holder_category,
            (select title from t_dict_data
                where dict_id =
                      (select t_dict.id from t_dict where type = 'CERTIFICATE_HOLDER_CATEGORY')
                  and value = ctd.certificate_holder_category) as ctd_certificate_holder_category_name,
            ctd.validity_range as ctd_validity_range,
            ctd.approval_status as ctd_approval_status,
            (select title from t_dict_data
                where dict_id =
                      (select t_dict.id from t_dict where type = 'approvalStatus')
                  and value = ctd.approval_status) as ctd_approval_status_name,
            ctd.issue_status as ctd_issue_status,
            (select title from t_dict_data
                where dict_id =
                      (select t_dict.id from t_dict where type = 'ISSUE_STATUS')
                  and value = ctd.issue_status) as ctd_issue_status_name,
            ctd.issue_date as ctd_issue_date,
            ctd.CREATE_BY as ctd_create_oper_id,
            ctd.create_time as ctd_create_time,
            ctd.UPDATE_BY as ctd_modify_oper_id,
            ctd.UPDATE_TIME as ctd_modify_time,
            ctd.del_flag as ctd_del_flag,

            -- 审批申请表(cert_type_approval_apply)全字段
            caa.cert_type_appr_apply_id as caa_cert_type_approval_apply_id,
            caa.apply_no as caa_apply_no,
            caa.cert_type_dir_id as caa_cert_type_dir_id,
            caa.execution_id as caa_execution_id,
            caa.apply_type as caa_apply_type,
            caa.apply_org_code as caa_apply_org_code,
            caa.apply_org_name as caa_apply_org_name,
            caa.accept_org_code as caa_accept_org_code,
            caa.accept_org_name as caa_accept_org_name,
            caa.approval_org_code as caa_approval_org_code,
            caa.approval_org_name as caa_approval_org_name,
            caa.publish_org_code as caa_publish_org_code,
            caa.publish_org_name as caa_publish_org_name,
            caa.applicant_id as caa_applicant_id,
            caa.applicant_name as caa_applicant_name,
            caa.apply_reason as caa_apply_reason,
            caa.apply_time as caa_apply_time,
            caa.accept_time as caa_accept_time,
            caa.approval_time as caa_approval_time,
            caa.publish_time as caa_publish_time,
            caa.current_node as caa_current_node,
            caa.current_handler_id as caa_current_handler_id,
            caa.current_handler_name as caa_current_handler_name,
            caa.status as caa_status,
            caa.del_flag as caa_del_flag,
            caa.create_by as caa_create_oper_id,
            caa.create_time as caa_create_time,
            caa.UPDATE_BY as caa_modify_oper_id,
            caa.update_time as caa_modify_time,

            -- 审核审批表(cert_type_approval)全字段
            cta.cert_type_appr_id as cta_cert_type_approval_id,
            cta.cert_type_dir_id as cta_cert_type_dir_id,
            cta.cert_type_appr_apply_id as cta_cert_type_approval_apply_id,
            cta.applicant_id as cta_applicant_id,
            cta.applicant_name as cta_applicant_name,
            cta.apply_time as cta_apply_time,
            cta.current_node as cta_current_node,
            cta.approval_result as cta_approval_result,
            cta.approval_opinion as cta_approval_opinion,
            cta.complete_time as cta_complete_time,
            cta.del_flag as cta_del_flag,
            cta.create_by as cta_create_oper_id,
            cta.create_time as cta_create_time,
            cta.UPDATE_BY as cta_modify_oper_id,
            cta.update_time as cta_modify_time
        FROM cert_type_directory ctd
        LEFT JOIN cert_type_approval_apply caa ON caa.cert_type_dir_id = ctd.cert_type_dir_id
            AND caa.del_flag = '0'
        LEFT JOIN cert_type_approval cta ON cta.cert_type_dir_id = ctd.cert_type_dir_id
            AND cta.del_flag = '0'
        WHERE ctd.cert_type_dir_id = #{certTypeDirId}
        AND ctd.del_flag = '0'
        ORDER BY caa.create_time DESC, cta.create_time DESC
    </select>

</mapper>

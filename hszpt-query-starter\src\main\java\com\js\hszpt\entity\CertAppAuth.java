package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.js.core.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 
 * @ClassName:  CertAppAuth   
 * @Description:TODO(APPID授权信息表)   
 * @author:   System Generation 
 */
@Data

@TableName("cert_app_auth")
@ApiModel(value = "APPID授权信息表")
public class CertAppAuth extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /**
    * appID
    */
    @ApiModelProperty(value = "appID")
    private String clientId;


    /**
    * 持有人姓名
    */
    @ApiModelProperty(value = "持有人姓名")
    private String holderName;


    /**
    * 持有人身份标识号码，例如身份证号、统一社会信用代码
    */
    @ApiModelProperty(value = "持有人身份标识号码，例如身份证号、统一社会信用代码")
    private String holderIdentityNumber;


}
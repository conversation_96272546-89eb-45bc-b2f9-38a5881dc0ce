package com.js.hszpt.api;


import java.util.List;

import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.js.annotation.SystemLog;
import com.js.core.api.BaseApiPlus;
import com.js.core.common.utils.ResultUtil;
import com.js.core.common.vo.PageVo;
import com.js.core.common.vo.Result;
import com.js.core.common.vo.SearchVo;
import com.js.enums.LogType;
import com.js.hszpt.entity.Demo;
import com.js.hszpt.service.DemoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


 /**
 * 
 * @ClassName: DemoApi  
 * @Description:TODO(示例表接口)   
 * @author:  System Generation  
 *
 */
@Slf4j
@RestController
@Api(description = "示例表接口")
@RequestMapping("/demo")
public class DemoApi extends BaseApiPlus<DemoService,Demo,String>{

	@SystemLog(description = "示例表-分页查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getPage", method = RequestMethod.GET)
	@ApiOperation(value = "分页查询")
	public Result<Page<Demo>> getPage(@ModelAttribute Demo param, @ModelAttribute SearchVo searchVo, @ModelAttribute PageVo pageVo) {
		Page<Demo> page = this.baseService.findByCondition(param, searchVo, pageVo);
		return ResultUtil.data(page);
	}
	
	@SystemLog(description = "示例表-列表查询", type = LogType.OPERATION)
	@RequestMapping(value = "/getList", method = RequestMethod.GET)
	@ApiOperation(value = "列表查询")
	public Result<List<Demo>> getList(@ModelAttribute Demo param, @ModelAttribute SearchVo searchVo) {
		List<Demo> list = this.baseService.findByCondition(param, searchVo);
		return ResultUtil.data(list);
	}
	
}

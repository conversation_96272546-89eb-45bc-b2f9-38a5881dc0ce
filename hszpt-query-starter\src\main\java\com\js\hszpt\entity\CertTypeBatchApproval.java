package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CertTypeBatchApproval   
 * @Description:TODO(批量审批日志表)   
 * @author:   System Generation 
 */
@Data

@TableName("CERT_TYPE_BATCH_APPROVAL")
@ApiModel(value = "批量审批日志表")
public class CertTypeBatchApproval extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 批量审批记录主键ID
     */
    @TableId(value = "CERT_TYPE_BATCH_APPR_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "批量审批记录主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());

    /**
    * 批量类型：审核、审批
    */
    @ApiModelProperty(value = "批量类型：审核、审批")
    private String batchType;


    /**
    * 审批结果：1-通过 2-不通过 3-退回
    */
    @ApiModelProperty(value = "审批结果：1-通过 2-不通过 3-退回")
    private String approvalResult;


    /**
    * 批量审批意见
    */
    @ApiModelProperty(value = "批量审批意见")
    private String approvalOpinion;


    /**
    * 批量处理人ID
    */
    @ApiModelProperty(value = "批量处理人ID")
    private String handlerId;


    /**
    * 批量处理人姓名
    */
    @ApiModelProperty(value = "批量处理人姓名")
    private String handlerName;


    /**
    * 批量处理时间
    */
    @ApiModelProperty(value = "批量处理时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;


    /**
    * 批量处理的记录数
    */
    @ApiModelProperty(value = "批量处理的记录数")
    private Integer recordCount;


    /**
    * 处理成功的记录数
    */
    @ApiModelProperty(value = "处理成功的记录数")
    private Integer successCount;


    /**
    * 处理失败的记录数
    */
    @ApiModelProperty(value = "处理失败的记录数")
    private Integer failCount;


}
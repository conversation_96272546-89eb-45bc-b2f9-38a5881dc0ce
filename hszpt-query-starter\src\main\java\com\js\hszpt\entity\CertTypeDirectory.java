package com.js.hszpt.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.js.core.entity.BaseEntity;
import com.js.util.SnowFlakeUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 
 * @ClassName:  CertTypeDirectory   
 * @Description:TODO(证照类型目录表)   
 * @author:   System Generation 
 */
@Data
@TableName("dwdz_ctf_cert_type_directory")
@ApiModel(value = "证照类型目录表")
public class CertTypeDirectory extends BaseEntity{

    private static final long serialVersionUID = 1L;


    /**
    * 证照类型目录主键ID
    */
    @TableId(value = "CERT_TYPE_DIR_ID", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "证照类型目录主键ID")
    private String id = String.valueOf(SnowFlakeUtil.nextId());


    /**
    * 证照类型代码，21位编码
    */
    @TableField("CERTIFICATE_TYPE_CODE")
    @ApiModelProperty(value = "证照类型代码，21位编码")
    private String certificateTypeCode;


    /**
    * 证照类型名称，如建筑工程施工许可证
    */
    @ApiModelProperty(value = "证照类型名称，如建筑工程施工许可证")
    private String certificateTypeName;

    /**
     * 父级证照类型ID
     */
    @ApiModelProperty(value = "父级证照类型ID")
    private String parentId;

    /**
     * 层级级别
     */
    @ApiModelProperty(value = "层级级别")
    private Integer certLevel;


    /**
     * 同级排序
     */
    @ApiModelProperty(value = "同级排序")
    private Integer sort;

    /**
    * 证照定义机构全称，如中华人民共和国住房和城乡建设部
    */
    @ApiModelProperty(value = "证照定义机构全称，如中华人民共和国住房和城乡建设部")
    private String certificateDefineAuthorityName;


    /**
    * 定义机构统一社会信用代码，18位，符合GB32100
    */
    @ApiModelProperty(value = "定义机构统一社会信用代码，18位，符合GB32100")
    private String certificateDefineAuthorityCode;


    /**
    * 机构级别：国家级/省部级/地市级/区县级/其他
    */
    @ApiModelProperty(value = "机构级别：国家级/省部级/地市级/区县级/其他")
    private String certificateDefineAuthorityLevel;


    /**
    * 关联政务服务事项名称
    */
    @ApiModelProperty(value = "关联政务服务事项名称")
    private String relatedItemName;


    /**
    * 关联事项编码，12位政务服务事项代码
    */
    @ApiModelProperty(value = "关联事项编码，12位政务服务事项代码")
    private String relatedItemCode;


    /**
    * 持证主体类别：自然人/法人或其他组织/混合/其他
    */
    @ApiModelProperty(value = "持证主体类别：自然人/法人或其他组织/混合/其他")
    private String certificateHolderCategory;


    /**
    * 有效期限范围，用^分隔不同期限
    */
    @ApiModelProperty(value = "有效期限范围，用^分隔不同期限")
    private String validityRange;


    /**
    * 颁发机构最低级别：国家级/省级/地市级/区县级/乡镇级/其他
    */
    @ApiModelProperty(value = "颁发机构最低级别：国家级/省级/地市级/区县级/乡镇级/其他")
    private String certificateIssuingAuthorityLevel;


    /**
    * 创建机构编码
    */
    @ApiModelProperty(value = "创建机构编码")
    private String createOrgCode;


    /**
     * 创建机构名称
     */
    @ApiModelProperty(value = "创建机构名称")
    private String createOrgName;

    /**
    * 审批状态，1-草稿（待提交） 2-已审核 3-已审批
    */
    @ApiModelProperty(value = "审批状态，1-草稿（待提交） 2-已审核 3-已审批")
    private String approvalStatus;


    /**
    * 下发状态，1-未下发 2-已下发 3-已废止
    */
    @TableField("issue_status")
    @ApiModelProperty(value = "下发状态，1-未下发 2-已下发 3-已废止")
    private String issueStatus;


    /**
    * 下发时间
    */
    @TableField(value = "ISSUE_DATE", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "下发时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueDate;

    /**
    * 删除标志：0-正常，1-已删除
    */
    @ApiModelProperty(value = "删除标志：0-正常，1-已删除")
    private String delFlag;

}
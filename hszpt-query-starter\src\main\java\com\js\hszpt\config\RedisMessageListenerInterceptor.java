package com.js.hszpt.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

/**
 * Redis 消息监听器容器拦截器
 * 用于禁用所有 Redis 消息监听器容器
 */
@Component
public class RedisMessageListenerInterceptor implements BeanPostProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(RedisMessageListenerInterceptor.class);
    
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }
    
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // 拦截所有 RedisMessageListenerContainer 类型的 bean
        if (bean instanceof RedisMessageListenerContainer) {
            log.info("禁用 Redis 消息监听器容器: {}", beanName);
            // 禁用容器
            RedisMessageListenerContainer container = (RedisMessageListenerContainer) bean;
            
            // 提供一个不执行任何操作的执行器
            Executor noOpExecutor = command -> {
                log.debug("拦截到 Redis 消息监听器任务，不执行");
            };
            
            container.setTaskExecutor(noOpExecutor);
            container.setSubscriptionExecutor(noOpExecutor);
            
            // 移除所有监听器
            container.removeMessageListener(null);
        }
        return bean;
    }
} 